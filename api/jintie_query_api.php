<?php
/**
 * 一站津贴查询专用API（简化版）
 * 提供严格的权限控制，只允许一站人员和管理员查看津贴数据
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    // 如果不是通过统一API调用，则启动会话并设置响应头
    // 设置错误报告
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    // 设置响应头
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    // 处理预检请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit(0);
    }

    // 引入配置文件
    require_once '../includes/config.php';
} else {
    // 通过统一API调用，不需要重新设置头部，但需要确保配置已加载
    if (!defined('DB_HOST')) {
        require_once '../includes/config.php';
    }
}

// 获取输入数据
// 如果是通过统一API调用，优先使用$_POST；否则尝试解析JSON输入
if (!empty($_POST['action'])) {
    // 通过统一API调用
    $input = $_POST;
} else {
    // 直接调用
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
}



// 验证action参数
if (!isset($input['action'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少action参数']);
    exit;
}

// 根据action执行相应操作
switch ($input['action']) {
    case 'get_latest_jintie_data':
        getLatestJintieData($input);
        break;
    case 'get_jintie_history':
        getJintieHistory($input);
        break;
    case 'get_personal_yearly_data':
        getPersonalYearlyData($input);
        break;
    default:
        echo json_encode(['status' => 'error', 'message' => '无效的操作']);
        break;
}

/**
 * 获取最新津贴数据
 */
function getLatestJintieData($input) {
    global $pdo;

    try {
        // 不再需要验证token，因为已经在统一API中验证过权限
        $username = $input['username'] ?? '';
        $viewAll = isset($input['view_all']) ? (bool)$input['view_all'] : false;
        $date = $input['date'] ?? null;

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if ($date) {
            $whereConditions[] = "DATE(record_date) = ?";
            $params[] = $date;
        }

        // 如果不是查看全部，则只查看当前用户的数据
        if (!$viewAll && $username) {
            $whereConditions[] = "name = ?";
            $params[] = $username;
        }

        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        // 获取最新的记录日期
        $latestDateQuery = "SELECT MAX(record_date) as latest_date FROM jintie_records";
        if ($date) {
            $latestDateQuery = "SELECT ? as latest_date";
            $latestDateStmt = $pdo->prepare($latestDateQuery);
            $latestDateStmt->execute([$date]);
        } else {
            $latestDateStmt = $pdo->prepare($latestDateQuery);
            $latestDateStmt->execute();
        }
        $latestDateResult = $latestDateStmt->fetch(PDO::FETCH_ASSOC);
        $latestDate = $latestDateResult['latest_date'];

        if (!$latestDate) {
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'records' => [],
                    'record_date' => null,
                    'month_year' => null
                ]
            ]);
            return;
        }

        // 查询该日期的津贴数据
        $query = "SELECT name, total_amount, record_date, month_year 
                  FROM jintie_records 
                  WHERE DATE(record_date) = DATE(?)";
        
        if (!$viewAll && $username) {
            $query .= " AND name = ?";
            $params = [$latestDate, $username];
        } else {
            $params = [$latestDate];
        }
        
        $query .= " ORDER BY name";

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化数据
        $formattedRecords = [];
        $monthYear = '';
        foreach ($records as $record) {
            $formattedRecords[] = [
                'name' => $record['name'],
                'total_amount' => $record['total_amount']
            ];
            if (!$monthYear) {
                $monthYear = $record['month_year'];
            }
        }

        echo json_encode([
            'status' => 'success',
            'data' => [
                'records' => $formattedRecords,
                'record_date' => $latestDate,
                'month_year' => $monthYear
            ]
        ]);

    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '获取津贴数据失败']);
    }
}

/**
 * 获取津贴历史记录
 */
function getJintieHistory($input) {
    global $pdo;

    try {
        // 不再需要验证token，因为已经在统一API中验证过权限
        $year = $input['year'] ?? date('Y');

        // 查询指定年份的历史记录
        $query = "SELECT DISTINCT DATE(record_date) as date, month_year 
                  FROM jintie_records 
                  WHERE YEAR(record_date) = ? 
                  ORDER BY record_date DESC";

        $stmt = $pdo->prepare($query);
        $stmt->execute([$year]);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'status' => 'success',
            'data' => [
                'records' => $records,
                'year' => $year
            ]
        ]);

    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '获取历史记录失败']);
    }
}

/**
 * 获取个人年度津贴数据
 */
function getPersonalYearlyData($input) {
    global $pdo;

    try {
        // 不再需要验证token，因为已经在统一API中验证过权限
        $personName = $input['person_name'] ?? '';
        $year = $input['year'] ?? date('Y');

        if (!$personName) {
            echo json_encode(['status' => 'error', 'message' => '缺少人员姓名']);
            return;
        }

        // 查询指定人员的年度数据
        $query = "SELECT name, total_amount, record_date, month_year, remark
                  FROM jintie_records
                  WHERE name = ? AND YEAR(record_date) = ?
                  ORDER BY record_date DESC";

        $stmt = $pdo->prepare($query);
        $stmt->execute([$personName, $year]);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化数据
        $formattedRecords = [];
        foreach ($records as $record) {
            $formattedRecords[] = [
                'name' => $record['name'],
                'amount' => $record['total_amount'],
                'record_date' => $record['record_date'],
                'month_year' => $record['month_year'],
                'remark' => $record['remark'] ?? '-'
            ];
        }

        echo json_encode([
            'status' => 'success',
            'data' => [
                'records' => $formattedRecords,
                'person_name' => $personName,
                'year' => $year
            ]
        ]);

    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '获取个人年度数据失败']);
    }
}

?>
