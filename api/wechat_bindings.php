<?php
/**
 * 微信绑定管理API
 * 提供微信账号绑定关系的管理功能
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    // 如果不是通过统一API调用，则启动会话并设置响应头
    session_start();
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    // 处理OPTIONS预检请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
    
    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => '权限不足，只有管理员可以访问'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// 引入配置文件
require_once __DIR__ . '/../includes/config.php';

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    switch ($action) {
        case 'list':
            // 获取绑定列表
            $page = intval($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            $itemsPerPage = 20;
            $offset = ($page - 1) * $itemsPerPage;

            // 构建查询条件
            $whereClause = '';
            $params = [];
            
            if (!empty($search)) {
                $whereClause = "WHERE (u.username LIKE ? OR u.real_name LIKE ? OR wu.nickname LIKE ?)";
                $searchTerm = "%$search%";
                $params = [$searchTerm, $searchTerm, $searchTerm];
            }

            // 获取绑定列表
            $sql = "
                SELECT 
                    uwb.id as binding_id,
                    uwb.bind_type,
                    uwb.bind_time,
                    uwb.bind_ip,
                    uwb.is_active,
                    u.id as user_id,
                    u.username,
                    u.real_name,
                    u.role,
                    u.is_station_staff,
                    wu.id as wechat_user_id,
                    wu.openid,
                    wu.nickname,
                    wu.avatar_url,
                    wu.city,
                    wu.province
                FROM user_wechat_bindings uwb
                JOIN users u ON uwb.user_id = u.id
                JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
                $whereClause
                ORDER BY uwb.bind_time DESC
                LIMIT $itemsPerPage OFFSET $offset
            ";

            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $bindings = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 获取总数
            $countSql = "
                SELECT COUNT(*) as total
                FROM user_wechat_bindings uwb
                JOIN users u ON uwb.user_id = u.id
                JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
                $whereClause
            ";
            $countStmt = $pdo->prepare($countSql);
            $countStmt->execute($params);
            $totalItems = $countStmt->fetchColumn();

            // 格式化数据
            foreach ($bindings as &$binding) {
                $binding['display_name'] = $binding['real_name'] ?: $binding['username'];
                $binding['role_text'] = $binding['role'] === 'admin' ? '管理员' : 
                                       ($binding['role'] === 'manager' ? '管理者' : '普通用户');
                $binding['bind_type_text'] = $binding['bind_type'] === 'manual' ? '手动绑定' : '自动绑定';
                $binding['status_text'] = $binding['is_active'] ? '激活' : '禁用';
            }

            // 获取统计信息
            $statsStmt = $pdo->query("
                SELECT 
                    (SELECT COUNT(*) FROM user_wechat_bindings WHERE is_active = 1) as active_bindings,
                    (SELECT COUNT(*) FROM wechat_users) as total_wechat_users,
                    (SELECT COUNT(*) FROM wechat_tokens WHERE expires_at > NOW()) as active_tokens
            ");
            $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'data' => [
                    'bindings' => $bindings,
                    'pagination' => [
                        'current_page' => $page,
                        'total_items' => intval($totalItems),
                        'items_per_page' => $itemsPerPage,
                        'total_pages' => ceil($totalItems / $itemsPerPage)
                    ],
                    'stats' => $stats
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;

        case 'unbind':
            // 解绑微信账号
            $bindingId = $input['binding_id'] ?? '';
            
            if (empty($bindingId)) {
                throw new Exception('缺少绑定ID');
            }

            // 获取绑定信息
            $stmt = $pdo->prepare("SELECT user_id FROM user_wechat_bindings WHERE id = ?");
            $stmt->execute([$bindingId]);
            $binding = $stmt->fetch();

            if (!$binding) {
                throw new Exception('绑定关系不存在');
            }

            // 删除绑定关系
            $stmt = $pdo->prepare("DELETE FROM user_wechat_bindings WHERE id = ?");
            $stmt->execute([$bindingId]);

            // 清除该用户的所有Token
            $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ?");
            $stmt->execute([$binding['user_id']]);

            echo json_encode([
                'success' => true,
                'message' => '微信账号解绑成功'
            ], JSON_UNESCAPED_UNICODE);
            break;

        default:
            throw new Exception('无效的操作');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
