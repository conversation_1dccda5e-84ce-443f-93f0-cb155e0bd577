<?php
// 防止直接访问
if (!defined('API_ACCESS')) {
    // 如果不是通过统一API调用，则启动会话并设置响应头
    require '../includes/config.php';
    header('Content-Type: application/json');

    // 设置时区为北京时间
    date_default_timezone_set('Asia/Shanghai');

    // 允许跨域请求
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST');
    header('Access-Control-Allow-Headers: Content-Type');

    // 处理OPTIONS请求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit(0);
    }
} else {
    // 通过统一API调用，不需要重新设置头部，但需要确保配置已加载
    if (!defined('DB_HOST')) {
        require '../includes/config.php';
    }
}

// 获取请求参数
// 如果是通过统一API调用，优先使用$_POST；否则尝试解析JSON输入
if (!empty($_POST['action'])) {
    // 通过统一API调用
    $input = $_POST;
} else {
    // 直接调用
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
}

// 创建必要的表结构
createRequiredTables();



// 如果没有action参数，返回错误
if (!isset($input['action'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少action参数']);
    exit;
}

// 根据action参数执行相应的操作
switch ($input['action']) {
    case 'query_rcp':
        queryRcp($input);
        break;
    case 'query_junction':
        queryJunction($input);
        break;
    case 'query_tag':
        queryTag($input);
        break;
    case 'query_valve':
        queryValve($input);
        break;
    default:
        echo json_encode(['status' => 'error', 'message' => '无效的action参数: ' . ($input['action'] ?? 'null')]);
        break;
}

// 获取客户端IP地址
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 创建必要的数据表
function createRequiredTables() {
    // 不再需要创建登录相关的表，因为使用微信登录系统
}





// 查询RCP柜信息
function queryRcp($input) {
    global $pdo;

    if (!isset($input['name']) || empty($input['name'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少RCP柜名称']);
        exit;
    }

    // 不再需要验证token，因为用户已经通过页面级别的验证

    $name = trim($input['name']);
    
    try {
        // 查询所有匹配的RCP柜
        $stmt = $pdo->prepare("
            SELECT r.*, p.name AS power_name 
            FROM rcp_cabinet r
            JOIN power_cabinet p ON r.power_id = p.id
            WHERE r.name LIKE ?
            ORDER BY r.name ASC
        ");
        $stmt->execute(["%$name%"]);
        $rcps = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($rcps)) {
            echo json_encode(['status' => 'error', 'message' => '未找到匹配的RCP柜']);
            exit;
        }
        
        $results = [];
        
        // 处理每个RCP柜的数据
        foreach ($rcps as $rcp) {
            // 查询RCP柜下的接线箱
            $stmt = $pdo->prepare("
                SELECT * FROM junction_box
                WHERE rcp_id = ?
                ORDER BY name ASC
            ");
            $stmt->execute([$rcp['id']]);
            $junctionBoxes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 查询RCP柜下的位号（不属于任何接线箱的位号）
            $stmt = $pdo->prepare("
                SELECT * FROM terminal
                WHERE rcp_id = ? AND (junction_id IS NULL OR junction_id = 0)
                ORDER BY code ASC
            ");
            $stmt->execute([$rcp['id']]);
            $terminals = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 组装结果
            $results[] = [
                'id' => $rcp['id'],
                'name' => $rcp['name'],
                'power_name' => $rcp['power_name'],
                'location' => $rcp['location'],
                'remark' => $rcp['remark'],
                'junctionBoxes' => $junctionBoxes,
                'terminals' => $terminals
            ];
        }
        
        echo json_encode(['status' => 'success', 'data' => $results]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 查询接线箱信息
function queryJunction($input) {
    global $pdo;

    if (!isset($input['name']) || empty($input['name'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少接线箱名称']);
        exit;
    }

    // 不再需要验证token，因为用户已经通过页面级别的验证

    $name = trim($input['name']);
    
    try {
        // 查询所有匹配的接线箱
        $stmt = $pdo->prepare("
            SELECT j.*, r.name AS rcp_name, p.name AS power_name
            FROM junction_box j
            JOIN rcp_cabinet r ON j.rcp_id = r.id
            JOIN power_cabinet p ON r.power_id = p.id
            WHERE j.name LIKE ?
            ORDER BY j.name ASC
        ");
        $stmt->execute(["%$name%"]);
        $junctions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($junctions)) {
            echo json_encode(['status' => 'error', 'message' => '未找到匹配的接线箱']);
            exit;
        }
        
        $results = [];
        
        // 处理每个接线箱的数据
        foreach ($junctions as $junction) {
            // 查询接线箱下的位号
            $stmt = $pdo->prepare("
                SELECT * FROM terminal
                WHERE junction_id = ?
                ORDER BY code ASC
            ");
            $stmt->execute([$junction['id']]);
            $terminals = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 组装结果
            $results[] = [
                'id' => $junction['id'],
                'name' => $junction['name'],
                'rcp_id' => $junction['rcp_id'],
                'rcp_name' => $junction['rcp_name'],
                'power_name' => $junction['power_name'],
                'remark' => $junction['remark'],
                'terminals' => $terminals
            ];
        }
        
        echo json_encode(['status' => 'success', 'data' => $results]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

// 查询位号信息
function queryTag($input) {
    global $pdo;

    if (!isset($input['info']) || empty($input['info'])) {
        echo json_encode(['status' => 'error', 'message' => '缺少位号信息']);
        exit;
    }

    // 不再需要验证token，因为用户已经通过页面级别的验证

    $info = trim($input['info']);
    
    try {
        // 支持多个关键词搜索（按空格分隔）
        $keywords = explode(' ', $info);
        $conditions = [];
        $params = [];

        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if (!empty($keyword)) {
                $conditions[] = "(t.code LIKE ? OR t.cable_name LIKE ?)";
                $params[] = "%$keyword%";
                $params[] = "%$keyword%";
            }
        }

        $whereClause = count($conditions) > 0 ? implode(' AND ', $conditions) : "1=1";
        
        // 修复查询，确保只使用表中存在的列
        $query = "
            SELECT t.id, t.code, t.cable_name, t.rcp_id, t.junction_id, t.remark,
                   r.name AS rcp_name, j.name AS junction_name
            FROM terminal t
            JOIN rcp_cabinet r ON t.rcp_id = r.id
            LEFT JOIN junction_box j ON t.junction_id = j.id
            WHERE $whereClause
            ORDER BY t.code ASC
            LIMIT 100
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($tags)) {
            echo json_encode(['status' => 'error', 'message' => '未找到匹配的位号']);
            exit;
        }
        
        echo json_encode(['status' => 'success', 'data' => $tags]);
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}













/**
 * 查询阀门信息
 * 
 * @param array $input 传入参数
 */
function queryValve($input) {
    global $pdo;
    
    // 检查参数是否完整
    if (!isset($input['valve_info']) || empty($input['valve_info'])) {
        echo json_encode([
            'status' => 'error',
            'message' => '缺少阀门信息'
        ]);
        exit;
    }
    
    // 不再需要验证token，因为用户已经通过页面级别的验证
    // 如果需要用户名，可以从输入参数中获取
    $username = isset($input['username']) ? $input['username'] : 'unknown';
    
    // 获取阀门信息
    $valve_info = trim($input['valve_info']);
    
    // 解析阀门信息为关键词
    $keywords = explode(' ', $valve_info);
    $search_terms = [];
    
    foreach ($keywords as $keyword) {
        if (trim($keyword) !== '') {
            $search_terms[] = trim($keyword);
        }
    }
    
    if (empty($search_terms)) {
        echo json_encode([
            'status' => 'error',
            'message' => '没有有效的阀门信息关键词'
        ]);
        exit;
    }
    
    try {
        // 构建查询条件
        $conditions = [];
        $params = [];
        
        foreach ($search_terms as $term) {
            // 使用问号占位符而不是命名参数
            $conditions[] = "(serial_number LIKE ? OR valve_name LIKE ?)";
            // 每个条件需要两个参数值
            $params[] = "%$term%";  // 用于 serial_number LIKE ?
            $params[] = "%$term%";  // 用于 valve_name LIKE ?
        }
        
        // 构建完整的SQL查询
        $sql = "SELECT * FROM valve WHERE " . implode(" AND ", $conditions);
        
        // 执行查询，直接传递参数数组
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // 获取查询结果
        $valves = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 记录用户操作（如果函数存在）
        if (function_exists('logUserAction')) {
            logUserAction($username, 'query_valve', $valve_info, count($valves) > 0 ? 'success' : 'no_result');
        }
        
        if (count($valves) > 0) {
            // 格式化结果为数组
            $formatted_valves = [];
            foreach ($valves as $valve) {
                // 移除不需要展示的字段
                if (isset($valve['create_time'])) unset($valve['create_time']);
                if (isset($valve['update_time'])) unset($valve['update_time']);
                
                $formatted_valve = [
                    'valve_no' => $valve['serial_number'],
                    'name' => $valve['valve_name'],
                    'platform_name' => isset($valve['platform_name']) ? $valve['platform_name'] : '',
                    'type' => isset($valve['valve_type']) ? $valve['valve_type'] : '',
                    'location' => isset($valve['location']) ? $valve['location'] : '',
                    'flow_type' => isset($valve['flow_type']) ? $valve['flow_type'] : '',
                    'spec' => isset($valve['valve_size']) ? $valve['valve_size'] : '',
                    'flange_type' => isset($valve['flange_type']) ? $valve['flange_type'] : '',
                    'flange_holes' => isset($valve['flange_holes']) ? $valve['flange_holes'] : '',
                    'seal_type' => isset($valve['seal_type']) ? $valve['seal_type'] : '',
                    'gasket_type' => isset($valve['gasket_type']) ? $valve['gasket_type'] : '',
                    'operation_date' => isset($valve['operation_date']) ? $valve['operation_date'] : '',
                    'remark' => isset($valve['remark']) ? $valve['remark'] : ''
                ];
                $formatted_valves[] = $formatted_valve;
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => $formatted_valves
            ]);
        } else {
            echo json_encode([
                'status' => 'success',
                'data' => [],
                'message' => '没有匹配的阀门信息'
            ]);
        }
    } catch (PDOException $e) {
        // 记录错误（如果函数存在）
        if (function_exists('logError')) {
            logError('query_valve', $e->getMessage());
        }
        echo json_encode([
            'status' => 'error',
            'message' => '数据库查询错误: ' . $e->getMessage()
        ]);
    }
}

