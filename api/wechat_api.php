<?php
/**
 * 微信功能统一API入口文件
 * 整合微信登录和微信绑定管理功能
 */

// 定义API访问常量
define('API_ACCESS', true);

// 设置时区为北京时间
date_default_timezone_set('Asia/Shanghai');

// 启动会话
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求参数
$input = json_decode(file_get_contents('php://input'), true) ?: [];
$action = $_GET['action'] ?? $_POST['action'] ?? $input['action'] ?? '';



// 路由分发
try {
    switch ($action) {
        // 微信登录相关功能
        case 'test_connection':
        case 'check_database':
        case 'init_database':
        case 'wechat_login':
        case 'bind_account':
        case 'verify_token':
        case 'logout':
        case 'clear_user_tokens':
        case 'change_password':
        case 'change_username':
        case 'change_real_name':
        case 'unbind_account':
            // 设置参数供子API使用
            $_POST['action'] = $action;
            if (!empty($input)) {
                foreach ($input as $key => $value) {
                    $_POST[$key] = $value;
                }
            }
            // 调用微信登录API
            require_once 'wechat_login_api.php';
            break;
            
        // 微信绑定管理功能（需要管理员权限）
        case 'bindings_list':
        case 'bindings_unbind':
            // 验证管理员权限
            if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'message' => '权限不足，只有管理员可以访问'
                ], JSON_UNESCAPED_UNICODE);
                exit();
            }
            
            // 重新映射action名称并设置参数
            if ($action === 'bindings_list') {
                $_GET['action'] = 'list';
                $_POST['action'] = 'list';
            } elseif ($action === 'bindings_unbind') {
                $_GET['action'] = 'unbind';
                $_POST['action'] = 'unbind';
            }

            // 设置其他参数
            if (!empty($input)) {
                foreach ($input as $key => $value) {
                    $_POST[$key] = $value;
                }
            }

            // 调用微信绑定管理API
            require_once 'wechat_bindings.php';
            break;

        // 仪表位号查询功能（转发到api.php）
        case 'query_rcp':
        case 'query_junction':
        case 'query_tag':
        case 'query_valve':
            // 设置参数供子API使用
            $_POST['action'] = $action;
            if (!empty($input)) {
                foreach ($input as $key => $value) {
                    $_POST[$key] = $value;
                }
            }
            // 调用查询API
            if (!defined('API_ACCESS')) {
                define('API_ACCESS', true);
            }

            // 捕获子API的输出
            ob_start();
            require_once 'api.php';
            $output = ob_get_clean();

            // 直接输出子API的结果
            echo $output;
            exit; // 重要：防止继续执行后续代码
            break;

        // 一站津贴查询功能（转发到jintie_query_api.php）
        case 'get_latest_jintie_data':
        case 'get_jintie_history':
        case 'get_personal_yearly_data':
            // 验证用户权限：只有一站人员和管理员可以访问
            try {
                if (!function_exists('verifyToken')) {
                    // 设置库模式标志，防止执行主要逻辑
                    define('WECHAT_LOGIN_LIBRARY_MODE', true);
                    require_once 'wechat_login_api.php';
                }
                $tokenResult = verifyToken($input['token'] ?? '');
                if (!$tokenResult['valid'] || !in_array($tokenResult['user']['role'], ['admin', 'station_staff'])) {
                    echo json_encode([
                        'success' => false,
                        'message' => '权限不足，只有一站人员和管理员才能查看津贴信息'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                }
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '权限验证失败: ' . $e->getMessage()
                ], JSON_UNESCAPED_UNICODE);
                break;
            }

            // 保存原始的 $_POST 数组
            $originalPost = $_POST;

            // 设置参数供子API使用
            $_POST = []; // 清空现有POST数据
            $_POST['action'] = $action;
            if (!empty($input)) {
                foreach ($input as $key => $value) {
                    $_POST[$key] = $value;
                }
            }



            // 调用津贴查询API
            if (!defined('API_ACCESS')) {
                define('API_ACCESS', true);
            }

            // 捕获子API的输出
            ob_start();
            require_once 'jintie_query_api.php';
            $output = ob_get_clean();

            // 恢复原始的 $_POST 数组
            $_POST = $originalPost;

            // 直接输出子API的结果
            echo $output;
            exit; // 重要：防止继续执行后续代码
            break;

        default:
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => '未找到请求的操作: ' . $action
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
