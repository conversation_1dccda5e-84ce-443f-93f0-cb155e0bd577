<?php
/**
 * API调用统计管理页面
 * 显示腾讯云API的使用情况统计
 */

require_once '../includes/config.php';
require_once '../api/api_stats_logger.php';

// 检查登录状态
if (!isset($_SESSION['user'])) {
    header('Location: login.php');
    exit;
}

// 只允许管理员访问
if ($_SESSION['user']['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

// 获取筛选参数
$apiName = $_GET['api_name'] ?? '';
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // 当月第一天
$endDate = $_GET['end_date'] ?? date('Y-m-t'); // 当月最后一天
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 20;

// 获取统计数据
$overviewStats = getApiOverviewStats();

// 获取详细统计数据（带分页）
$detailStats = getApiUsageStatsWithPagination($apiName, $startDate, $endDate, $page, $perPage);
$totalDetailRecords = getTotalApiUsageCount($apiName, $startDate, $endDate);
$totalPages = ceil($totalDetailRecords / $perPage);

// 获取IP地址统计
$ipStats = getIpUsageStats($startDate, $endDate, 10);

// 获取最近调用记录
$recentCalls = getRecentApiCalls(20, $apiName);

// 获取前端错误统计
$frontendErrorStats = getFrontendErrorStats($startDate, $endDate, 10);

// 获取最近前端错误记录
$recentFrontendErrors = getRecentFrontendErrors(15);

// 计算总计数据
$totalCalls = 0;
$totalSuccess = 0;
$totalFailed = 0;
foreach ($overviewStats as $stat) {
    $totalCalls += $stat['total_calls'];
    $totalSuccess += $stat['success_calls'];
    $totalFailed += $stat['failed_calls'];
}
$overallSuccessRate = $totalCalls > 0 ? round(($totalSuccess / $totalCalls) * 100, 2) : 0;

// API名称映射
$apiNames = [
    'ocr_recognition' => 'OCR文字识别（腾讯云）',
    'portrait_segmentation' => '人像分割（腾讯云）',
    'image_compress' => '图片压缩',
    'smart_photo_optimizer' => '智能证件照优化'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调用统计</title>

    <!-- 预加载字体文件 -->
    <link rel="preload" href="../assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin="anonymous">

    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f2f2f7;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #1d1d1f;
            line-height: 1.47059;
            font-weight: 400;
        }

        .container-fluid {
            padding: 24px;
        }

        .page-header {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .page-header h3 {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0;
            letter-spacing: -0.003em;
        }

        .stats-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 28px 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .stats-card.success::before {
            background: linear-gradient(90deg, #34c759 0%, #30d158 100%);
        }

        .stats-card.warning::before {
            background: linear-gradient(90deg, #ff9500 0%, #ff6b35 100%);
        }

        .stats-card.info::before {
            background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
        }

        .stats-card .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 20px;
            color: white;
        }

        .stats-card.success .card-icon {
            background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
        }

        .stats-card.warning .card-icon {
            background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
        }

        .stats-card.info .card-icon {
            background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
        }

        .stats-card .card-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stats-card .card-title {
            font-size: 14px;
            font-weight: 500;
            color: #86868b;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stats-card .card-value {
            font-size: 32px;
            font-weight: 700;
            color: #1d1d1f;
            margin: 0;
            line-height: 1.1;
        }

        .stats-card .card-subtitle {
            font-size: 13px;
            color: #86868b;
            margin-top: 4px;
        }

        .api-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .api-badge.ocrrecognition { background-color: #007aff; color: white; }
        .api-badge.imagecompress { background-color: #34c759; color: white; }
        .api-badge.portraitsegmentation { background-color: #ff9500; color: white; }
        .api-badge.smartphotooptimizer { background-color: #af52de; color: white; }

        .chart-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            margin-bottom: 24px;
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .chart-container h5 {
            color: #1d1d1f;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 17px;
        }

        .btn-group .btn {
            border-radius: 8px;
            margin-right: 8px;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
            text-align: center;
        }

        .table td, .table th {
            text-align: center;
            vertical-align: middle;
        }

        /* 筛选区域样式优化 */
        .filter-form .row {
            margin: 0;
        }

        .filter-form .col-md-3 {
            padding-left: 8px;
            padding-right: 8px;
        }

        .filter-form .col-md-3:first-child {
            padding-left: 0;
        }

        .filter-form .col-md-3:last-child {
            padding-right: 0;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            font-size: 14px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 0.2rem rgba(0, 122, 255, 0.25);
        }

        /* 分页样式 */
        .pagination {
            margin-top: 20px;
            margin-bottom: 0;
        }

        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 8px 12px;
            font-size: 14px;
        }

        .pagination .page-item.active .page-link {
            background-color: #007aff;
            border-color: #007aff;
            color: white;
        }

        .pagination .page-link:hover {
            background-color: #f8f9fa;
            border-color: #007aff;
            color: #007aff;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #fff;
            border-color: #dee2e6;
        }

        /* 标题行样式 */
        .chart-container .d-flex h5 {
            color: #333;
            font-weight: 600;
        }

        .chart-container .btn-group .btn-sm {
            padding: 6px 12px;
            font-size: 13px;
        }

        /* IP统计和调用记录样式 */
        .table-sm {
            font-size: 13px;
        }

        .table code, .ip-code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 11px;
            margin-right: 4px;
            margin-bottom: 2px;
            display: inline-block;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        /* IP地址统计样式 */
        .ip-address {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
            font-weight: 500;
            padding: 4px 8px;
            font-size: 12px;
        }

        .rank-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            min-width: 32px;
            text-align: center;
        }

        .rank-badge.rank-top {
            background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
            color: #333;
            box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
        }

        .rank-badge.rank-normal {
            background-color: #6c757d;
            color: white;
        }

        .badge {
            font-size: 11px;
        }

        .table-sm td {
            padding: 6px 8px;
            vertical-align: middle;
        }

        .table-sm th {
            padding: 8px;
            font-weight: 600;
            font-size: 12px;
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 16px;
            }

            .stats-card {
                padding: 20px 16px;
                margin-bottom: 16px;
            }

            .stats-card .card-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
                margin-bottom: 12px;
            }

            .stats-card .card-value {
                font-size: 28px;
            }

            .page-header {
                padding: 20px 24px;
            }

            .page-header h3 {
                font-size: 24px;
            }

            .table-responsive {
                font-size: 12px;
            }

            .api-badge {
                font-size: 10px;
                padding: 2px 6px;
            }
        }



        /* 表格样式优化 */
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .table td {
            vertical-align: middle;
            font-size: 13px;
        }

        .badge {
            font-size: 11px;
        }

        .api-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="page-header d-flex justify-content-between align-items-center">
            <h3><i class="fas fa-chart-bar mr-2"></i>API调用统计</h3>
            <button type="button" class="btn btn-outline-primary" onclick="refreshStats()">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
        </div>

        <!-- 总览统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="card-icon">
                        <i class="fas fa-phone-alt"></i>
                    </div>
                    <div class="card-title">总调用次数</div>
                    <div class="card-value"><?= number_format($totalCalls) ?></div>
                    <div class="card-subtitle">API调用总计</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card success">
                    <div class="card-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="card-title">成功调用</div>
                    <div class="card-value"><?= number_format($totalSuccess) ?></div>
                    <div class="card-subtitle">调用成功次数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card warning">
                    <div class="card-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="card-title">失败调用</div>
                    <div class="card-value"><?= number_format($totalFailed) ?></div>
                    <div class="card-subtitle">调用失败次数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info">
                    <div class="card-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="card-title">成功率</div>
                    <div class="card-value"><?= $overallSuccessRate ?>%</div>
                    <div class="card-subtitle">整体成功率</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="chart-container">
            <h5><i class="fas fa-filter"></i> 数据筛选</h5>
            <form method="GET" class="filter-form">
                <div class="row align-items-end g-3">
                    <div class="col-md-3">
                        <label for="api_name" class="form-label">API类型</label>
                        <select class="form-select" id="api_name" name="api_name">
                            <option value="">全部API</option>
                            <?php foreach ($apiNames as $key => $name): ?>
                                <option value="<?= $key ?>" <?= $apiName === $key ? 'selected' : '' ?>><?= $name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $startDate ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $endDate ?>">
                    </div>
                    <div class="col-md-3">
                                <div class="btn-group w-100" role="group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 查询
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilter()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- API概览统计 -->
                <div class="chart-container">
                    <h5><i class="fas fa-chart-pie"></i> API使用概览</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>API名称</th>
                                    <th>总调用次数</th>
                                    <th>成功次数</th>
                                    <th>失败次数</th>
                                    <th>成功率</th>
                                    <th>平均响应时间</th>
                                    <th>首次调用</th>
                                    <th>最近调用</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($overviewStats as $stat): ?>
                                <tr>
                                    <td>
                                        <span class="api-badge <?= str_replace('_', '', $stat['api_name']) ?>">
                                            <?= $apiNames[$stat['api_name']] ?? $stat['api_name'] ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($stat['total_calls']) ?></td>
                                    <td class="text-success"><?= number_format($stat['success_calls']) ?></td>
                                    <td class="text-danger"><?= number_format($stat['failed_calls']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $stat['success_rate'] >= 95 ? 'success' : ($stat['success_rate'] >= 80 ? 'warning' : 'danger') ?>">
                                            <?= $stat['success_rate'] ?>%
                                        </span>
                                    </td>
                                    <td><?= $stat['avg_processing_time'] ?>s</td>
                                    <td><?= $stat['first_call'] ?></td>
                                    <td><?= $stat['last_call'] ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 详细统计数据 -->
                <?php if (!empty($detailStats)): ?>
                <div class="chart-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-list-alt"></i> 详细统计数据 (<?= $startDate ?> 至 <?= $endDate ?>)
                            <small class="text-muted ms-2">共 <?= $totalDetailRecords ?> 条记录</small>
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> 导出Excel
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="exportToCSV()">
                                <i class="fas fa-file-csv"></i> 导出CSV
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>日期</th>
                                    <th>API名称</th>
                                    <th>总调用</th>
                                    <th>成功</th>
                                    <th>失败</th>
                                    <th>成功率</th>
                                    <th>平均响应时间</th>
                                    <th>IP数量</th>
                                    <th>请求数据量</th>
                                    <th>响应数据量</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($detailStats as $stat): ?>
                                <tr>
                                    <td><?= $stat['call_date'] ?></td>
                                    <td>
                                        <span class="api-badge <?= str_replace('_', '', $stat['api_name']) ?>">
                                            <?= $apiNames[$stat['api_name']] ?? $stat['api_name'] ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($stat['total_calls']) ?></td>
                                    <td class="text-success"><?= number_format($stat['success_calls']) ?></td>
                                    <td class="text-danger"><?= number_format($stat['failed_calls']) ?></td>
                                    <td>
                                        <?php
                                        $successRate = $stat['total_calls'] > 0 ? round(($stat['success_calls'] / $stat['total_calls']) * 100, 2) : 0;
                                        ?>
                                        <span class="badge bg-<?= $successRate >= 95 ? 'success' : ($successRate >= 80 ? 'warning' : 'danger') ?>">
                                            <?= $successRate ?>%
                                        </span>
                                    </td>
                                    <td><?= $stat['avg_processing_time'] ?>s</td>
                                    <td>
                                        <span class="badge bg-info"><?= $stat['unique_ips'] ?> 个</span>
                                    </td>
                                    <td><?= formatBytes($stat['total_request_size']) ?></td>
                                    <td><?= formatBytes($stat['total_response_size']) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页导航 -->
                    <?php if ($totalPages > 1): ?>
                    <nav aria-label="详细统计数据分页">
                        <ul class="pagination justify-content-center">
                            <!-- 上一页 -->
                            <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </a>
                            </li>

                            <!-- 页码 -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            if ($startPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>">1</a>
                                </li>
                                <?php if ($startPage > 2): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($endPage < $totalPages): ?>
                                <?php if ($endPage < $totalPages - 1): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>"><?= $totalPages ?></a>
                                </li>
                            <?php endif; ?>

                            <!-- 下一页 -->
                            <li class="page-item <?= $page >= $totalPages ? 'disabled' : '' ?>">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="chart-container text-center">
                    <div class="py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无统计数据</h5>
                        <p class="text-muted">请调整筛选条件或等待API调用产生数据</p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Top 10 IP地址统计 -->
                <?php if (!empty($ipStats)): ?>
                <div class="chart-container">
                    <h5><i class="fas fa-globe"></i> Top 10 IP地址统计</h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>IP地址</th>
                                    <th>总调用次数</th>
                                    <th>成功次数</th>
                                    <th>失败次数</th>
                                    <th>成功率</th>
                                    <th>使用API类型</th>
                                    <th>首次调用</th>
                                    <th>最近调用</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ipStats as $index => $stat): ?>
                                <tr>
                                    <td>
                                        <span class="rank-badge rank-<?= $index + 1 <= 3 ? 'top' : 'normal' ?>">
                                            <?= $index + 1 ?>
                                        </span>
                                    </td>
                                    <td>
                                        <code class="ip-address"><?= htmlspecialchars($stat['ip_address']) ?></code>
                                    </td>
                                    <td>
                                        <strong><?= number_format($stat['total_calls']) ?></strong>
                                    </td>
                                    <td class="text-success"><?= number_format($stat['success_calls']) ?></td>
                                    <td class="text-danger"><?= number_format($stat['failed_calls']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $stat['success_rate'] >= 95 ? 'success' : ($stat['success_rate'] >= 80 ? 'warning' : 'danger') ?>">
                                            <?= $stat['success_rate'] ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= $stat['api_types'] ?> 种API</span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= $stat['first_call'] ?></small>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= $stat['last_call'] ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 前端错误统计 -->
                <div class="chart-container">
                    <h5><i class="fas fa-exclamation-triangle text-warning"></i> 前端错误统计</h5>
                    <?php if (!empty($frontendErrorStats)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>错误类型</th>
                                    <th>相关API</th>
                                    <th>错误次数</th>
                                    <th>影响IP数</th>
                                    <th>错误日期</th>
                                    <th>最近发生</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($frontendErrorStats as $error): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?= getErrorTypeBadgeColor($error['error_type']) ?>">
                                            <?= getErrorTypeDisplayName($error['error_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="api-badge <?= str_replace('_', '', $error['api_name']) ?>">
                                            <?= $apiNames[$error['api_name']] ?? $error['api_name'] ?>
                                        </span>
                                    </td>
                                    <td><strong class="text-danger"><?= $error['error_count'] ?></strong></td>
                                    <td><span class="badge bg-info"><?= $error['unique_ips'] ?> 个</span></td>
                                    <td><?= $error['error_date'] ?></td>
                                    <td><small class="text-muted"><?= date('H:i:s', strtotime($error['latest_error'])) ?></small></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h6 class="text-muted">暂无前端错误记录</h6>
                            <p class="text-muted small">这是一个好消息！说明前端运行正常</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- 最近前端错误记录 -->
                <div class="chart-container">
                    <h5><i class="fas fa-bug text-danger"></i> 最近前端错误记录 (最新15条)</h5>
                    <?php if (!empty($recentFrontendErrors)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>错误时间</th>
                                    <th>错误类型</th>
                                    <th>相关API</th>
                                    <th>IP地址</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentFrontendErrors as $error): ?>
                                <tr>
                                    <td>
                                        <small><?= date('m-d H:i:s', strtotime($error['error_time'])) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= getErrorTypeBadgeColor($error['error_type']) ?> badge-sm">
                                            <?= getErrorTypeDisplayName($error['error_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="api-badge <?= str_replace('_', '', $error['api_name']) ?>">
                                            <?= $apiNames[$error['api_name']] ?? $error['api_name'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <code class="text-muted" style="font-size: 11px;"><?= htmlspecialchars($error['ip_address']) ?></code>
                                    </td>
                                    <td>
                                        <small class="text-danger" title="<?= htmlspecialchars($error['error_message']) ?>">
                                            <?= mb_substr($error['error_message'], 0, 30) ?>...
                                        </small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-smile fa-3x text-success mb-3"></i>
                            <h6 class="text-muted">暂无前端错误记录</h6>
                            <p class="text-muted small">前端运行稳定，没有错误报告</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- 最近调用记录 -->
                <?php if (!empty($recentCalls)): ?>
                <div class="chart-container">
                    <h5><i class="fas fa-history"></i> 最近调用记录 (最新20条)</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>调用时间</th>
                                    <th>API名称</th>
                                    <th>IP地址</th>
                                    <th>状态</th>
                                    <th>响应时间</th>
                                    <th>请求大小</th>
                                    <th>响应大小</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentCalls as $call): ?>
                                <tr>
                                    <td>
                                        <small><?= date('m-d H:i:s', strtotime($call['call_time'])) ?></small>
                                    </td>
                                    <td>
                                        <span class="api-badge <?= str_replace('_', '', $call['api_name']) ?>">
                                            <?= $apiNames[$call['api_name']] ?? $call['api_name'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <code class="text-muted" style="font-size: 11px;"><?= htmlspecialchars($call['ip_address']) ?></code>
                                    </td>
                                    <td>
                                        <?php if ($call['is_success']): ?>
                                            <span class="badge bg-success">成功</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">失败</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?= $call['processing_time'] ?>s</small>
                                    </td>
                                    <td>
                                        <small><?= formatBytes($call['request_size']) ?></small>
                                    </td>
                                    <td>
                                        <small><?= formatBytes($call['response_size']) ?></small>
                                    </td>
                                    <td>
                                        <?php if (!empty($call['error_message'])): ?>
                                            <small class="text-danger" title="<?= htmlspecialchars($call['error_message']) ?>">
                                                <?= mb_substr($call['error_message'], 0, 20) ?>...
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted">-</small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php else: ?>
                <div class="chart-container text-center">
                    <div class="py-5">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无调用记录</h5>
                        <p class="text-muted">等待API调用产生数据</p>
                    </div>
                </div>
                <?php endif; ?>
    </div>

    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshStats() {
            location.reload();
        }

        function resetFilter() {
            document.getElementById('api_name').value = '';
            document.getElementById('start_date').value = '<?= date('Y-m-01') ?>'; // 当月第一天
            document.getElementById('end_date').value = '<?= date('Y-m-t') ?>'; // 当月最后一天
        }

        function exportToExcel() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'excel');
            window.open('api_stats_export.php?' + params.toString(), '_blank');
        }

        function exportToCSV() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            window.open('api_stats_export.php?' + params.toString(), '_blank');
        }

        // 自动刷新功能
        setInterval(function() {
            // 每5分钟自动刷新一次统计数据
            const lastRefresh = localStorage.getItem('api_stats_last_refresh');
            const now = Date.now();
            if (!lastRefresh || (now - parseInt(lastRefresh)) > 300000) { // 5分钟
                localStorage.setItem('api_stats_last_refresh', now.toString());
                // 可以在这里添加AJAX刷新逻辑
            }
        }, 60000); // 每分钟检查一次
    </script>
</body>
</html>

<?php
/**
 * 格式化字节数为可读格式
 */
function formatBytes($bytes, $precision = 2) {
    if ($bytes == 0) return '0 B';

    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    $base = log($bytes, 1024);

    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
}

/**
 * 获取错误类型的显示名称
 */
function getErrorTypeDisplayName($errorType) {
    $errorTypeNames = [
        'network_error' => '网络错误',
        'ocr_error' => 'OCR识别错误',
        'js_error' => 'JavaScript错误',
        'api_error' => 'API调用错误',
        'validation_error' => '数据验证错误',
        'timeout_error' => '超时错误',
        'unknown_error' => '未知错误'
    ];

    return $errorTypeNames[$errorType] ?? $errorType;
}

/**
 * 获取错误类型的徽章颜色
 */
function getErrorTypeBadgeColor($errorType) {
    $colorMap = [
        'network_error' => 'danger',
        'ocr_error' => 'warning',
        'js_error' => 'info',
        'api_error' => 'danger',
        'validation_error' => 'secondary',
        'timeout_error' => 'warning',
        'unknown_error' => 'dark'
    ];

    return $colorMap[$errorType] ?? 'secondary';
}
?>
