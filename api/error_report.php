<?php
/**
 * 前端错误上报API
 * 用于收集微信小程序中的各种错误信息
 */

// 引入配置文件
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/api_stats_logger.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只支持POST请求'
    ]);
    exit();
}

$startTime = microtime(true);
$requestSize = strlen(file_get_contents('php://input'));

try {
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('无效的请求数据');
    }

    // 验证必需字段
    $requiredFields = ['errorType', 'errorMessage', 'apiName'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            throw new Exception("缺少必需字段: {$field}");
        }
    }

    $errorType = $data['errorType']; // network_error, ocr_error, js_error, etc.
    $errorMessage = $data['errorMessage'];
    $apiName = $data['apiName'];
    $errorDetails = $data['errorDetails'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    $timestamp = $data['timestamp'] ?? time();

    // 记录错误到数据库
    recordFrontendError($errorType, $errorMessage, $apiName, $errorDetails, $userAgent, $timestamp);

    // 返回成功响应
    $response = [
        'success' => true,
        'message' => '错误报告已记录'
    ];

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 记录前端错误到数据库
 */
function recordFrontendError($errorType, $errorMessage, $apiName, $errorDetails, $userAgent, $timestamp) {
    global $pdo;
    
    try {
        // 确保错误记录表存在
        createErrorReportTableIfNotExists();
        
        // 获取客户端信息
        $ipAddress = getClientIP();
        
        // 插入错误记录
        $sql = "INSERT INTO frontend_error_reports (
            error_type, error_message, api_name, error_details,
            ip_address, user_agent, error_time, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $errorType,
            $errorMessage,
            $apiName,
            $errorDetails,
            $ipAddress,
            $userAgent,
            $timestamp
        ]);
        
    } catch (Exception $e) {
        error_log("记录前端错误失败: " . $e->getMessage());
        throw $e;
    }
}

/**
 * 创建前端错误报告表（如果不存在）
 */
function createErrorReportTableIfNotExists() {
    global $pdo;
    
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS `frontend_error_reports` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `error_type` varchar(50) NOT NULL COMMENT '错误类型',
            `error_message` text NOT NULL COMMENT '错误信息',
            `api_name` varchar(50) NOT NULL COMMENT '相关API名称',
            `error_details` text COMMENT '错误详细信息(JSON)',
            `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
            `user_agent` text COMMENT '用户代理',
            `error_time` datetime NOT NULL COMMENT '错误发生时间',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
            PRIMARY KEY (`id`),
            KEY `idx_error_type` (`error_type`),
            KEY `idx_api_name` (`api_name`),
            KEY `idx_error_time` (`error_time`),
            KEY `idx_ip_address` (`ip_address`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='前端错误报告表'");
    } catch (PDOException $e) {
        error_log("创建前端错误报告表失败: " . $e->getMessage());
        throw $e;
    }
}
?>
