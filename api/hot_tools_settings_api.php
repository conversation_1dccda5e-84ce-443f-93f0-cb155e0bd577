<?php
/**
 * 热门工具设置API接口
 * 专门用于处理用户自定义热门工具设置
 */

// 引入配置文件
require_once '../includes/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求参数
$input = json_decode(file_get_contents('php://input'), true) ?: [];
$action = $_GET['action'] ?? $_POST['action'] ?? $input['action'] ?? '';
$token = $_GET['token'] ?? $_POST['token'] ?? $input['token'] ?? '';

/**
 * 验证用户Token
 */
function verifyUserToken($token) {
    global $pdo;

    if (empty($token)) {
        return false;
    }

    try {
        // 查询Token对应的用户信息
        $stmt = $pdo->prepare("
            SELECT u.id, u.username, u.real_name, u.role
            FROM users u
            INNER JOIN wechat_tokens wt ON u.id = wt.user_id
            WHERE wt.token = ? AND wt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();

        return $user ?: false;
    } catch (PDOException $e) {
        return false;
    }
}

// 验证Token
$user = verifyUserToken($token);
if (!$user) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => '用户未登录或Token已过期'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// 创建用户设置表（如果不存在）
function createUserSettingsTableIfNotExists() {
    global $pdo;

    try {
        $sql = "CREATE TABLE IF NOT EXISTS `user_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL COMMENT '用户ID',
            `hot_tools` text COMMENT '热门工具设置（JSON格式）',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_id` (`user_id`),
            KEY `idx_user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户个人设置表'";

        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// 保存用户热门工具设置
function saveUserHotToolsSettings($userId, $hotTools) {
    global $pdo;

    try {
        // 确保表存在
        createUserSettingsTableIfNotExists();
        
        // 验证热门工具数据
        if (!is_array($hotTools)) {
            return [
                'success' => false,
                'message' => '热门工具数据格式错误'
            ];
        }
        
        // 限制最多4个工具
        if (count($hotTools) > 4) {
            return [
                'success' => false,
                'message' => '最多只能选择4个热门工具'
            ];
        }
        
        // 将数组转换为JSON字符串
        $hotToolsJson = json_encode($hotTools, JSON_UNESCAPED_UNICODE);
        
        // 检查用户设置是否已存在
        $stmt = $pdo->prepare("SELECT id FROM user_settings WHERE user_id = ?");
        $stmt->execute([$userId]);
        $existingSetting = $stmt->fetch();
        
        if ($existingSetting) {
            // 更新现有设置
            $stmt = $pdo->prepare("UPDATE user_settings SET hot_tools = ?, updated_at = NOW() WHERE user_id = ?");
            $stmt->execute([$hotToolsJson, $userId]);
        } else {
            // 创建新设置
            $stmt = $pdo->prepare("INSERT INTO user_settings (user_id, hot_tools, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
            $stmt->execute([$userId, $hotToolsJson]);
        }
        
        return [
            'success' => true,
            'message' => '热门工具设置保存成功'
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => '数据库错误: ' . $e->getMessage()
        ];
    }
}

// 获取用户热门工具设置
function getUserHotToolsSettings($userId) {
    global $pdo;

    try {
        // 确保表存在
        createUserSettingsTableIfNotExists();
        
        $stmt = $pdo->prepare("SELECT hot_tools FROM user_settings WHERE user_id = ?");
        $stmt->execute([$userId]);
        $setting = $stmt->fetch();
        
        $hotTools = [];
        if ($setting && $setting['hot_tools']) {
            $hotTools = json_decode($setting['hot_tools'], true) ?: [];
        }
        
        return [
            'success' => true,
            'data' => [
                'hot_tools' => $hotTools
            ]
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => '数据库错误: ' . $e->getMessage()
        ];
    }
}

// 路由处理
try {
    switch ($action) {
        case 'save_hot_tools_settings':
            // 保存热门工具设置
            $hotToolsParam = $input['hot_tools'] ?? $_GET['hot_tools'] ?? '';
            
            // 如果是字符串，转换为数组
            if (is_string($hotToolsParam)) {
                $hotTools = $hotToolsParam ? explode(',', $hotToolsParam) : [];
            } else {
                $hotTools = $hotToolsParam;
            }
            
            $result = saveUserHotToolsSettings($user['id'], $hotTools);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'get_hot_tools_settings':
            // 获取热门工具设置
            $result = getUserHotToolsSettings($user['id']);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        default:
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => '未找到请求的操作: ' . $action
            ], JSON_UNESCAPED_UNICODE);
            break;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

?>
