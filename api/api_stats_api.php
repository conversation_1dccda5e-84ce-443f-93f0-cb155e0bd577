<?php
/**
 * API统计数据接口
 * 提供实时的API调用统计数据
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/api_stats_logger.php';

// 检查登录状态（如果需要）
if (!isset($_SESSION['user'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit();
}

// 只允许管理员访问
if ($_SESSION['user']['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['error' => '权限不足']);
    exit();
}

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'overview':
            // 获取概览统计
            $stats = getApiOverviewStats();
            
            // 计算总计数据
            $totalCalls = 0;
            $totalSuccess = 0;
            $totalFailed = 0;
            foreach ($stats as $stat) {
                $totalCalls += $stat['total_calls'];
                $totalSuccess += $stat['success_calls'];
                $totalFailed += $stat['failed_calls'];
            }
            $overallSuccessRate = $totalCalls > 0 ? round(($totalSuccess / $totalCalls) * 100, 2) : 0;
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'overview' => $stats,
                    'totals' => [
                        'total_calls' => $totalCalls,
                        'total_success' => $totalSuccess,
                        'total_failed' => $totalFailed,
                        'success_rate' => $overallSuccessRate
                    ]
                ]
            ]);
            break;
            
        case 'detail':
            // 获取详细统计
            $apiName = $_GET['api_name'] ?? '';
            $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $_GET['end_date'] ?? date('Y-m-d');
            
            $stats = getApiUsageStats($apiName, $startDate, $endDate);
            
            echo json_encode([
                'success' => true,
                'data' => $stats,
                'filters' => [
                    'api_name' => $apiName,
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]);
            break;
            
        case 'realtime':
            // 获取实时统计（最近24小时）
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $today = date('Y-m-d');
            
            $realtimeStats = getApiUsageStats('', $yesterday, $today);
            
            // 按小时分组统计
            $hourlyStats = [];
            for ($i = 0; $i < 24; $i++) {
                $hour = str_pad($i, 2, '0', STR_PAD_LEFT);
                $hourlyStats[$hour] = [
                    'hour' => $hour . ':00',
                    'total_calls' => 0,
                    'success_calls' => 0,
                    'failed_calls' => 0
                ];
            }
            
            // 获取详细的小时统计
            $sql = "SELECT 
                HOUR(call_time) as hour,
                COUNT(*) as total_calls,
                SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
                SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls
            FROM api_usage_stats 
            WHERE DATE(call_time) >= ? AND DATE(call_time) <= ?
            GROUP BY HOUR(call_time)
            ORDER BY hour";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$yesterday, $today]);
            $hourlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($hourlyData as $data) {
                $hour = str_pad($data['hour'], 2, '0', STR_PAD_LEFT);
                if (isset($hourlyStats[$hour])) {
                    $hourlyStats[$hour] = [
                        'hour' => $hour . ':00',
                        'total_calls' => (int)$data['total_calls'],
                        'success_calls' => (int)$data['success_calls'],
                        'failed_calls' => (int)$data['failed_calls']
                    ];
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'daily' => $realtimeStats,
                    'hourly' => array_values($hourlyStats)
                ]
            ]);
            break;
            
        case 'api_list':
            // 获取API列表和基本信息
            $apiList = [
                'ocr_recognition' => [
                    'name' => 'OCR文字识别',
                    'description' => '腾讯云OCR文字识别API',
                    'endpoint' => '/api/ocr_recognition.php',
                    'color' => '#007aff'
                ],
                'image_compress' => [
                    'name' => '图片压缩',
                    'description' => '本地GD库图片压缩处理',
                    'endpoint' => '/api/image_compress.php',
                    'color' => '#34c759'
                ],
                'portrait_segmentation' => [
                    'name' => '人像分割',
                    'description' => '腾讯云人体分析人像分割API',
                    'endpoint' => '/api/portrait_segmentation.php',
                    'color' => '#ff9500'
                ]
            ];
            
            echo json_encode([
                'success' => true,
                'data' => $apiList
            ]);
            break;
            
        case 'recent_calls':
            // 获取最近的API调用记录
            $limit = (int)($_GET['limit'] ?? 50);
            $apiName = $_GET['api_name'] ?? '';
            
            $sql = "SELECT 
                api_name, api_action, call_time, ip_address,
                processing_time, is_success, error_message,
                request_size, response_size
            FROM api_usage_stats 
            WHERE 1=1";
            
            $params = [];
            if ($apiName) {
                $sql .= " AND api_name = ?";
                $params[] = $apiName;
            }
            
            $sql .= " ORDER BY call_time DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $recentCalls = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'data' => $recentCalls
            ]);
            break;
            
        case 'error_analysis':
            // 错误分析
            $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
            $endDate = $_GET['end_date'] ?? date('Y-m-d');
            
            $sql = "SELECT 
                api_name,
                error_message,
                COUNT(*) as error_count,
                DATE(call_time) as error_date
            FROM api_usage_stats 
            WHERE is_success = 0 
            AND DATE(call_time) >= ? 
            AND DATE(call_time) <= ?
            AND error_message IS NOT NULL
            GROUP BY api_name, error_message, DATE(call_time)
            ORDER BY error_count DESC, error_date DESC
            LIMIT 100";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$startDate, $endDate]);
            $errorAnalysis = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'data' => $errorAnalysis
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => '无效的操作']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
