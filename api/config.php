<?php
/**
 * API配置文件
 * 注意：此文件包含敏感信息，请确保：
 * 1. 不要提交到版本控制系统
 * 2. 设置适当的文件权限
 * 3. 定期更换密钥
 */

// 引入数据库配置
require_once __DIR__ . '/../includes/config.php';

// 腾讯云API配置 - 从数据库动态加载
// 注意：这些常量已经在 includes/config.php 中通过 TencentConfigManager 定义
// 这里只是为了确保在API文件中也能使用这些常量
if (!defined('TENCENT_SECRET_ID')) {
    define('TENCENT_SECRET_ID', '');
}
if (!defined('TENCENT_SECRET_KEY')) {
    define('TENCENT_SECRET_KEY', '');
}
if (!defined('TENCENT_REGION')) {
    define('TENCENT_REGION', 'ap-beijing');
}

// 人体分析API配置
define('BDA_HOST', 'bda.tencentcloudapi.com');          // 人体分析API域名

// 数据万象API配置 (用于图片压缩) - 当前未使用
// 注意：您的系统使用本地GD库进行图片压缩，不需要腾讯云数据万象服务
// 如果将来需要使用腾讯云图片处理服务，可以取消注释并配置以下选项：
// define('CI_BUCKET', '');                             // 存储桶名称
// define('CI_REGION', 'ap-beijing');                   // 存储桶地域
// define('CI_DOMAIN', '');                             // 数据万象域名

// OCR识别API配置
define('OCR_ENDPOINT', 'ocr.tencentcloudapi.com');      // OCR API域名

// 智能照片优化配置
// 注意：智能照片优化API (smart_photo_optimizer.php) 使用本地GD库处理
// 不需要调用外部API，因此不需要额外的API密钥或域名配置
define('SMART_OPTIMIZER_ENABLED', true);                // 是否启用智能优化功能
define('SMART_OPTIMIZER_MAX_SIZE', 2048);               // 最大处理图片尺寸 (像素)
define('SMART_OPTIMIZER_QUALITY', 95);                  // 输出图片质量 (1-100)

// 其他API配置可以在这里添加
// define('OTHER_API_KEY', 'your_api_key_here');

// 环境配置
define('DEBUG_MODE', false);  // 生产环境设置为false
define('LOG_ERRORS', true);   // 是否记录错误日志

// 安全配置 - 允许访问API的域名列表
define('ALLOWED_ORIGINS', [
    'https://zdh.sunxiyue.com',        // 您的主域名
    'https://sunxiyue.com',            // 保留现有域名
    'http://localhost',                // 本地开发环境
    'http://127.0.0.1'                 // 本地开发环境
]);

// 文件上传配置
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/webp']);
?>
