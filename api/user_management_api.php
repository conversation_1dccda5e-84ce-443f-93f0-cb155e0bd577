<?php
/**
 * 用户管理API接口
 * 专门用于微信小程序的管理员用户管理功能
 */

require_once '../includes/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * 验证管理员权限（通过Token）
 */
function verifyAdminToken($token) {
    global $pdo;
    
    if (empty($token)) {
        return false;
    }
    
    try {
        // 验证Token并检查是否为管理员
        $stmt = $pdo->prepare("
            SELECT wt.user_id, u.username, u.role, u.real_name
            FROM wechat_tokens wt
            JOIN users u ON wt.user_id = u.id
            WHERE wt.token = ? AND wt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $result = $stmt->fetch();
        
        if ($result && $result['role'] === 'admin') {
            return $result;
        }
        
        return false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取用户列表
 */
function getUserList($page = 1, $search = '', $role = '') {
    global $pdo;
    
    try {
        $itemsPerPage = 20;
        $offset = ($page - 1) * $itemsPerPage;
        
        // 构建查询条件
        $whereConditions = [];
        $params = [];
        
        if (!empty($search)) {
            $whereConditions[] = "(u.username LIKE ? OR u.real_name LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($role)) {
            $whereConditions[] = "u.role = ?";
            $params[] = $role;
        }
        
        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
        
        // 查询用户列表
        $sql = "SELECT SQL_CALC_FOUND_ROWS
                    u.id, u.username, u.real_name, u.role, u.is_station_staff,
                    (SELECT COUNT(*) FROM user_wechat_bindings uwb WHERE uwb.user_id = u.id AND uwb.is_active = 1) as wechat_bindings,
                    (SELECT wu.nickname FROM user_wechat_bindings uwb
                     JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
                     WHERE uwb.user_id = u.id AND uwb.is_active = 1 LIMIT 1) as wechat_nickname,
                    (SELECT uwb.bind_time FROM user_wechat_bindings uwb
                     WHERE uwb.user_id = u.id AND uwb.is_active = 1 LIMIT 1) as wechat_bind_time
                FROM users u
                $whereClause
                ORDER BY u.id DESC
                LIMIT ? OFFSET ?";
        
        $params[] = $itemsPerPage;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll();
        
        // 获取总数
        $total = $pdo->query("SELECT FOUND_ROWS()")->fetchColumn();
        
        // 处理数据格式
        foreach ($users as &$user) {
            $user['role_text'] = $user['role'] === 'admin' ? '管理员' :
                                ($user['role'] === 'manager' ? '普通管理员' : '普通用户');
            $user['station_text'] = $user['is_station_staff'] ? '一站人员' : '非一站人员';
            $user['has_wechat'] = $user['wechat_bindings'] > 0;

            // 格式化微信绑定信息
            if ($user['has_wechat']) {
                $user['wechat_info'] = [
                    'nickname' => $user['wechat_nickname'] ?: '未知',
                    'bind_time' => $user['wechat_bind_time'] ? date('Y-m-d H:i:s', strtotime($user['wechat_bind_time'])) : null,
                    'bind_time_formatted' => $user['wechat_bind_time'] ? date('m-d H:i', strtotime($user['wechat_bind_time'])) : null
                ];
            } else {
                $user['wechat_info'] = null;
            }

            // 清理不需要的字段
            unset($user['wechat_nickname'], $user['wechat_bind_time']);
        }
        
        return [
            'success' => true,
            'data' => [
                'users' => $users,
                'pagination' => [
                    'current_page' => $page,
                    'total_items' => $total,
                    'items_per_page' => $itemsPerPage,
                    'total_pages' => ceil($total / $itemsPerPage)
                ]
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '获取用户列表失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 添加用户
 */
function addUser($username, $password, $realName, $role, $isStationStaff) {
    global $pdo;
    
    try {
        // 验证输入
        if (empty($username) || empty($password)) {
            throw new Exception('用户名和密码不能为空');
        }
        
        if (strlen($username) < 3 || strlen($username) > 50) {
            throw new Exception('用户名长度必须在3-50个字符之间');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('密码长度不能少于6个字符');
        }
        
        if (!in_array($role, ['admin', 'manager', 'user'])) {
            throw new Exception('无效的用户角色');
        }
        
        // 检查用户名是否已存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->fetch()) {
            throw new Exception('用户名已存在');
        }
        
        // 加密密码
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // 插入用户
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, real_name, role, is_station_staff) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$username, $hashedPassword, $realName ?: $username, $role, $isStationStaff ? 1 : 0]);
        
        $userId = $pdo->lastInsertId();
        
        return [
            'success' => true,
            'message' => '用户添加成功',
            'data' => ['user_id' => $userId]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 解绑用户微信
 */
function unbindUserWechat($userId, $adminUserId) {
    global $pdo;

    try {
        if (empty($userId)) {
            throw new Exception('用户ID不能为空');
        }

        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT username, real_name FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 检查是否有微信绑定
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_wechat_bindings WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $bindingCount = $stmt->fetchColumn();

        if ($bindingCount == 0) {
            throw new Exception('该用户没有微信绑定');
        }

        // 开始事务
        $pdo->beginTransaction();

        try {
            // 1. 解除微信绑定（设置为非活跃状态）
            $stmt = $pdo->prepare("UPDATE user_wechat_bindings SET is_active = 0, updated_at = NOW() WHERE user_id = ?");
            $stmt->execute([$userId]);

            // 2. 删除相关的微信Token（但保留当前管理员的Token）
            if ($userId == $adminUserId) {
                // 如果是管理员解除自己的绑定，只删除其他Token，保留当前使用的Token
                $currentToken = $_GET['token'] ?? $input['token'] ?? '';
                $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ? AND token != ?");
                $stmt->execute([$userId, $currentToken]);
            } else {
                // 如果是解除其他用户的绑定，删除该用户的所有Token
                $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ?");
                $stmt->execute([$userId]);
            }

            // 3. 记录操作日志（如果有日志表的话）
            try {
                $stmt = $pdo->prepare("INSERT INTO operation_logs (user_id, action, target, details) VALUES (?, ?, ?, ?)");
                $stmt->execute([
                    $adminUserId,
                    '解除微信绑定',
                    'users',
                    json_encode(['target_user_id' => $userId, 'target_username' => $user['username']], JSON_UNESCAPED_UNICODE)
                ]);
            } catch (Exception $e) {
                // 日志记录失败不影响主要操作
            }

            $pdo->commit();

            return [
                'success' => true,
                'message' => '微信绑定解除成功'
            ];

        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 删除用户（自动解绑微信）
 */
function deleteUser($userId, $adminUserId) {
    global $pdo;

    try {
        if (empty($userId)) {
            throw new Exception('用户ID不能为空');
        }

        // 不能删除自己
        if ($userId == $adminUserId) {
            throw new Exception('不能删除自己的账号');
        }

        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT username, role FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 开始事务
        $pdo->beginTransaction();

        try {
            // 1. 先解除微信绑定（设置为非活跃状态）
            $stmt = $pdo->prepare("UPDATE user_wechat_bindings SET is_active = 0, updated_at = NOW() WHERE user_id = ?");
            $stmt->execute([$userId]);

            // 2. 删除相关的微信Token
            $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ?");
            $stmt->execute([$userId]);

            // 3. 删除用户
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$userId]);

            // 4. 记录操作日志
            try {
                $stmt = $pdo->prepare("INSERT INTO operation_logs (user_id, action, target, details) VALUES (?, ?, ?, ?)");
                $stmt->execute([
                    $adminUserId,
                    '删除用户',
                    'users',
                    json_encode(['deleted_user_id' => $userId, 'deleted_username' => $user['username'], 'note' => '已自动解除微信绑定'], JSON_UNESCAPED_UNICODE)
                ]);
            } catch (Exception $e) {
                // 日志记录失败不影响主要操作
            }

            $pdo->commit();

            return [
                'success' => true,
                'message' => '用户删除成功（已自动解除微信绑定）'
            ];

        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 修改用户密码
 */
function updateUserPassword($userId, $newPassword, $adminUserId) {
    global $pdo;
    
    try {
        if (empty($userId) || empty($newPassword)) {
            throw new Exception('用户ID和新密码不能为空');
        }
        
        if (strlen($newPassword) < 6) {
            throw new Exception('密码长度不能少于6个字符');
        }
        
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        // 加密新密码
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // 更新密码
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$hashedPassword, $userId]);
        
        // 如果修改的是自己的密码，清除所有Token强制重新登录
        if ($userId == $adminUserId) {
            $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ?");
            $stmt->execute([$userId]);
        }
        
        return [
            'success' => true,
            'message' => '密码修改成功'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 修改用户真实姓名
 */
function updateUserRealName($userId, $realName) {
    global $pdo;
    
    try {
        if (empty($userId)) {
            throw new Exception('用户ID不能为空');
        }
        
        if (empty($realName)) {
            throw new Exception('真实姓名不能为空');
        }
        
        if (strlen($realName) > 50) {
            throw new Exception('真实姓名长度不能超过50个字符');
        }
        
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        // 更新真实姓名
        $stmt = $pdo->prepare("UPDATE users SET real_name = ? WHERE id = ?");
        $stmt->execute([$realName, $userId]);
        
        return [
            'success' => true,
            'message' => '真实姓名修改成功'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 修改用户角色
 */
function updateUserRole($userId, $role, $isStationStaff, $adminUserId) {
    global $pdo;
    
    try {
        if (empty($userId)) {
            throw new Exception('用户ID不能为空');
        }
        
        if (!in_array($role, ['admin', 'manager', 'user'])) {
            throw new Exception('无效的用户角色');
        }
        
        // 不能修改自己的角色
        if ($userId == $adminUserId) {
            throw new Exception('不能修改自己的角色');
        }
        
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        // 更新角色和一站人员状态
        $stmt = $pdo->prepare("UPDATE users SET role = ?, is_station_staff = ? WHERE id = ?");
        $stmt->execute([$role, $isStationStaff ? 1 : 0, $userId]);
        
        return [
            'success' => true,
            'message' => '用户角色修改成功'
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 获取用户详情
 */
function getUserDetail($userId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT u.id, u.username, u.real_name, u.role, u.is_station_staff,
                   (SELECT COUNT(*) FROM user_wechat_bindings uwb WHERE uwb.user_id = u.id AND uwb.is_active = 1) as wechat_bindings,
                   (SELECT wu.nickname FROM user_wechat_bindings uwb
                    JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
                    WHERE uwb.user_id = u.id AND uwb.is_active = 1 LIMIT 1) as wechat_nickname,
                   (SELECT uwb.bind_time FROM user_wechat_bindings uwb
                    WHERE uwb.user_id = u.id AND uwb.is_active = 1 LIMIT 1) as wechat_bind_time
            FROM users u
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            throw new Exception('用户不存在');
        }

        $user['role_text'] = $user['role'] === 'admin' ? '管理员' :
                            ($user['role'] === 'manager' ? '普通管理员' : '普通用户');
        $user['station_text'] = $user['is_station_staff'] ? '一站人员' : '非一站人员';
        $user['has_wechat'] = $user['wechat_bindings'] > 0;

        // 格式化微信绑定信息
        if ($user['has_wechat']) {
            $user['wechat_info'] = [
                'nickname' => $user['wechat_nickname'] ?: '未知',
                'bind_time' => $user['wechat_bind_time'] ? date('Y-m-d H:i:s', strtotime($user['wechat_bind_time'])) : null,
                'bind_time_formatted' => $user['wechat_bind_time'] ? date('m-d H:i', strtotime($user['wechat_bind_time'])) : null
            ];
        } else {
            $user['wechat_info'] = null;
        }

        // 清理不需要的字段
        unset($user['wechat_nickname'], $user['wechat_bind_time']);

        return [
            'success' => true,
            'data' => $user
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$input = json_decode(file_get_contents('php://input'), true) ?: [];

// 验证Token和管理员权限
$token = $input['token'] ?? $_GET['token'] ?? '';
$adminUser = verifyAdminToken($token);

if (!$adminUser) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => '权限不足，只有管理员可以访问'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// 路由处理
try {
    switch ($action) {
        case 'list':
            // 获取用户列表
            $page = max(1, intval($_GET['page'] ?? 1));
            $search = $_GET['search'] ?? '';
            $role = $_GET['role'] ?? '';

            $result = getUserList($page, $search, $role);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'add':
            // 添加用户
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';
            $realName = $input['real_name'] ?? '';
            $role = $input['role'] ?? 'user';
            $isStationStaff = $input['is_station_staff'] ?? false;

            $result = addUser($username, $password, $realName, $role, $isStationStaff);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'delete':
            // 删除用户
            $userId = $input['user_id'] ?? $_GET['user_id'] ?? '';

            $result = deleteUser($userId, $adminUser['user_id']);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'unbind_wechat':
            // 解绑微信
            $userId = $input['user_id'] ?? $_GET['user_id'] ?? '';

            $result = unbindUserWechat($userId, $adminUser['user_id']);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'update_password':
            // 修改密码
            $userId = $input['user_id'] ?? $_GET['user_id'] ?? '';
            $newPassword = $input['new_password'] ?? $_GET['new_password'] ?? '';

            $result = updateUserPassword($userId, $newPassword, $adminUser['user_id']);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'update_real_name':
            // 修改真实姓名
            $userId = $input['user_id'] ?? $_GET['user_id'] ?? '';
            $realName = $input['real_name'] ?? $_GET['real_name'] ?? '';

            $result = updateUserRealName($userId, $realName);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'update_role':
            // 修改角色
            $userId = $input['user_id'] ?? $_GET['user_id'] ?? '';
            $role = $input['role'] ?? $_GET['role'] ?? '';
            $isStationStaff = $input['is_station_staff'] ?? $_GET['is_station_staff'] ?? false;

            $result = updateUserRole($userId, $role, $isStationStaff, $adminUser['user_id']);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'detail':
            // 获取用户详情
            $userId = $_GET['user_id'] ?? '';

            $result = getUserDetail($userId);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        default:
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => '未找到请求的操作: ' . $action
            ], JSON_UNESCAPED_UNICODE);
            break;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
