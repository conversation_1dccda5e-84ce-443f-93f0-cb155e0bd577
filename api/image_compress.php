<?php
/**
 * 图片压缩API - 调整图片文件大小
 * 使用GD库进行本地图片压缩处理
 */

// 引入配置文件
require_once __DIR__ . '/config.php';
// 引入API统计记录器
require_once __DIR__ . '/api_stats_logger.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '只允许POST请求']);
    exit;
}

$startTime = microtime(true);
$requestSize = strlen(file_get_contents('php://input'));
$isSuccess = true;
$errorMessage = null;

try {
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('无效的请求数据');
    }

    $imageData = $input['imageData'] ?? '';
    $targetSize = $input['targetSize'] ?? 100; // 默认100KB
    $format = $input['format'] ?? 'jpg'; // 默认JPG格式

    if (empty($imageData)) {
        throw new Exception('缺少图片数据');
    }

    // 验证目标大小范围 (10KB - 2MB)
    if ($targetSize < 10 || $targetSize > 2048) {
        throw new Exception('目标大小必须在10KB-2MB之间');
    }

    // 解码base64图片数据
    if (strpos($imageData, 'data:image/') === 0) {
        $imageData = substr($imageData, strpos($imageData, ',') + 1);
    }

    $decodedImage = base64_decode($imageData);
    if ($decodedImage === false) {
        throw new Exception('图片数据解码失败');
    }

    // 创建临时文件
    $tempFile = tempnam(sys_get_temp_dir(), 'compress_');
    file_put_contents($tempFile, $decodedImage);

    // 使用GD库进行图片压缩处理
    $compressedImage = compressImageToSize($tempFile, $targetSize, $format);

    // 清理临时文件
    unlink($tempFile);

    if ($compressedImage === false) {
        throw new Exception('图片压缩失败');
    }

    // 准备返回结果
    $result = [
        'success' => true,
        'data' => [
            'compressedImage' => 'data:image/' . $format . ';base64,' . base64_encode($compressedImage),
            'originalSize' => strlen($decodedImage),
            'compressedSize' => strlen($compressedImage),
            'compressionRatio' => round((1 - strlen($compressedImage) / strlen($decodedImage)) * 100, 1)
        ]
    ];

    // 记录API调用统计
    $responseSize = strlen(json_encode($result));
    logApiUsage('image_compress', 'GD_Compress', $startTime, true, null, $requestSize, $responseSize, null);

    // 返回压缩后的图片
    echo json_encode($result);

} catch (Exception $e) {
    $isSuccess = false;
    $errorMessage = $e->getMessage();

    http_response_code(400);

    $result = [
        'success' => false,
        'error' => $e->getMessage()
    ];

    // 记录API调用统计（失败）
    $responseSize = strlen(json_encode($result));
    logApiUsage('image_compress', 'GD_Compress', $startTime, false, $errorMessage, $requestSize, $responseSize, null);

    echo json_encode($result);
}

/**
 * 压缩图片到指定大小
 */
function compressImageToSize($imagePath, $targetSizeKB, $format = 'jpg') {
    // 获取图片信息
    $imageInfo = getimagesize($imagePath);
    if (!$imageInfo) {
        return false;
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $mimeType = $imageInfo['mime'];
    
    // 创建图片资源
    switch ($mimeType) {
        case 'image/jpeg':
            $image = imagecreatefromjpeg($imagePath);
            break;
        case 'image/png':
            $image = imagecreatefrompng($imagePath);
            break;
        case 'image/gif':
            $image = imagecreatefromgif($imagePath);
            break;
        case 'image/webp':
            $image = imagecreatefromwebp($imagePath);
            break;
        default:
            return false;
    }
    
    if (!$image) {
        return false;
    }
    
    // 目标文件大小（字节）
    $targetSizeBytes = $targetSizeKB * 1024;
    
    // 初始质量设置
    $quality = 95;
    $minQuality = 10;
    $maxIterations = 10;
    $iteration = 0;
    
    do {
        ob_start();
        
        if ($format === 'jpg' || $format === 'jpeg') {
            imagejpeg($image, null, $quality);
        } else if ($format === 'png') {
            // PNG使用压缩级别 (0-9)
            $pngQuality = 9 - round(($quality / 100) * 9);
            imagepng($image, null, $pngQuality);
        } else if ($format === 'webp') {
            imagewebp($image, null, $quality);
        }
        
        $imageData = ob_get_contents();
        ob_end_clean();
        
        $currentSize = strlen($imageData);
        
        // 如果当前大小符合要求，返回结果
        if ($currentSize <= $targetSizeBytes || $quality <= $minQuality) {
            break;
        }
        
        // 根据当前大小调整质量
        $ratio = $targetSizeBytes / $currentSize;
        $quality = max($minQuality, round($quality * $ratio * 0.9)); // 稍微保守一点
        
        $iteration++;
        
    } while ($iteration < $maxIterations);
    
    // 如果还是太大，尝试缩小尺寸
    if (strlen($imageData) > $targetSizeBytes && $iteration >= $maxIterations) {
        $scale = sqrt($targetSizeBytes / strlen($imageData));
        $newWidth = round($width * $scale);
        $newHeight = round($height * $scale);
        
        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // 保持透明度（PNG）
        if ($format === 'png') {
            imagealphablending($resizedImage, false);
            imagesavealpha($resizedImage, true);
            $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
            imagefill($resizedImage, 0, 0, $transparent);
        }
        
        imagecopyresampled($resizedImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        ob_start();
        if ($format === 'jpg' || $format === 'jpeg') {
            imagejpeg($resizedImage, null, 85);
        } else if ($format === 'png') {
            imagepng($resizedImage, null, 6);
        } else if ($format === 'webp') {
            imagewebp($resizedImage, null, 85);
        }
        $imageData = ob_get_contents();
        ob_end_clean();
        
        imagedestroy($resizedImage);
    }
    
    imagedestroy($image);
    
    return $imageData;
}
?>
