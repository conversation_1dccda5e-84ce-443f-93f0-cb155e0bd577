<?php
/**
 * 智能证件照优化API
 * 自动检测照片质量，判断是否需要优化，并进行智能裁剪和调整
 */

// 引入配置文件
require_once __DIR__ . '/config.php';
// 引入API统计记录器
require_once __DIR__ . '/api_stats_logger.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => -1,
        'message' => '只支持POST请求'
    ]);
    exit();
}

$startTime = microtime(true);
$requestSize = strlen(file_get_contents('php://input'));
$isSuccess = true;
$errorMessage = null;

try {
    // 检查智能优化功能是否启用
    if (!defined('SMART_OPTIMIZER_ENABLED') || !SMART_OPTIMIZER_ENABLED) {
        throw new Exception('智能优化功能未启用');
    }

    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data || !isset($data['image'])) {
        throw new Exception('缺少图片数据');
    }

    $imageBase64 = $data['image'];
    $targetSize = isset($data['targetSize']) ? $data['targetSize'] : '1inch';

    // 验证base64图片数据
    if (empty($imageBase64)) {
        throw new Exception('图片数据为空');
    }

    // 检查图片大小
    if (strlen($imageBase64) > 10 * 1024 * 1024) {
        throw new Exception('图片过大，请选择小于8MB的图片');
    }

    // 分析照片质量和类型
    $photoAnalysis = analyzePhotoQuality($imageBase64);
    
    // 根据分析结果决定是否需要优化
    $needsOptimization = $photoAnalysis['needsOptimization'];
    $optimizedImage = $imageBase64;
    
    if ($needsOptimization) {
        // 进行智能优化
        $optimizedImage = optimizePhoto($imageBase64, $photoAnalysis, $targetSize);
    }

    // 准备返回结果
    $response = [
        'code' => 0,
        'message' => '处理成功',
        'data' => [
            'originalImage' => $imageBase64,
            'optimizedImage' => $optimizedImage,
            'needsOptimization' => $needsOptimization,
            'analysis' => $photoAnalysis,
            'optimizationApplied' => $needsOptimization
        ]
    ];

    // 记录API调用统计
    $responseSize = strlen(json_encode($response));
    logApiUsage('smart_photo_optimizer', 'OptimizePhoto', $startTime, true, null, $requestSize, $responseSize);

    // 返回成功结果
    echo json_encode($response);

} catch (Exception $e) {
    $isSuccess = false;
    $errorMessage = $e->getMessage();

    // 准备返回结果
    $response = [
        'code' => -1,
        'message' => $e->getMessage()
    ];

    // 记录API调用统计（失败）
    $responseSize = strlen(json_encode($response));
    logApiUsage('smart_photo_optimizer', 'OptimizePhoto', $startTime, false, $errorMessage, $requestSize, $responseSize);

    // 返回错误信息
    echo json_encode($response);
}

/**
 * 分析照片质量和类型
 */
function analyzePhotoQuality($imageBase64) {
    try {
        // 解码base64图片
        $imageData = base64_decode($imageBase64);
        if ($imageData === false) {
            throw new Exception('图片数据解码失败');
        }

        // 创建图片资源
        $image = imagecreatefromstring($imageData);
        if ($image === false) {
            throw new Exception('无法创建图片资源');
        }

        $width = imagesx($image);
        $height = imagesy($image);
        
        // 检测人脸位置和大小
        $faceDetection = detectFacePosition($image, $width, $height);
        
        // 分析照片比例，防止除零错误
        if ($height <= 0) {
            throw new Exception('图片高度无效');
        }
        $aspectRatio = $width / $height;
        $isPortrait = $aspectRatio < 1.0;
        
        // 判断是否是标准证件照比例（放宽标准）
        $standardRatios = [
            '1inch' => 295/413,    // 1寸照片比例 ≈ 0.714
            '2inch' => 413/579,    // 2寸照片比例 ≈ 0.713
            'passport' => 390/567, // 护照照片比例 ≈ 0.688
            'common' => 2/3        // 常见证件照比例 ≈ 0.667
        ];

        $isStandardRatio = false;
        $matchedRatio = null;
        foreach ($standardRatios as $name => $ratio) {
            if (abs($aspectRatio - $ratio) < 0.15) { // 放宽容差到15%
                $isStandardRatio = true;
                $matchedRatio = $name;
                break;
            }
        }
        
        // 判断是否需要优化
        $needsOptimization = false;
        $reasons = [];

        // 如果是标准证件照且人脸检测成功，优先考虑不优化
        $isLikelyStandardPhoto = $isStandardRatio && $isPortrait && $faceDetection['found'];
        
        // 检查人脸位置是否合适
        if ($faceDetection['found']) {
            $faceX = $faceDetection['x'];
            $faceY = $faceDetection['y'];
            $faceWidth = $faceDetection['width'];
            $faceHeight = $faceDetection['height'];
            
            // 计算人脸中心点
            $faceCenterX = $faceX + $faceWidth / 2;
            $faceCenterY = $faceY + $faceHeight / 2;
            
            // 检查人脸是否在图片中心区域
            $centerX = $width / 2;
            $centerY = $height / 2;
            
            // 防止除零错误
            if ($width <= 0 || $height <= 0) {
                throw new Exception('图片尺寸无效');
            }

            $horizontalDeviation = abs($faceCenterX - $centerX) / $width;
            $verticalDeviation = abs($faceCenterY - $centerY) / $height;
            
            // 对于标准证件照，使用更宽松的标准
            $horizontalThreshold = $isStandardRatio ? 0.25 : 0.15; // 标准证件照允许25%偏差
            $verticalThreshold = $isStandardRatio ? 0.20 : 0.10;   // 标准证件照允许20%偏差

            // 如果人脸偏离中心太多，需要优化
            if ($horizontalDeviation > $horizontalThreshold) {
                $needsOptimization = true;
                $reasons[] = '人脸水平位置偏离中心';
            }

            if ($verticalDeviation > $verticalThreshold) {
                $needsOptimization = true;
                $reasons[] = '人脸垂直位置偏离中心';
            }
            
            // 检查人脸大小是否合适（人脸应该占图片高度的60-80%）
            // 防止除零错误
            if ($height <= 0) {
                throw new Exception('图片高度无效');
            }
            $faceHeightRatio = $faceHeight / $height;

            // 对于标准证件照，使用更宽松的人脸大小标准
            $minFaceRatio = $isStandardRatio ? 0.25 : 0.4;  // 标准证件照最小25%
            $maxFaceRatio = $isStandardRatio ? 1.0 : 0.9;   // 标准证件照最大100%

            if ($faceHeightRatio < $minFaceRatio || $faceHeightRatio > $maxFaceRatio) {
                $needsOptimization = true;
                $reasons[] = '人脸大小不合适';
            }
        } else {
            $needsOptimization = true;
            $reasons[] = '未检测到人脸';
        }
        
        // 检查图片比例
        if (!$isStandardRatio && !$isPortrait) {
            $needsOptimization = true;
            $reasons[] = '图片比例不是证件照标准比例';
        }
        
        // 检查图片尺寸
        if ($width < 200 || $height < 200) {
            $needsOptimization = true;
            $reasons[] = '图片尺寸过小';
        }

        // 最终判断：如果是疑似标准证件照且没有严重问题，就不优化
        if ($isLikelyStandardPhoto && count($reasons) <= 1) {
            $needsOptimization = false;
            $reasons = ['已是标准证件照，无需优化'];
        }

        imagedestroy($image);
        
        return [
            'needsOptimization' => $needsOptimization,
            'reasons' => $reasons,
            'faceDetection' => $faceDetection,
            'dimensions' => ['width' => $width, 'height' => $height],
            'aspectRatio' => $aspectRatio,
            'isStandardRatio' => $isStandardRatio,
            'isPortrait' => $isPortrait
        ];
        
    } catch (Exception $e) {
        return [
            'needsOptimization' => false,
            'reasons' => ['分析失败: ' . $e->getMessage()],
            'faceDetection' => ['found' => false],
            'dimensions' => ['width' => 0, 'height' => 0],
            'aspectRatio' => 1.0,
            'isStandardRatio' => false,
            'isPortrait' => false
        ];
    }
}

/**
 * 检测人脸位置（重新设计的算法）
 */
function detectFacePosition($image, $width, $height) {
    try {
        // 重新设计的人脸检测算法
        // 1. 首先在图片上半部分寻找最可能的面部区域
        $faceRegions = [];
        $sampleStep = max(3, min($width, $height) / 60);

        // 只检测图片上半部分，避免被身体其他部位误导
        $searchStartY = 0;
        $searchEndY = $height * 0.6; // 只检测上60%的区域
        $searchStartX = $width * 0.1; // 左右各留10%边距
        $searchEndX = $width * 0.9;

        // 分块检测，寻找肤色密集区域
        $blockSize = min($width, $height) / 10;

        for ($blockY = $searchStartY; $blockY < $searchEndY; $blockY += $blockSize) {
            for ($blockX = $searchStartX; $blockX < $searchEndX; $blockX += $blockSize) {
                $skinCount = 0;
                $totalPixels = 0;

                // 在每个块内采样
                for ($x = $blockX; $x < min($blockX + $blockSize, $searchEndX); $x += $sampleStep) {
                    for ($y = $blockY; $y < min($blockY + $blockSize, $searchEndY); $y += $sampleStep) {
                        $rgb = imagecolorat($image, $x, $y);
                        $r = ($rgb >> 16) & 0xFF;
                        $g = ($rgb >> 8) & 0xFF;
                        $b = $rgb & 0xFF;

                        $totalPixels++;
                        if (isFaceColor($r, $g, $b)) {
                            $skinCount++;
                        }
                    }
                }

                // 计算肤色密度
                $skinDensity = $totalPixels > 0 ? $skinCount / $totalPixels : 0;

                // 如果肤色密度足够高，认为是潜在的面部区域
                if ($skinDensity > 0.3) {
                    $faceRegions[] = [
                        'x' => $blockX,
                        'y' => $blockY,
                        'width' => min($blockSize, $searchEndX - $blockX),
                        'height' => min($blockSize, $searchEndY - $blockY),
                        'density' => $skinDensity,
                        'centerX' => $blockX + $blockSize / 2,
                        'centerY' => $blockY + $blockSize / 2
                    ];
                }
            }
        }

        if (empty($faceRegions)) {
            return ['found' => false];
        }

        // 找到密度最高且位置最合理的区域
        usort($faceRegions, function($a, $b) use ($height) {
            // 优先选择密度高且位置在上半部分的区域
            // 防止除零错误
            if ($height <= 0) {
                return 0;
            }
            $scoreA = $a['density'] * (1 - $a['centerY'] / $height); // 越靠上分数越高
            $scoreB = $b['density'] * (1 - $b['centerY'] / $height);
            return $scoreB <=> $scoreA;
        });

        $bestRegion = $faceRegions[0];

        // 基于最佳区域，精确定位面部边界
        $faceArea = refineFaceArea($image, $bestRegion, $width, $height);

        if (!$faceArea) {
            return ['found' => false];
        }

        // 扩展到完整的头部区域（包括头发）
        $faceWidth = $faceArea['maxX'] - $faceArea['minX'];
        $faceHeight = $faceArea['maxY'] - $faceArea['minY'];
        $faceCenterX = ($faceArea['minX'] + $faceArea['maxX']) / 2;
        $faceCenterY = ($faceArea['minY'] + $faceArea['maxY']) / 2;

        // 头部区域应该比面部大，并且向上扩展以包含头发
        $headWidth = $faceWidth * 1.3;
        $headHeight = $faceHeight * 1.6;

        // 头部中心比面部中心稍微向上
        $headCenterX = $faceCenterX;
        $headCenterY = $faceCenterY - $faceHeight * 0.15;

        // 计算最终的头部边界
        $headX = max(0, $headCenterX - $headWidth / 2);
        $headY = max(0, $headCenterY - $headHeight / 2);

        // 确保不超出图片边界
        $headWidth = min($headWidth, $width - $headX);
        $headHeight = min($headHeight, $height - $headY);

        return [
            'found' => true,
            'x' => $headX,
            'y' => $headY,
            'width' => $headWidth,
            'height' => $headHeight,
            'faceCenter' => ['x' => $faceCenterX, 'y' => $faceCenterY],
            'faceBounds' => $faceArea,
            'confidence' => $bestRegion['density']
        ];

    } catch (Exception $e) {
        return ['found' => false];
    }
}

/**
 * 专门用于面部检测的肤色识别（更严格的标准）
 */
function isFaceColor($r, $g, $b) {
    // 更严格的面部肤色检测，避免被身体其他部位误导

    // 方法1：经典的面部肤色检测
    $method1 = ($r > 95 && $g > 40 && $b > 20 &&
                $r > $g && $r > $b &&
                abs($r - $g) > 15 &&
                max($r, $g, $b) - min($r, $g, $b) > 15);

    // 方法2：YCrCb色彩空间（针对面部优化）
    $y = 0.299 * $r + 0.587 * $g + 0.114 * $b;
    $cr = 0.713 * ($r - $y) + 128;
    $cb = 0.564 * ($b - $y) + 128;

    // 面部肤色的YCrCb范围更严格
    $method2 = ($y > 80 && $y < 230 &&
                $cr >= 135 && $cr <= 180 &&
                $cb >= 85 && $cb <= 135);

    // 方法3：排除过暗或过亮的区域
    $brightness = ($r + $g + $b) / 3;
    $method3 = ($brightness > 80 && $brightness < 220);

    // 所有方法都要通过才认为是面部肤色
    return $method1 && $method2 && $method3;
}

/**
 * 精确定位面部区域
 */
function refineFaceArea($image, $region, $width, $height) {
    $startX = max(0, $region['x'] - $region['width'] * 0.5);
    $endX = min($width, $region['x'] + $region['width'] * 1.5);
    $startY = max(0, $region['y'] - $region['height'] * 0.5);
    $endY = min($height, $region['y'] + $region['height'] * 1.5);

    $facePixels = [];
    $step = max(2, min($width, $height) / 100);

    // 在扩展区域内精确寻找面部像素
    for ($x = $startX; $x < $endX; $x += $step) {
        for ($y = $startY; $y < $endY; $y += $step) {
            $rgb = imagecolorat($image, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;

            if (isFaceColor($r, $g, $b)) {
                $facePixels[] = ['x' => $x, 'y' => $y];
            }
        }
    }

    if (count($facePixels) < 10) {
        return false;
    }

    // 计算面部区域的精确边界
    $minX = min(array_column($facePixels, 'x'));
    $maxX = max(array_column($facePixels, 'x'));
    $minY = min(array_column($facePixels, 'y'));
    $maxY = max(array_column($facePixels, 'y'));

    // 验证面部区域的合理性
    $faceWidth = $maxX - $minX;
    $faceHeight = $maxY - $minY;

    // 防止除零错误
    if ($faceWidth <= 0 || $faceHeight <= 0) {
        return false;
    }

    $aspectRatio = $faceWidth / $faceHeight;

    // 面部宽高比应该在合理范围内
    if ($aspectRatio < 0.5 || $aspectRatio > 1.5) {
        return false;
    }

    // 面部不应该太小
    if ($faceWidth < $width * 0.1 || $faceHeight < $height * 0.1) {
        return false;
    }

    return [
        'minX' => $minX,
        'maxX' => $maxX,
        'minY' => $minY,
        'maxY' => $maxY
    ];
}

/**
 * 保留原有的肤色检测函数（用于兼容性）
 */
function isSkinColor($r, $g, $b) {
    return isFaceColor($r, $g, $b);
}

/**
 * 找到主要的肤色聚类区域
 */
function findMainSkinCluster($skinPixels, $width, $height) {
    if (count($skinPixels) < 10) {
        return [];
    }

    // 简单的聚类算法：找到密度最高的区域
    $gridSize = min($width, $height) / 20; // 网格大小
    $grid = [];

    // 将像素点分配到网格中
    foreach ($skinPixels as $pixel) {
        $gridX = floor($pixel['x'] / $gridSize);
        $gridY = floor($pixel['y'] / $gridSize);
        $key = $gridX . '_' . $gridY;

        if (!isset($grid[$key])) {
            $grid[$key] = [];
        }
        $grid[$key][] = $pixel;
    }

    // 找到像素点最多的网格区域
    $maxCount = 0;
    $mainGridKey = null;

    foreach ($grid as $key => $pixels) {
        if (count($pixels) > $maxCount) {
            $maxCount = count($pixels);
            $mainGridKey = $key;
        }
    }

    if ($mainGridKey === null) {
        return $skinPixels; // 如果没有明显的聚类，返回所有像素
    }

    // 获取主要区域及其邻近区域的所有像素
    list($mainGridX, $mainGridY) = explode('_', $mainGridKey);
    $mainCluster = [];

    for ($dx = -2; $dx <= 2; $dx++) {
        for ($dy = -2; $dy <= 2; $dy++) {
            $neighborKey = ($mainGridX + $dx) . '_' . ($mainGridY + $dy);
            if (isset($grid[$neighborKey])) {
                $mainCluster = array_merge($mainCluster, $grid[$neighborKey]);
            }
        }
    }

    return $mainCluster;
}

/**
 * 智能优化照片
 */
function optimizePhoto($imageBase64, $analysis, $targetSize) {
    try {
        // 解码base64图片
        $imageData = base64_decode($imageBase64);
        if ($imageData === false) {
            throw new Exception('图片数据解码失败');
        }

        // 创建图片资源
        $image = imagecreatefromstring($imageData);
        if ($image === false) {
            throw new Exception('无法创建图片资源');
        }

        $width = imagesx($image);
        $height = imagesy($image);

        // 获取目标尺寸配置
        $targetDimensions = getTargetDimensions($targetSize);

        // 如果检测到人脸，进行智能裁剪
        if ($analysis['faceDetection']['found']) {
            $optimizedImage = smartCropWithFace($image, $analysis['faceDetection'], $targetDimensions);
        } else {
            // 没有检测到人脸，进行中心裁剪
            $optimizedImage = centerCrop($image, $targetDimensions);
        }

        // 应用图片增强
        $enhancedImage = enhanceImage($optimizedImage);

        // 输出为base64
        ob_start();
        $quality = defined('SMART_OPTIMIZER_QUALITY') ? SMART_OPTIMIZER_QUALITY : 95;
        imagejpeg($enhancedImage, null, $quality);
        $optimizedData = ob_get_contents();
        ob_end_clean();

        // 清理资源
        imagedestroy($image);
        imagedestroy($optimizedImage);
        imagedestroy($enhancedImage);

        return base64_encode($optimizedData);

    } catch (Exception $e) {
        throw new Exception('照片优化失败: ' . $e->getMessage());
    }
}

/**
 * 获取目标尺寸配置
 */
function getTargetDimensions($targetSize) {
    $dimensions = [
        '1inch' => ['width' => 295, 'height' => 413],
        '2inch' => ['width' => 413, 'height' => 579],
        'small2inch' => ['width' => 413, 'height' => 531],
        'passport' => ['width' => 390, 'height' => 567]
    ];

    return isset($dimensions[$targetSize]) ? $dimensions[$targetSize] : $dimensions['1inch'];
}

/**
 * 基于人脸位置的智能裁剪（优化版）
 */
function smartCropWithFace($image, $faceDetection, $targetDimensions) {
    $width = imagesx($image);
    $height = imagesy($image);

    $headX = $faceDetection['x'];
    $headY = $faceDetection['y'];
    $headWidth = $faceDetection['width'];
    $headHeight = $faceDetection['height'];

    // 计算头部中心点
    $headCenterX = $headX + $headWidth / 2;
    $headCenterY = $headY + $headHeight / 2;

    // 防止除零错误
    if ($targetDimensions['height'] <= 0 || $headHeight <= 0) {
        throw new Exception('无效的尺寸参数');
    }

    // 计算目标比例
    $targetRatio = $targetDimensions['width'] / $targetDimensions['height'];

    // 证件照标准优化（基于实际测试调整）：
    // - 头部（包含头发）应该占画面高度的60-70%
    // - 头顶留白应该占20-25%（确保头发和头顶完全不被裁掉）
    // - 下巴留白应该占10-15%
    $headToPhotoRatio = 0.65;  // 头部占照片高度的65%（减少比例，增加留白）
    $topMarginRatio = 0.22;    // 头顶留白22%（进一步增加留白）
    $bottomMarginRatio = 0.13; // 下巴留白13%

    // 根据头部大小计算理想的照片高度
    $idealPhotoHeight = $headHeight / $headToPhotoRatio;
    $idealPhotoWidth = $idealPhotoHeight * $targetRatio;

    // 确保裁剪区域不超出原图边界，如果超出则按比例缩小
    $cropWidth = $idealPhotoWidth;
    $cropHeight = $idealPhotoHeight;

    // 检查是否超出边界并调整
    if ($cropWidth > $width || $cropHeight > $height) {
        // 按原图尺寸限制，保持目标比例
        // 防止除零错误
        if ($height <= 0) {
            throw new Exception('图片高度无效');
        }

        if ($width / $height > $targetRatio) {
            // 原图更宽，以高度为准
            $cropHeight = $height;
            $cropWidth = $height * $targetRatio;
        } else {
            // 原图更高，以宽度为准
            $cropWidth = $width;
            $cropHeight = $width / $targetRatio;
        }
    }

    // 计算裁剪位置
    // 水平居中：以头部中心为基准
    $cropX = $headCenterX - $cropWidth / 2;
    $cropX = max(0, min($width - $cropWidth, $cropX));

    // 垂直位置：确保头顶有足够留白
    $idealTopMargin = $cropHeight * $topMarginRatio;

    // 重新计算裁剪位置，确保头部完全包含且有足够留白
    // 方法：从检测到的头部区域开始，向上扩展足够的空间

    // 首先确保头部区域完全在裁剪范围内
    $safeTopMargin = $cropHeight * 0.15; // 最小15%的安全留白
    $maxTopMargin = $idealTopMargin;

    // 计算可用的头顶空间
    $availableTopSpace = $headY; // 头部顶部到图片顶部的距离

    // 根据可用空间调整留白
    $actualTopMargin = min($maxTopMargin, max($safeTopMargin, $availableTopSpace * 0.8));

    // 计算裁剪区域的顶部位置
    $cropY = $headY - $actualTopMargin;
    $cropY = max(0, $cropY); // 确保不超出图片顶部

    // 检查底部是否超出边界
    if ($cropY + $cropHeight > $height) {
        // 如果超出底部，从底部向上调整
        $cropY = $height - $cropHeight;
        $cropY = max(0, $cropY);
    }

    // 最终验证：确保头部区域在裁剪范围内
    $headTop = $headY;
    $headBottom = $headY + $headHeight;
    $cropTop = $cropY;
    $cropBottom = $cropY + $cropHeight;

    // 如果头部仍然被裁掉，优先保证头部完整
    if ($headTop < $cropTop || $headBottom > $cropBottom) {
        // 重新计算，以头部为中心，确保完全包含
        $headCenterY = $headY + $headHeight / 2;
        $cropY = $headCenterY - $cropHeight / 2;

        // 调整到合理位置
        $cropY = max(0, min($height - $cropHeight, $cropY));

        // 如果还是不行，说明原图太小，按比例缩小裁剪区域
        if ($headTop < $cropY || $headBottom > $cropY + $cropHeight) {
            $requiredHeight = $headHeight * 1.5; // 头部高度的1.5倍作为最小裁剪高度
            if ($requiredHeight < $cropHeight) {
                $cropHeight = $requiredHeight;
                $cropWidth = $cropHeight * $targetRatio;
                $cropY = max(0, $headY - $cropHeight * 0.2); // 头部上方留20%空间
            }
        }
    }

    // 创建裁剪后的图片
    $croppedImage = imagecreatetruecolor($cropWidth, $cropHeight);
    imageantialias($croppedImage, true);

    imagecopyresampled(
        $croppedImage, $image,
        0, 0, $cropX, $cropY,
        $cropWidth, $cropHeight, $cropWidth, $cropHeight
    );

    // 缩放到目标尺寸
    $finalImage = imagecreatetruecolor($targetDimensions['width'], $targetDimensions['height']);
    imageantialias($finalImage, true);

    imagecopyresampled(
        $finalImage, $croppedImage,
        0, 0, 0, 0,
        $targetDimensions['width'], $targetDimensions['height'],
        $cropWidth, $cropHeight
    );

    imagedestroy($croppedImage);

    return $finalImage;
}

/**
 * 中心裁剪（当没有检测到人脸时）
 */
function centerCrop($image, $targetDimensions) {
    $width = imagesx($image);
    $height = imagesy($image);

    // 防止除零错误
    if ($targetDimensions['height'] <= 0 || $height <= 0) {
        throw new Exception('无效的图片或目标尺寸');
    }

    $targetRatio = $targetDimensions['width'] / $targetDimensions['height'];
    $sourceRatio = $width / $height;

    // 计算裁剪区域
    if ($sourceRatio > $targetRatio) {
        // 原图更宽，需要裁剪宽度
        $cropHeight = $height;
        $cropWidth = $height * $targetRatio;
        $cropX = ($width - $cropWidth) / 2;
        $cropY = 0;
    } else {
        // 原图更高，需要裁剪高度
        $cropWidth = $width;
        $cropHeight = $width / $targetRatio;
        $cropX = 0;
        $cropY = ($height - $cropHeight) / 2;
    }

    // 创建裁剪后的图片
    $croppedImage = imagecreatetruecolor($cropWidth, $cropHeight);
    imageantialias($croppedImage, true);

    imagecopyresampled(
        $croppedImage, $image,
        0, 0, $cropX, $cropY,
        $cropWidth, $cropHeight, $cropWidth, $cropHeight
    );

    // 缩放到目标尺寸
    $finalImage = imagecreatetruecolor($targetDimensions['width'], $targetDimensions['height']);
    imageantialias($finalImage, true);

    imagecopyresampled(
        $finalImage, $croppedImage,
        0, 0, 0, 0,
        $targetDimensions['width'], $targetDimensions['height'],
        $cropWidth, $cropHeight
    );

    imagedestroy($croppedImage);

    return $finalImage;
}

/**
 * 图片增强处理
 */
function enhanceImage($image) {
    $width = imagesx($image);
    $height = imagesy($image);

    // 创建增强后的图片
    $enhancedImage = imagecreatetruecolor($width, $height);
    imageantialias($enhancedImage, true);

    // 复制原图
    imagecopy($enhancedImage, $image, 0, 0, 0, 0, $width, $height);

    // 轻微增强对比度
    imagefilter($enhancedImage, IMG_FILTER_CONTRAST, 5);

    // 轻微锐化
    $sharpenMatrix = [
        [0, -1, 0],
        [-1, 5, -1],
        [0, -1, 0]
    ];
    imageconvolution($enhancedImage, $sharpenMatrix, 1, 0);

    // 减少噪点
    imagefilter($enhancedImage, IMG_FILTER_SMOOTH, 1);

    return $enhancedImage;
}

?>
