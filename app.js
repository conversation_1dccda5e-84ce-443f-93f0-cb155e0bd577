// app.js
App({
  onLaunch() {
    // 版本更新提示
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(() => {
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否立即重启应用？',
            success: (res) => {
              if (res.confirm) {
                updateManager.applyUpdate();
              }
            }
          });
        });
        updateManager.onUpdateFailed(() => {
          wx.showToast({ title: '更新失败', icon: 'none' });
        });
      }
    });

    // 设置web-view的CORS策略
    if (wx.setWebViewCORS) {
      wx.setWebViewCORS({
        enable: true,
        domain: '*'
      });
    }
  },

  // 全局Base64解码方法（重要修正）
  decodeBase64(base64Str) {
    try {
      const arrayBuffer = wx.base64ToArrayBuffer(base64Str);
      const uint8Array = new Uint8Array(arrayBuffer);
      let decodedString = '';
      for (let i = 0; i < uint8Array.length; i++) {
        decodedString += String.fromCharCode(uint8Array[i]);
      }
      return decodedString;
    } catch (e) {
      console.error('Base64解码失败:', e);
      return '';
    }
  },

  icons: {
    arrowDown: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABmJLR0QA/wD/AP+gvaeTAAABKklEQVRYhe2WQQ6DMAxEfRIn7v9kDlD1S1U6aZsYt6rUJQshxWOPA4zMjPwqgD4xYwWwAVjH9F7gKXwj6wQe9x5wHxN3e0sFfDkA4O4Cb2tLJx7yAI8jHhCwvV0K4NMBALdXUoEAD3hAwNZqKhDgAQ8I2FJNBYI84AEBm6upQKAHPCBgYzUVCPaABwRsrKYCiTzgAQHrqqlAsgc8IGBVNRVI9oAHBKyppgIFHvCAgBXVVKDQAx4Q0F5NBQo94AEBrdVUoMADHhDQVk0FJnigEwJaqqnAJA90QkCpmgpM9EAnBExXU4HJHuiEgKlqKlDgAQ8ImKqmAkUe8ICA4WoqUOgBDwgYqqYCxR7wgIDuaipQ4QEPCOiqpgJVHvCAgM5qKlDpAQ8I6KqmAtUe8ICA5moq0OABDwj4WE0FGj3gAQF/qx3qBQh4mX8O3yWvAAAAAElFTkSuQmCC',
    calendar: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAABmJLR0QA/wD/AP+gvaeTAAABkUlEQVRYhe2XzU7CQBSGv6EFFPkBQRRQ8N24ceML+AA+hA/gQ7j2AXwJd8aNiYkLExNjoiYqoEALtKVQoFgXpYVCO23BxH7JSWbamTnfmTlzZkZoms1m+xe4A8Dtdq8Aq0BQdKxYLL4Dd8C1qqp3AEqpVHp3uVwbgB+IiI4XCgUAEsA1cKmq6q0BqGnafrPZPAcOAJ/o+HA4DABXAHEgViqV3gCUSqWiOZ3OQyAC7IuOFwqFbwBx4Bw4V1X1xgBUVXU/Go2eAEdASHS8UCgA4kAMiBYKhVcARdM0zel0HgHHwK7oeD6f/wYQB84URTk3AFVV3Q+HwyfAKbAlOp7L5b4AxIBTo+oGoKZpB5FI5BQ4A7ZFx7PZ7BeAC+A4n8+/ACiFQuHN4XAcAifAnuh4Npv9BBAFjnK53DOAkkgkXux2+z6wB2yLjmcymU8AF8BROp1+BFAkSXqx2Wx7wC6wIzqez+cNwP10Ov0EoEiS9Gyz2XaBHWBXdDydThuA+6lU6hFAKRaLr1ardRfYBnZFx9PptAF4kEwmnwAUWZZfLRbLDrAF7ImOp1IpA/AgkUg8AiiyLL9ZLJZtYBPYEx1PJpMG4GE8Hn8AUGRZfrdYLFvAJrAvOp5IJAzAo1gsdg+glEqlD7PZvAlsABui4/F43AA8jsVi9wBKpVL5NJtNm8A6sC46Ho/HDcCTaDR6B6DUarUvk8m0AawBa6Lj8XjcADyNRqO3AEq9Xv82mUzrwCqwKjoei8UMwLNoNHoDoDQajabRaFwDVoAV0fFoNGoAnl9dXd0AKM1ms2UwGNaAZWBJdPzy8tIAPL+8vLwGUFqtVttgMKwCS8Ci6PjFxcUP4MXFxdUvgNJoNFp6vX4FWAAWRMcvLy9/AC8vL68BKK1Wq63X65eBeWBBdPz8/PwHMBqNXgMo7Xa7o9PploA5YF50/AdiD3i0H1a1bAAAAABJRU5ErkJggg=='
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '平台常用计算工具',
      path: '/pages/index/index'
    };
  }
});