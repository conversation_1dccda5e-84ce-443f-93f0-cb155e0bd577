/* 通用容器样式 */
.container {
  padding: 30rpx;
}

/* 返回按钮样式 */
.back-btn {
  display: block;
  padding: 20rpx;
  color: #2196F3;
  margin-bottom: 40rpx;
}

/* 输入组样式 */
.input-group {
  margin-bottom: 30rpx;
}

.input-group text {
  display: block;
  margin-bottom: 10rpx;
  color: #333;
}

input {
  border: 1rpx solid #ddd;
  padding: 20rpx;
  border-radius: 8rpx;
  width: 100%;
}

button {
  background: #2196F3;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 40rpx;
}

/* 结果样式 */
.result {
  margin-top: 30rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.qualified { color: #4CAF50; }
.unqualified { color: #F44336; }