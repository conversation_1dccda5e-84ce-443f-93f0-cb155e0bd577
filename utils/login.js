/**
 * 微信小程序登录功能
 * 基于shili示例代码集成
 */

// 配置API基础URL
const API_BASE_URL = 'https://sunxiyue.com/zdh/api/wechat_api.php';

// 调试模式
const DEBUG_MODE = true;

/**
 * 微信登录管理类
 */
class WechatLoginManager {
  constructor() {
    this.token = wx.getStorageSync('wechat_token') || '';
    this.userInfo = wx.getStorageSync('user_info') || null;
  }

  /**
   * 微信登录
   */
  async wechatLogin(getUserInfo = false) {
    try {
      // 1. 获取微信授权码
      const loginRes = await this.wxLogin();

      // 2. 获取用户信息（可选，只有在用户主动点击时才获取）
      let userInfo = null;
      if (getUserInfo) {
        try {
          userInfo = await this.getUserProfile();
        } catch (e) {
          // 用户取消授权，但仍然可以继续登录流程
        }
      }

      // 3. 调用后端登录接口
      const response = await this.callAPI('wechat_login', {
        code: loginRes.code,
        userInfo: userInfo
      });

      if (response.status === 'success') {
        // 登录成功，保存token和用户信息
        if (response.data.token) {
          this.token = response.data.token;
          wx.setStorageSync('wechat_token', this.token);
          wx.setStorageSync('token_expires_at', response.data.expires_at);
        }
        
        this.userInfo = response.data.user;
        wx.setStorageSync('user_info', this.userInfo);

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };

      } else if (response.status === 'need_bind') {
        // 需要绑定账号

        
        return {
          success: false,
          needBind: true,
          data: response.data
        };

      } else {
        throw new Error(response.message || '登录失败');
      }

    } catch (error) {
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 绑定系统账号
   */
  async bindAccount(bindData, username, password, retryCount = 0) {
    try {
      const response = await this.callAPI('bind_account', {
        openid: bindData.openid,
        wechat_user_id: bindData.wechat_user_id,
        username: username,
        password: password
      });

      if (response.status === 'success') {
        // 绑定成功，保存token和用户信息
        this.token = response.data.token;
        this.userInfo = response.data.user;
        
        wx.setStorageSync('wechat_token', this.token);
        wx.setStorageSync('user_info', this.userInfo);
        wx.setStorageSync('token_expires_at', response.data.expires_at);

        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        });

        return {
          success: true,
          data: response.data
        };
      } else {
        throw new Error(response.message || '绑定失败');
      }

    } catch (error) {

      // 处理特定错误类型
      let errorMessage = error.message || '绑定失败';

      // 检查是否为IP锁定错误
      if (errorMessage.includes('IP已锁定') || errorMessage.includes('分钟后再试')) {
        // IP锁定错误，显示详细的锁定信息弹窗
        wx.showModal({
          title: '绑定请求已锁定',
          content: errorMessage + '\n\n为了保护系统安全，连续错误5次后将锁定IP地址1小时。请稍后再试或联系管理员。',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#e74c3c'
        });

        return {
          success: false,
          error: errorMessage,
          type: 'ip_locked'
        };
      }

      // 处理数据库约束冲突错误
      if (errorMessage.includes('Duplicate entry') && errorMessage.includes('uk_user_wechat')) {
        if (retryCount === 0) {
          // 第一次遇到冲突，提示用户可以尝试清理后重试
          const self = this;
          wx.showModal({
            title: '绑定冲突',
            content: '检测到该微信账号存在历史绑定记录，是否尝试清理后重新绑定？',
            confirmText: '清理重试',
            cancelText: '取消',
            success: async function(res) {
              if (res.confirm) {
                // 尝试清理绑定记录后重试
                try {
                  await self.callAPI('clean_bind_conflict', {
                    openid: bindData.openid,
                    wechat_user_id: bindData.wechat_user_id
                  });

                  // 清理完成后重试绑定
                  setTimeout(async () => {
                    const retryResult = await self.bindAccount(bindData, username, password, 1);
                    if (!retryResult.success) {
                      wx.showToast({
                        title: '重试失败，请联系管理员',
                        icon: 'none',
                        duration: 3000
                      });
                    }
                  }, 1000);

                } catch (cleanError) {
                  wx.showToast({
                    title: '清理失败，请联系管理员',
                    icon: 'none',
                    duration: 3000
                  });
                }
              }
            }
          });
          return {
            success: false,
            error: '绑定冲突，等待用户选择'
          };
        } else {
          errorMessage = '绑定记录冲突仍然存在，请联系管理员手动处理';
        }
      }

      // 检查是否为用户名密码错误
      if (errorMessage.includes('用户名或密码错误')) {
        wx.showModal({
          title: '绑定失败',
          content: '用户名或密码错误，请检查后重试。\n\n注意：连续错误5次将锁定IP地址1小时。',
          showCancel: false,
          confirmText: '重新输入',
          confirmColor: '#3b82f6'
        });

        return {
          success: false,
          error: errorMessage,
          type: 'auth_error'
        };
      }

      // 检查其他HTTP错误
      if (errorMessage.includes('HTTP 400')) {
        errorMessage = '绑定请求失败，请检查用户名和密码是否正确';
      }

      // 其他错误使用Toast提示
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });

      return {
        success: false,
        error: errorMessage,
        type: 'general'
      };
    }
  }

  /**
   * 验证Token有效性
   */
  async verifyToken() {
    if (!this.token) {
      return false;
    }

    try {
      const response = await this.callAPI('verify_token', {
        token: this.token
      });

      if (response.status === 'success') {
        // 更新用户信息
        this.userInfo = response.data.user;
        wx.setStorageSync('user_info', this.userInfo);
        return true;
      } else {
        // Token无效，清除本地存储
        this.clearLoginData();
        return false;
      }

    } catch (error) {
      this.clearLoginData();
      return false;
    }
  }

  /**
   * 解绑账号
   */
  async unbindAccount(forceClean = false) {
    try {
      // 在解绑前先获取当前用户的openid信息
      let currentOpenid = null;
      if (this.token) {
        try {
          const tokenResult = await this.callAPI('verify_token', { token: this.token });
          if (tokenResult.status === 'success' && tokenResult.data.wechat) {
            currentOpenid = tokenResult.data.wechat.openid;
          }
        } catch (e) {
          // 获取openid失败，将尝试重新登录获取
        }
      }

      const response = await this.callAPI('unbind_account', {
        token: this.token,
        force_clean: forceClean ? 1 : 0  // 添加强制清理参数
      });

      if (response.status === 'success') {
        // 解绑成功后进入游客模式，保存openid信息
        this.switchToGuestMode(currentOpenid);
        wx.showToast({
          title: '解绑成功，已切换为游客模式',
          icon: 'success'
        });
        return true;
      } else {
        throw new Error(response.message || '解绑失败');
      }

    } catch (error) {

      // 如果是第一次解绑失败，提示用户可以尝试强制清理
      if (!forceClean && error.message && error.message.includes('绑定记录')) {
        const self = this;
        wx.showModal({
          title: '解绑失败',
          content: '检测到绑定记录异常，是否尝试强制清理？这将彻底清除所有绑定关系。',
          confirmText: '强制清理',
          cancelText: '取消',
          success: async function(res) {
            if (res.confirm) {
              // 递归调用，使用强制清理模式
              return await self.unbindAccount(true);
            }
          }
        });
        return false;
      }

      wx.showToast({
        title: error.message || '解绑失败',
        icon: 'none'
      });
      return false;
    }
  }

  /**
   * 切换到游客模式
   */
  switchToGuestMode(openid = null) {
    // 清除系统账号相关的数据
    this.token = '';
    this.userInfo = null;
    wx.removeStorageSync('wechat_token');
    wx.removeStorageSync('user_info');
    wx.removeStorageSync('token_expires_at');

    // 设置游客模式标识
    wx.setStorageSync('guest_mode', true);

    // 保存openid信息，用于后续重新绑定
    if (openid) {
      wx.setStorageSync('wechat_openid', openid);

    } else {
      // 如果没有提供openid，尝试重新获取
      this.refreshOpenidForGuest();
    }


  }

  /**
   * 为游客模式刷新openid
   */
  async refreshOpenidForGuest() {
    try {
      // 重新进行微信登录获取openid
      const loginRes = await this.wxLogin();
      const response = await this.callAPI('wechat_login', {
        code: loginRes.code,
        userInfo: null
      });

      if (response.status === 'need_bind' && response.data.openid) {
        wx.setStorageSync('wechat_openid', response.data.openid);
        wx.setStorageSync('wechat_user_id', response.data.wechat_user_id);

      }
    } catch (error) {
      // 刷新openid失败
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    return !!(this.token && this.userInfo);
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.userInfo;
  }

  /**
   * 正式退出登录（撤销服务器端Token）
   */
  async logout() {
    if (this.token) {
      try {
        await this.callAPI('logout', {
          token: this.token
        });
      } catch (error) {
        // 即使服务器端撤销失败，也要清除本地数据
      }
    }

    this.clearLoginData();

    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  }

  /**
   * 修改真实姓名
   */
  async changeRealName(newRealName) {
    try {
      const response = await this.callAPI('change_real_name', {
        token: this.token,
        real_name: newRealName
      });

      if (response.status === 'success') {
        // 更新本地用户信息
        if (this.userInfo) {
          this.userInfo.real_name = newRealName;
          wx.setStorageSync('user_info', this.userInfo);
        }

        wx.showToast({
          title: '真实姓名修改成功',
          icon: 'success'
        });

        return {
          success: true,
          message: '真实姓名修改成功'
        };
      } else {
        throw new Error(response.message || '修改失败');
      }
    } catch (error) {

      let errorMessage = error.message || '修改失败';
      if (errorMessage.includes('HTTP 400')) {
        errorMessage = '请求失败，请检查网络连接';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 清除登录数据（本地清理）
   */
  clearLoginData() {
    this.token = '';
    this.userInfo = null;
    wx.removeStorageSync('wechat_token');
    wx.removeStorageSync('user_info');
    wx.removeStorageSync('token_expires_at');
  }

  /**
   * 微信登录
   */
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo);
        },
        fail: reject
      });
    });
  }

  /**
   * 调用API接口
   */
  async callAPI(action, data = {}) {
    return new Promise((resolve, reject) => {
      const requestData = {
        action: action,
        ...data
      };



      wx.request({
        url: API_BASE_URL,
        method: 'POST',
        data: requestData,
        header: {
          'Content-Type': 'application/json',
          ...(this.token ? { 'Authorization': `Bearer ${this.token}` } : {})
        },
        success: (res) => {
          
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            const errorMsg = res.data && res.data.message ? res.data.message : '请求失败';

            reject(new Error(`HTTP ${res.statusCode}: ${errorMsg}`));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败: ' + (error.errMsg || '未知错误')));
        }
      });
    });
  }
}

// 导出登录管理器实例
const loginManager = new WechatLoginManager();

module.exports = {
  loginManager,
  WechatLoginManager
};
