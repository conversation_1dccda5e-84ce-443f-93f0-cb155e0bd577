/**
 * 用户管理工具类
 * 封装用户管理相关的API调用
 */

const API_BASE_URL = 'https://sunxiyue.com/zdh/api/user_management_api.php';

class UserManager {
  
  /**
   * 获取管理员Token
   */
  getAdminToken() {
    return wx.getStorageSync('wechat_token');
  }

  /**
   * 检查管理员权限
   */
  checkAdminPermission() {
    const userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('wechat_token');
    
    return !!(token && userInfo && userInfo.role === 'admin');
  }

  /**
   * 网络请求封装
   */
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败: ' + (error.errMsg || '未知错误')));
        }
      });
    });
  }

  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {string} params.search - 搜索关键词
   * @param {string} params.role - 角色筛选
   */
  async getUserList(params = {}) {
    // 手动构建查询参数
    let queryParams = `action=list&token=${encodeURIComponent(this.getAdminToken())}&page=${params.page || 1}`;

    if (params.search) {
      queryParams += `&search=${encodeURIComponent(params.search)}`;
    }

    if (params.role) {
      queryParams += `&role=${encodeURIComponent(params.role)}`;
    }

    return await this.request({
      url: `${API_BASE_URL}?${queryParams}`,
      method: 'GET'
    });
  }

  /**
   * 获取用户详情
   * @param {number} userId - 用户ID
   */
  async getUserDetail(userId) {
    const queryParams = `action=detail&user_id=${encodeURIComponent(userId)}&token=${encodeURIComponent(this.getAdminToken())}`;
    return await this.request({
      url: `${API_BASE_URL}?${queryParams}`,
      method: 'GET'
    });
  }

  /**
   * 添加用户
   * @param {Object} userData - 用户数据
   */
  async addUser(userData) {
    return await this.request({
      url: `${API_BASE_URL}?action=add`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        ...userData
      }
    });
  }

  /**
   * 删除用户
   * @param {number} userId - 用户ID
   */
  async deleteUser(userId) {
    return await this.request({
      url: `${API_BASE_URL}?action=delete`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        user_id: userId
      }
    });
  }

  /**
   * 更新用户真实姓名
   * @param {number} userId - 用户ID
   * @param {string} realName - 真实姓名
   */
  async updateRealName(userId, realName) {
    return await this.request({
      url: `${API_BASE_URL}?action=update_real_name`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        user_id: userId,
        real_name: realName
      }
    });
  }

  /**
   * 更新用户角色
   * @param {number} userId - 用户ID
   * @param {string} role - 角色
   * @param {boolean} isStationStaff - 是否一站人员
   */
  async updateRole(userId, role, isStationStaff = false) {
    return await this.request({
      url: `${API_BASE_URL}?action=update_role`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        user_id: userId,
        role: role,
        is_station_staff: isStationStaff
      }
    });
  }

  /**
   * 更新用户密码
   * @param {number} userId - 用户ID
   * @param {string} newPassword - 新密码
   */
  async updatePassword(userId, newPassword) {
    return await this.request({
      url: `${API_BASE_URL}?action=update_password`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        user_id: userId,
        new_password: newPassword
      }
    });
  }

  /**
   * 批量操作用户
   * @param {Array} userIds - 用户ID数组
   * @param {string} action - 操作类型
   * @param {Object} data - 额外数据
   */
  async batchOperation(userIds, action, data = {}) {
    return await this.request({
      url: `${API_BASE_URL}?action=batch`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        user_ids: userIds,
        batch_action: action,
        ...data
      }
    });
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats() {
    const queryParams = `action=stats&token=${encodeURIComponent(this.getAdminToken())}`;
    return await this.request({
      url: `${API_BASE_URL}?${queryParams}`,
      method: 'GET'
    });
  }

  /**
   * 导出用户数据
   * @param {Object} filters - 筛选条件
   */
  async exportUsers(filters = {}) {
    let queryParams = `action=export&token=${encodeURIComponent(this.getAdminToken())}`;

    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        queryParams += `&${encodeURIComponent(key)}=${encodeURIComponent(filters[key])}`;
      }
    });

    return await this.request({
      url: `${API_BASE_URL}?${queryParams}`,
      method: 'GET'
    });
  }

  /**
   * 验证用户名是否可用
   * @param {string} username - 用户名
   */
  async checkUsername(username) {
    const queryParams = `action=check_username&username=${encodeURIComponent(username)}&token=${encodeURIComponent(this.getAdminToken())}`;
    return await this.request({
      url: `${API_BASE_URL}?${queryParams}`,
      method: 'GET'
    });
  }

  /**
   * 重置用户密码
   * @param {number} userId - 用户ID
   */
  async resetPassword(userId) {
    return await this.request({
      url: `${API_BASE_URL}?action=reset_password`,
      method: 'POST',
      data: {
        token: this.getAdminToken(),
        user_id: userId
      }
    });
  }

  /**
   * 格式化用户角色文本
   * @param {string} role - 角色值
   */
  formatRoleText(role) {
    const roleMap = {
      'admin': '管理员',
      'manager': '普通管理员',
      'user': '普通用户'
    };
    return roleMap[role] || '未知角色';
  }

  /**
   * 格式化用户状态
   * @param {Object} user - 用户对象
   */
  formatUserStatus(user) {
    const status = [];
    
    if (user.is_station_staff) {
      status.push('一站人员');
    }
    
    if (user.has_wechat) {
      status.push('已绑定微信');
    }
    
    if (user.is_active === false) {
      status.push('已禁用');
    }
    
    return status;
  }

  /**
   * 验证用户数据
   * @param {Object} userData - 用户数据
   * @param {boolean} isEdit - 是否为编辑模式
   */
  validateUserData(userData, isEdit = false) {
    const errors = {};

    // 验证用户名（新增时必须）
    if (!isEdit) {
      if (!userData.username || !userData.username.trim()) {
        errors.username = '请输入用户名';
      } else if (userData.username.trim().length < 3) {
        errors.username = '用户名至少3个字符';
      } else if (userData.username.trim().length > 50) {
        errors.username = '用户名不能超过50个字符';
      } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(userData.username.trim())) {
        errors.username = '用户名只能包含字母、数字、下划线和中文';
      }
    }

    // 验证密码（新增时必须）
    if (!isEdit && userData.password !== undefined) {
      if (!userData.password) {
        errors.password = '请输入密码';
      } else if (userData.password.length < 6) {
        errors.password = '密码至少6个字符';
      }
    }

    // 验证真实姓名
    if (userData.real_name && userData.real_name.length > 50) {
      errors.real_name = '真实姓名不能超过50个字符';
    }

    // 验证角色
    if (userData.role && !['admin', 'manager', 'user'].includes(userData.role)) {
      errors.role = '无效的用户角色';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}

// 创建单例实例
const userManager = new UserManager();

module.exports = userManager;
