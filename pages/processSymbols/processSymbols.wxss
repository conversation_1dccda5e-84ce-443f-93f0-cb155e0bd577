/* pages/processSymbols/processSymbols.wxss */

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0 0 40rpx 0;
}

/* 主选项卡样式 */
.main-tabs {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 100;
  height: 110rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 0 0 28rpx 28rpx;
  overflow: hidden;
}

.main-tabs-scroll {
  display: flex;
  white-space: nowrap;
  padding: 0;
  overflow-x: auto;
  height: 100%;
  align-items: center;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}

.main-tabs-scroll::-webkit-scrollbar {
  display: none;
}

.main-tab-item {
  display: inline-flex;
  padding: 0 32rpx;
  font-size: 28rpx;
  color: #64748b;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  height: 110rpx;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  padding-bottom: 4rpx;
  border-radius: 20rpx;
  margin: 8rpx 4rpx;
}

.main-tab-item.active {
  color: #1e293b;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.06),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.main-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 3rpx;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 2rpx;
}

/* 子选项卡样式 */
.sub-tabs {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  position: fixed;
  width: 100%;
  top: 110rpx;
  z-index: 99;
  display: block;
  height: 100rpx;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.06),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 0 0 20rpx 20rpx;
  overflow: hidden;
}

.sub-tabs-scroll {
  display: flex;
  white-space: nowrap;
  padding: 0;
  overflow-x: auto;
  height: 100%;
  align-items: center;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}

.sub-tabs-scroll::-webkit-scrollbar {
  display: none;
}

.sub-tab-item {
  display: inline-flex;
  padding: 0 32rpx;
  font-size: 26rpx;
  color: #64748b;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  min-width: auto;
  text-align: center;
  height: 100rpx;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  padding-bottom: 4rpx;
  border-radius: 16rpx;
  margin: 6rpx 3rpx;
}

.sub-tab-item.active {
  color: #1e293b;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    0 1rpx 2rpx rgba(0, 0, 0, 0.02);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.sub-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 1rpx;
}

/* 内容区域样式 */
.content-section {
  flex: 1;
  padding: 30rpx 24rpx;
  margin-top: 210rpx;
}

.content-section.no-subtabs {
  margin-top: 110rpx;
}

.content-block {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 28rpx;
  padding: 36rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 36rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.content-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 32rpx;
  text-align: center;
  position: relative;
  letter-spacing: 0.5rpx;
}

.content-title::after {
  content: '';
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 2rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.sub-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  margin: 30rpx 0 15rpx;
  padding-left: 12rpx;
  border-left: 4rpx solid #3b82f6;
  letter-spacing: 0.3rpx;
}

.image-container {
  margin: 24rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.content-image {
  width: 100%;
  display: block;
  transform: scale(0.99);
  transition: transform 0.3s ease;
  border-radius: 12rpx;
}

.content-image:active {
  transform: scale(0.98);
}

/* 表格样式 */
.table-container {
  width: 100%;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  margin-top: 24rpx;
  font-size: 28rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.06),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: background-color 0.2s ease;
}

.table-row:nth-child(even) {
  background-color: rgba(59, 130, 246, 0.01);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: rgba(59, 130, 246, 0.02);
}

.table-header {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  font-weight: 700;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

.table-cell {
  padding: 24rpx 20rpx;
  flex: 1;
  word-break: break-all;
  border-right: 1rpx solid rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  color: #475569;
  transition: all 0.2s ease;
}

.table-cell:last-child {
  border-right: none;
}

.code-cell {
  flex: 0.5;
  font-weight: 700;
  color: #3b82f6;
  justify-content: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
  text-align: center;
  letter-spacing: 0.5rpx;
}

.param-cell {
  flex: 1.2;
}

.func-cell {
  flex: 1.5;
}

.description {
  font-size: 28rpx;
  color: #64748b;
  line-height: 1.6;
  padding: 24rpx 20rpx;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0.01) 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #3b82f6;
  margin-top: 24rpx;
  box-shadow:
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-icon {
  width: 64rpx;
  height: 64rpx;
  border: 3rpx solid rgba(0, 0, 0, 0.05);
  border-top: 3rpx solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 仪表字母定义样式 */
.instr-list {
  margin-top: 24rpx;
}

.instr-letter-title {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    0 1rpx 2rpx rgba(0, 0, 0, 0.02);
  border: 1rpx solid rgba(59, 130, 246, 0.1);
}

.instr-letter-title .letter {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-right: 20rpx;
  letter-spacing: 0.5rpx;
}

.instr-letter-title .meaning {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

.instr-combinations {
  margin-bottom: 40rpx;
}

.instr-combinations .table-container {
  margin-top: 0;
}

.instr-combinations .table-row:first-child {
  border-top: 0.5rpx solid rgba(0, 0, 0, 0.06);
}

.instr-combinations .table-row:last-child {
  border-bottom: none;
}

/* 搜索框样式 */
.search-box {
  position: relative;
  margin: 24rpx 0;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  padding: 0 70rpx 0 30rpx;
  font-size: 28rpx;
  color: #1e293b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05),
    0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow:
    0 0 0 6rpx rgba(59, 130, 246, 0.1),
    inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.search-input::placeholder {
  color: #94a3b8;
}

.search-icon {
  position: absolute;
  right: 30rpx;
  width: 28rpx;
  height: 28rpx;
  background: url("data:image/svg+xml,%3Csvg t='1709700844644' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4162' width='28' height='28'%3E%3Cpath d='M469.333333 768c-166.4 0-298.666667-132.266667-298.666666-298.666667s132.266667-298.666667 298.666666-298.666666 298.666667 132.266667 298.666667 298.666666-132.266667 298.666667-298.666667 298.666667z m0-85.333333c119.466667 0 213.333333-93.866667 213.333334-213.333334s-93.866667-213.333333-213.333334-213.333333-213.333333 93.866667-213.333333 213.333333 93.866667 213.333333 213.333333 213.333334z m251.733334 0l119.466666 119.466666-59.733333 59.733334-119.466667-119.466667 59.733334-59.733333z' fill='%23999999' p-id='4163'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
} 