<view class="container">
  <!-- 顶部主选项卡 -->
  <view class="main-tabs {{mainTabsHasMoreLeft ? 'has-more-left' : ''}} {{mainTabsHasMoreRight ? 'has-more-right' : ''}}">
    <scroll-view 
      scroll-x 
      enable-flex 
      class="main-tabs-scroll" 
      enhanced 
      show-scrollbar="{{false}}"
      bindscroll="onMainTabsScroll"
      scroll-into-view="main-tab-{{activeMainTab}}">
      <view 
        wx:for="{{mainTabs}}" 
        wx:key="id" 
        id="main-tab-{{item.id}}"
        class="main-tab-item {{activeMainTab === item.id ? 'active' : ''}}"
        bindtap="switchMainTab"
        data-id="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>
  
  <!-- 子选项卡 -->
  <view class="sub-tabs {{subTabsHasMoreLeft ? 'has-more-left' : ''}} {{subTabsHasMoreRight ? 'has-more-right' : ''}}" wx:if="{{subTabs[activeMainTab].length > 1}}">
    <scroll-view 
      scroll-x 
      enable-flex 
      class="sub-tabs-scroll" 
      enhanced 
      show-scrollbar="{{false}}"
      bindscroll="onSubTabsScroll"
      scroll-into-view="sub-tab-{{activeSubTab}}">
      <view 
        wx:for="{{subTabs[activeMainTab]}}" 
        wx:key="id" 
        id="sub-tab-{{item.id}}"
        class="sub-tab-item {{activeSubTab === item.id ? 'active' : ''}}"
        bindtap="switchSubTab"
        data-id="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>
  
  <!-- 内容区域 -->
  <view class="content-section {{subTabs[activeMainTab].length <= 1 ? 'no-subtabs' : ''}}">
    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{isLoading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 管线编号 - 管线编号规则 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'pipeCode' && activeSubTab === 'pipeRule'}}">
      <view class="content-title">管线编号规则</view>
      <view class="image-container">
        <image mode="widthFix" class="content-image" src="/images/processSymbols/pipeRule.png" binderror="imageError" bindtap="previewImage" data-src="/images/processSymbols/pipeRule.png"></image>
      </view>
      <view class="description">
        <text>管线编号由公称直径、介质代号、系统编号、管道顺序号、公称压力、基本材质、材质顺序号、保温形式及维持温度组成。</text>
      </view>
    </view>
    
    <!-- 管线编号 - 介质代码 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'pipeCode' && activeSubTab === 'mediumCode'}}">
      <view class="content-title">介质代码</view>
      <!-- 搜索框 -->
      <view class="search-box">
        <input 
          class="search-input" 
          placeholder="搜索介质代码或含义" 
          bindinput="onSearchMedium"
          value="{{mediumSearchKey}}"
        />
        <view class="search-icon"></view>
      </view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代码</view>
          <view class="table-cell">含义</view>
        </view>
        <view class="table-row" wx:for="{{filteredMediumCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.meaning}}</view>
        </view>
      </view>
      <view class="description">
        <text>介质代码表示管线中输送的介质类型。</text>
      </view>
    </view>
    
    <!-- 管线编号 - 管线材料等级代码 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'pipeCode' && activeSubTab === 'materialCode'}}">
      <view class="content-title">管线材料等级代码</view>
      <view class="sub-title">压力等级代码</view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代码</view>
          <view class="table-cell">含义</view>
        </view>
        <view class="table-row" wx:for="{{pressureCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.meaning}}</view>
        </view>
      </view>
      
      <view class="sub-title">材质编码</view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代码</view>
          <view class="table-cell">含义</view>
        </view>
        <view class="table-row" wx:for="{{materialCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.meaning}}</view>
        </view>
      </view>
      
      <view class="sub-title">保温及电伴热带号及形式</view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代码</view>
          <view class="table-cell">含义</view>
        </view>
        <view class="table-row" wx:for="{{insulationCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.meaning}}</view>
        </view>
      </view>
      
      <view class="description">
        <text>管线材料等级代码包括压力等级代码、材质编码、保温及电伴热带号及形式。</text>
      </view>
    </view>
    
    <!-- 管线编号 - 管线规格及应用范围 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'pipeCode' && activeSubTab === 'pipeSpec'}}">
      <view class="content-title">管线规格及应用范围</view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">材质代码</view>
          <view class="table-cell">材料</view>
          <view class="table-cell">适用介质</view>
          <view class="table-cell">压力等级</view>
          <view class="table-cell">壁厚(mm)</view>
        </view>
        <view class="table-row" wx:for="{{pipeSpecs}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.material}}</view>
          <view class="table-cell">{{item.medium}}</view>
          <view class="table-cell">{{item.pressure}}</view>
          <view class="table-cell">{{item.thickness}}</view>
        </view>
      </view>
      <view class="description">
        <text>管线规格及应用范围表明了不同材质代码的管线在不同介质和压力等级下的应用情况。</text>
      </view>
    </view>
    
    <!-- 管线编号 - 系统编码 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'pipeCode' && activeSubTab === 'systemCode'}}">
      <view class="content-title">系统编码</view>
      <!-- 搜索框 -->
      <view class="search-box">
        <input 
          class="search-input" 
          placeholder="搜索系统编码或名称" 
          bindinput="onSearchSystem"
          value="{{systemSearchKey}}"
        />
        <view class="search-icon"></view>
      </view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">编码</view>
          <view class="table-cell">系统名称</view>
        </view>
        <view class="table-row" wx:for="{{filteredSystemCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.system}}</view>
        </view>
      </view>
      <view class="description">
        <text>系统编码表示管线所属的系统类型。</text>
      </view>
    </view>
    
    <!-- 设备编号 - 设备标号 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'equipCode' && activeSubTab === 'equipTag'}}">
      <view class="content-title">设备标号</view>
      <view class="image-container">
        <image mode="widthFix" class="content-image" src="/images/processSymbols/equipTag.png" binderror="imageError" bindtap="previewImage" data-src="/images/processSymbols/equipTag.png"></image>
      </view>
      <view class="description">
        <text>设备标号由平台代码、设备代码、系统编号、第一顺序号和第二顺序号组成。</text>
      </view>
    </view>
    
    <!-- 设备编号 - 设备代码 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'equipCode' && activeSubTab === 'equipSymbol'}}">
      <view class="content-title">设备代码</view>
      <!-- 搜索框 -->
      <view class="search-box">
        <input 
          class="search-input" 
          placeholder="搜索设备代码或类型" 
          bindinput="onSearchEquip"
          value="{{equipSearchKey}}"
        />
        <view class="search-icon"></view>
      </view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代码</view>
          <view class="table-cell">设备类型</view>
        </view>
        <view class="table-row" wx:for="{{filteredEquipCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.equipment}}</view>
        </view>
      </view>
      <view class="description">
        <text>设备代码表示设备的类型，如：A(生活设备)、AC(空气冷却器)、P(泵)等。</text>
      </view>
    </view>
    
    <!-- 设备编号 - 平台代码 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'equipCode' && activeSubTab === 'platformCode'}}">
      <view class="content-title">平台代码</view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代码</view>
          <view class="table-cell">平台类型</view>
        </view>
        <view class="table-row" wx:for="{{platformCodes}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.platform}}</view>
        </view>
      </view>
      <view class="description">
        <text>平台代码表示设备所在的平台，如：WHP(井口平台)、PRP(生产平台)等。</text>
      </view>
    </view>
    
    <!-- 阀门编码 - 阀门编码规则 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'valveCode' && activeSubTab === 'valveRule'}}">
      <view class="content-title">阀门编码规则</view>
      <view class="image-container">
        <image mode="widthFix" class="content-image" src="/images/processSymbols/valveRule.png" binderror="imageError" bindtap="previewImage" data-src="/images/processSymbols/valveRule.png"></image>
      </view>
      <view class="description">
        <text>阀门编码由阀门类型、系统编号和顺序号组成，格式为MBV-1501。</text>
      </view>
    </view>
    
    <!-- 阀门编码 - 阀门代号 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'valveCode' && activeSubTab === 'valveSymbol'}}">
      <view class="content-title">阀门代号</view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">代号</view>
          <view class="table-cell">阀门类型</view>
        </view>
        <view class="table-row" wx:for="{{valveSymbols}}" wx:key="code">
          <view class="table-cell code-cell">{{item.code}}</view>
          <view class="table-cell">{{item.type}}</view>
        </view>
      </view>
      <view class="description">
        <text>阀门代号表示阀门的类型，如：MBV(球阀)、MCV(止回阀)、MGV(闸阀)等。</text>
      </view>
    </view>
    
    <!-- 常用缩写 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'commonAbbr'}}">
      <view class="content-title">常用缩写</view>
      <!-- 搜索框 -->
      <view class="search-box">
        <input 
          class="search-input" 
          placeholder="搜索缩写或含义" 
          bindinput="onSearchAbbr"
          value="{{abbrSearchKey}}"
        />
        <view class="search-icon"></view>
      </view>
      <view class="table-container">
        <view class="table-row table-header">
          <view class="table-cell code-cell">缩写</view>
          <view class="table-cell">含义</view>
        </view>
        <view class="table-row" wx:for="{{filteredCommonAbbrs}}" wx:key="abbr">
          <view class="table-cell code-cell">{{item.abbr}}</view>
          <view class="table-cell">{{item.meaning}}</view>
        </view>
      </view>
      <view class="description">
        <text>常用缩写包括各种设备、系统和工艺中常见的缩写符号。</text>
      </view>
    </view>
    
    <!-- 仪表字母定义 -->
    <view class="content-block" wx:if="{{!isLoading && activeMainTab === 'instrDef'}}">
      <view class="content-title">仪表字母定义</view>
      
      <!-- 搜索框 -->
      <view class="search-box">
        <input class="search-input" 
               placeholder="搜索仪表代码或含义" 
               value="{{instrSearchKey}}"
               bindinput="onSearchInstr"/>
        <view class="search-icon"></view>
      </view>

      <!-- 仪表字母定义列表 -->
      <view class="instr-list">
        <block wx:for="{{filteredInstrLetters}}" wx:key="letter">
          <!-- 字母标题 -->
          <view class="instr-letter-title">
            <text class="letter">{{item.letter}}</text>
            <text class="meaning">{{item.meaning}}</text>
          </view>
          
          <!-- 组合列表 -->
          <view class="instr-combinations">
            <view class="table-container">
              <view class="table-row table-header">
                <view class="table-cell code-cell">代码</view>
                <view class="table-cell param-cell">测量参数</view>
                <view class="table-cell func-cell">仪表功能</view>
              </view>
              <block wx:for="{{item.combinations}}" wx:key="code" wx:for-item="combo">
                <view class="table-row">
                  <view class="table-cell code-cell">{{combo.code}}</view>
                  <view class="table-cell param-cell">{{combo.meaning.param}}</view>
                  <view class="table-cell func-cell">{{combo.meaning.func}}</view>
                </view>
              </block>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view> 