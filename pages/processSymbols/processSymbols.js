Page({
  data: {
    activeMainTab: 'pipeCode', // 默认选中管线编号
    activeSubTab: 'pipeRule', // 默认选中管线编号规则
    isLoading: true, // 是否在加载中
    // 主选项卡
    mainTabs: [
      { id: 'pipeCode', name: '管线编号' },
      { id: 'equipCode', name: '设备编号' },
      { id: 'valveCode', name: '阀门编码' },
      { id: 'commonAbbr', name: '常用缩写' },
      { id: 'instrDef', name: '仪表字母定义' }
    ],
    // 子选项卡 - 按主选项卡分组
    subTabs: {
      pipeCode: [
        { id: 'pipeRule', name: '管线编号规则' },
        { id: 'mediumCode', name: '介质代码' },
        { id: 'materialCode', name: '管线材料等级代码' },
        { id: 'pipeSpec', name: '管线规格及应用范围' },
        { id: 'systemCode', name: '系统编码' }
      ],
      equipCode: [
        { id: 'equipTag', name: '设备标号' },
        { id: 'equipSymbol', name: '设备代码' },
        { id: 'platformCode', name: '平台代码' }
      ],
      valveCode: [
        { id: 'valveRule', name: '阀门编码规则' },
        { id: 'valveSymbol', name: '阀门代号' }
      ],
      commonAbbr: [
        { id: 'commonAbbr', name: '常用缩写' }
      ],
      instrDef: [
        { id: 'instrDef', name: '仪表字母定义' }
      ]
    },
    
    // 介质代码数据
    mediumCodes: [
      { code: 'AC', meaning: '伴生气' },
      { code: 'AI', meaning: '仪表风' },
      { code: 'AU', meaning: '公用风' },
      { code: 'BG', meaning: '密封气' },
      { code: 'CI', meaning: '化学药剂注入液' },
      { code: 'CL', meaning: '次氯酸钠' },
      { code: 'CO2', meaning: '二氧化碳' },
      { code: 'CR', meaning: '原油' },
      { code: 'FC', meaning: '泡沫浓缩液' },
      { code: 'FD', meaning: '柴油' },
      { code: 'DC', meaning: '闭式排放' },
      { code: 'FF', meaning: '消防泡沫' },
      { code: 'FG', meaning: '燃料气' },
      { code: 'FL', meaning: '低压火炬' },
      { code: 'FO', meaning: '燃料油' },
      { code: 'FW', meaning: '消防水' },
      { code: 'GC', meaning: '天然气凝析油' },
      { code: 'HO', meaning: '液压油' },
      { code: 'HM', meaning: '热媒油' },
      { code: 'JF', meaning: '直升机燃料' },
      { code: 'LO', meaning: '润滑油' },
      { code: 'DO', meaning: '开式排放' },
      { code: 'N2', meaning: '氮气' },
      { code: 'NG', meaning: '天然气' },
      { code: 'OW', meaning: '废油、污泥污水' },
      { code: 'PF', meaning: '工艺流体' },
      { code: 'PL', meaning: '工艺产液(油)' },
      { code: 'PW', meaning: '生产水' },
      { code: 'SO', meaning: '密封油' },
      { code: 'ST', meaning: '蒸汽' },
      { code: 'WB', meaning: '反洗水' },
      { code: 'WC', meaning: '冷却水' },
      { code: 'AV', meaning: '冷放空' },
      { code: 'WF', meaning: '淡水' },
      { code: 'WG', meaning: '生活污水' },
      { code: 'WH', meaning: '热水' },
      { code: 'WI', meaning: '注水' },
      { code: 'WK', meaning: '压井水' },
      { code: 'WO', meaning: '含油污水' },
      { code: 'WP', meaning: '饮用水' },
      { code: 'WS', meaning: '海水' },
      { code: 'WW', meaning: '废水' },
      { code: 'UG', meaning: '公用天然气' }
    ],
    
    // 压力等级代码
    pressureCodes: [
      { code: 'A', meaning: 'PN20/ANSI CLASS 150' },
      { code: 'B', meaning: 'PN50/ANSI CLASS 300' },
      { code: 'C', meaning: 'PN110/ANSI CLASS 600' },
      { code: 'E', meaning: 'PN260/ANSI CLASS 1500' }
    ],
    
    // 材质编码
    materialCodes: [
      { code: 'A', meaning: '基本材质为碳钢' },
      { code: 'B', meaning: '基本材质为底合金钢' },
      { code: 'K', meaning: '基本材质为不锈钢' },
      { code: 'P', meaning: '基本材质为钢骨架增强聚乙烯复合管' },
      { code: 'X', meaning: '基本材质为碳钢+镀锌' },
      { code: 'Y', meaning: '基本材质为镍铝青铜' },
      { code: 'Z', meaning: '基本材质为碳钢+环氧树脂' }
    ],
    
    // 保温及电伴热带号及形式
    insulationCodes: [
      { code: 'T', meaning: '电伴热' },
      { code: 'S', meaning: '蒸汽伴热' },
      { code: 'H', meaning: '保温' },
      { code: 'HT', meaning: '保温+电伴热' },
      { code: 'C', meaning: '保冷' },
      { code: '40', meaning: '维持温度' }
    ],
    
    // 管线规格及应用范围
    pipeSpecs: [
      { code: 'AA1', material: 'A106Gr.B/X42', medium: 'PF、PL、CR、AG、FL、FG、NG、AV、DO、DC、WI、WO、OW、WG', pressure: 'CLASS 150', thickness: '3' },
      { code: 'AA2', material: 'A106Gr.B', medium: 'HM', pressure: 'CLASS 150', thickness: '3' },
      { code: 'AA3', material: 'A106Gr.B', medium: 'N2、FD', pressure: 'CLASS 150', thickness: '2' },
      { code: 'AK1', material: '316L', medium: 'CI、LO', pressure: 'CLASS 150', thickness: '0' },
      { code: 'AP1', material: 'SPRE', medium: 'WS', pressure: 'CLASS 150', thickness: '2' },
      { code: 'AX1', material: 'A106Gr.B+镀锌', medium: 'AI、AU、AH、AF、WP', pressure: 'CLASS 150', thickness: '2' },
      { code: 'AY1', material: 'C70600(90Cu/10Ni)', medium: 'FW、FF(≤3")', pressure: 'CLASS 150', thickness: '0' },
      { code: 'AZ1', material: 'A106Gr.B+环氧树脂', medium: 'FW、FF(≥4")', pressure: 'CLASS 150', thickness: '2' },
      { code: 'BA1', material: 'A106Gr.B', medium: 'PL、PF、NG、CR', pressure: 'CLASS 300', thickness: '3' },
      { code: 'BX1', material: 'A106Gr.B+镀锌', medium: 'AI、AU', pressure: 'CLASS 300', thickness: '2' },
      { code: 'CA1', material: 'A106Gr.B/A105', medium: 'PF、PL、CR(泵后)', pressure: 'CLASS 600', thickness: '3' },
      { code: 'EA1', material: 'A106Gr.B/A105', medium: 'WI', pressure: 'CLASS 1500', thickness: '2' },
      { code: 'EX1', material: 'A106Gr.B/A105', medium: '二氧化碳及湿粉灭火介质', pressure: 'CLASS 1500', thickness: '2' }
    ],
    
    // 系统编码
    systemCodes: [
      { code: '10', system: '采油' },
      { code: '12', system: '管汇/多路阀' },
      { code: '13', system: '测试分离器/多项流量计' },
      { code: '15', system: '收发球桶' },
      { code: '20', system: '原油油气分离、脱水、储存及外输' },
      { code: '25', system: '气体压缩、处理、外输' },
      { code: '30', system: '含油污水处理系统' },
      { code: '31', system: '燃料气系统' },
      { code: '32', system: '柴油/燃料油系统' },
      { code: '34', system: '火炬/放空系统' },
      { code: '35', system: '开排系统' },
      { code: '36', system: '化学药剂注入系统' },
      { code: '37', system: '仪表/公用风系统' },
      { code: '40', system: '海水系统' },
      { code: '41', system: '注水系统' },
      { code: '42', system: '淡水' },
      { code: '43', system: '饮用水系统' },
      { code: '44', system: '热水' },
      { code: '45', system: '钻井水系统' },
      { code: '46', system: '生活污水排放系统' },
      { code: '47', system: '次氯酸盐/电解铜系统' },
      { code: '50', system: '热介质系统' },
      { code: '51', system: '冷却介质系统' },
      { code: '52', system: '锅炉系统' },
      { code: '57', system: 'HVAC系统' },
      { code: '60', system: '消防水泵系统' },
      { code: '62', system: '气体灭火系统' },
      { code: '63', system: '泡沫灭火系统' },
      { code: '65', system: '救生及逃生系统' },
      { code: '66', system: '闭排系统' },
      { code: '70', system: '主发电机/应急发电机' }
    ],
    
    // 设备代码
    equipCodes: [
      { code: 'A', equipment: '生活设备' },
      { code: 'AC', equipment: '空气冷却器' },
      { code: 'B', equipment: '锅炉及加热炉' },
      { code: 'BL', equipment: '风机' },
      { code: 'C', equipment: '压缩机' },
      { code: 'CB', equipment: '二氧化碳气瓶' },
      { code: 'COG', equipment: '原油发电机组' },
      { code: 'CT', equipment: '采油树' },
      { code: 'CCP', equipment: '中控室' },
      { code: 'D', equipment: '钻井设备' },
      { code: 'DE', equipment: '柴油机' },
      { code: 'DEG', equipment: '柴油发电机组' },
      { code: 'E', equipment: '换热器' },
      { code: 'FH', equipment: '电热器' },
      { code: 'F', equipment: '过滤器' },
      { code: 'FF', equipment: '消防设备' },
      { code: 'FIP', equipment: '火炬点火盘' },
      { code: 'FT', equipment: '火炬头' },
      { code: 'G', equipment: '发电机' },
      { code: 'GTG', equipment: '燃气轮机发电机组' },
      { code: 'H', equipment: '加热器' },
      { code: 'HC', equipment: '水力旋流器' },
      { code: 'L', equipment: '起重设备' },
      { code: 'LCP', equipment: '就地控制盘' },
      { code: 'LE', equipment: '救生设备' },
      { code: 'M', equipment: '管汇' },
      { code: 'MFM', equipment: '多相流量计' },
      { code: 'MI', equipment: '搅拌器' },
      { code: 'P', equipment: '泵' },
      { code: 'PL', equipment: '亲管球发射器' },
      { code: 'PIC', equipment: '清管阀' },
      { code: 'PR', equipment: '清管球接收器' },
      { code: 'STG', equipment: '蒸汽透平发电机' },
      { code: 'T', equipment: '罐' },
      { code: 'UPS', equipment: '不间断电源' },
      { code: 'V', equipment: '压力容器' },
      { code: 'WC', equipment: '水冷却器' },
      { code: 'WCP', equipment: '井口控制盘' },
      { code: 'WR', equipment: '修井设备' },
      { code: 'X', equipment: '撬装设备' }
    ],
    
    // 平台代码
    platformCodes: [
      { code: 'WHP', platform: '井口平台' },
      { code: 'PRP', platform: '生产平台' },
      { code: 'CEP', platform: '中心平台' },
      { code: 'APP', platform: '生活动力平台' },
      { code: 'LQ', platform: '生活楼' }
    ],
    
    // 阀门代号
    valveSymbols: [
      { code: 'MBV', type: '球阀' },
      { code: 'MCV', type: '止回阀' },
      { code: 'MFV', type: '蝶阀' },
      { code: 'MGV', type: '闸阀' },
      { code: 'MLV', type: '截止阀' },
      { code: 'CSV', type: '疏水阀' }
    ],
    
    // 常用缩写
    commonAbbrs: [
      { abbr: 'A', meaning: '空气' },
      { abbr: 'A/M', meaning: '自动/手动' },
      { abbr: 'ATM', meaning: '常压' },
      { abbr: 'BDV', meaning: '紧急释放阀' },
      { abbr: 'CC', meaning: '腐蚀挂片' },
      { abbr: 'CKO', meaning: '锁开(盘用)' },
      { abbr: 'CKC', meaning: '锁关(盘用)' },
      { abbr: 'DP', meaning: '排放点' },
      { abbr: 'EL', meaning: '标高' },
      { abbr: 'FR', meaning: '电阻探针' },
      { abbr: 'ESD', meaning: '应急关断' },
      { abbr: 'EX', meaning: '废弃排放' },
      { abbr: 'FA', meaning: '阻火器' },
      { abbr: 'LP', meaning: '低压' },
      { abbr: 'MW', meaning: '人孔' },
      { abbr: 'FB', meaning: '全通径' },
      { abbr: 'FC', meaning: '事故关' },
      { abbr: 'FH', meaning: '高压火炬' },
      { abbr: 'FO', meaning: '事故开' },
      { abbr: 'FP', meaning: '干粉灭火系统' },
      { abbr: 'FS', meaning: '失效状态' },
      { abbr: 'H', meaning: '高' },
      { abbr: 'HC', meaning: '软管连接' },
      { abbr: 'HCV', meaning: '手动角阀' },
      { abbr: 'HP', meaning: '高压' },
      { abbr: 'h.p.', meaning: '马力' },
      { abbr: 'HS', meaning: '软管站' },
      { abbr: 'L', meaning: '低' },
      { abbr: 'LC', meaning: '锁关' },
      { abbr: 'LO', meaning: '锁开' },
      { abbr: 'NC', meaning: '常关' },
      { abbr: 'NNF', meaning: '正常无流动' },
      { abbr: 'NO', meaning: '常开' },
      { abbr: 'PSD', meaning: '生产关断' },
      { abbr: 'SDV', meaning: '紧急关断阀' },
      { abbr: 'S.P.', meaning: '设定点' },
      { abbr: 'TEM', meaning: '临时性的' },
      { abbr: 'TS', meaning: '临时过滤器' },
      { abbr: 'TW', meaning: '温井' },
      { abbr: 'UA', meaning: '单元报警' },
      { abbr: 'US', meaning: '公用站' },
      { abbr: 'USD', meaning: '单元关断' },
      { abbr: 'VB', meaning: '除漏器' },
      { abbr: 'VF', meaning: '供应商提供' },
      { abbr: 'VP', meaning: '放空点' },
      { abbr: 'VSD', meaning: '变速驱动' },
      { abbr: 'RO', meaning: '限流孔板' }
    ],
    
    // 仪表字母定义
    instrLetters: [
      { letter: 'A', meaning: '数据分析', combinations: [
        { code: 'AE', meaning: { param: '数据分析', func: '元件' } },
        { code: 'AT', meaning: { param: '数据分析', func: '变送' } },
        { code: 'AIT', meaning: { param: '数据分析', func: '指示变送' } },
        { code: 'AY', meaning: { param: '数据分析', func: '转换器' } },
        { code: 'AI', meaning: { param: '数据分析', func: '指示' } },
        { code: 'AR', meaning: { param: '数据分析', func: '记录' } },
        { code: 'AC', meaning: { param: '数据分析', func: '控制器' } },
        { code: 'AIC', meaning: { param: '数据分析', func: '指示控制器' } },
        { code: 'ARC', meaning: { param: '数据分析', func: '记录控制器' } },
        { code: 'AS', meaning: { param: '数据分析', func: '开关' } },
        { code: 'ASLL', meaning: { param: '数据分析', func: '低低开关' } },
        { code: 'ASL', meaning: { param: '数据分析', func: '低开关' } },
        { code: 'ASH', meaning: { param: '数据分析', func: '高开关' } },
        { code: 'ASHH', meaning: { param: '数据分析', func: '高高开关' } },
        { code: 'ASHL', meaning: { param: '数据分析', func: '高低开关' } },
        { code: 'AALL', meaning: { param: '数据分析', func: '低低报警' } },
        { code: 'AAL', meaning: { param: '数据分析', func: '低报警' } },
        { code: 'AAH', meaning: { param: '数据分析', func: '高报警' } },
        { code: 'AAHH', meaning: { param: '数据分析', func: '高高报警' } },
        { code: 'AQ', meaning: { param: '数据分析', func: '积算' } },
        { code: 'ACV', meaning: { param: '数据分析', func: '自力式控制阀' } },
        { code: 'AG', meaning: { param: '数据分析', func: '测量' } },
        { code: 'AL', meaning: { param: '数据分析', func: '灯' } },
        { code: 'AV', meaning: { param: '数据分析', func: '控制阀' } }
      ]},
      { letter: 'B', meaning: '燃烧器/火焰', combinations: [
        { code: 'BE', meaning: { param: '燃烧器/火焰', func: '元件' } },
        { code: 'BT', meaning: { param: '燃烧器/火焰', func: '变送' } },
        { code: 'BIT', meaning: { param: '燃烧器/火焰', func: '指示变送' } },
        { code: 'BY', meaning: { param: '燃烧器/火焰', func: '转换器' } },
        { code: 'BI', meaning: { param: '燃烧器/火焰', func: '指示' } },
        { code: 'BR', meaning: { param: '燃烧器/火焰', func: '记录' } },
        { code: 'BC', meaning: { param: '燃烧器/火焰', func: '控制器' } },
        { code: 'BIC', meaning: { param: '燃烧器/火焰', func: '指示控制器' } },
        { code: 'BRC', meaning: { param: '燃烧器/火焰', func: '记录控制器' } }
      ]},
      { letter: 'C', meaning: '传导率', combinations: [
        { code: 'CE', meaning: { param: '传导率', func: '元件' } },
        { code: 'CT', meaning: { param: '传导率', func: '变送' } },
        { code: 'CIT', meaning: { param: '传导率', func: '指示变送' } },
        { code: 'CY', meaning: { param: '传导率', func: '转换器' } },
        { code: 'CI', meaning: { param: '传导率', func: '指示' } },
        { code: 'CR', meaning: { param: '传导率', func: '记录' } },
        { code: 'CC', meaning: { param: '传导率', func: '控制器' } },
        { code: 'CIC', meaning: { param: '传导率', func: '指示控制器' } },
        { code: 'CRC', meaning: { param: '传导率', func: '记录控制器' } }
      ]},
      { letter: 'D', meaning: '密度', combinations: [
        { code: 'DE', meaning: { param: '密度', func: '元件' } },
        { code: 'DT', meaning: { param: '密度', func: '变送' } },
        { code: 'DIT', meaning: { param: '密度', func: '指示变送' } },
        { code: 'DY', meaning: { param: '密度', func: '转换器' } },
        { code: 'DI', meaning: { param: '密度', func: '指示' } },
        { code: 'DR', meaning: { param: '密度', func: '记录' } },
        { code: 'DC', meaning: { param: '密度', func: '控制器' } },
        { code: 'DIC', meaning: { param: '密度', func: '指示控制器' } },
        { code: 'DRC', meaning: { param: '密度', func: '记录控制器' } }
      ]},
      { letter: 'F', meaning: '流量', combinations: [
        { code: 'FE', meaning: { param: '流量', func: '元件' } },
        { code: 'FT', meaning: { param: '流量', func: '变送' } },
        { code: 'FIT', meaning: { param: '流量', func: '指示变送' } },
        { code: 'FY', meaning: { param: '流量', func: '转换器' } },
        { code: 'FI', meaning: { param: '流量', func: '指示' } },
        { code: 'FR', meaning: { param: '流量', func: '记录' } },
        { code: 'FC', meaning: { param: '流量', func: '控制器' } },
        { code: 'FIC', meaning: { param: '流量', func: '指示控制器' } },
        { code: 'FRC', meaning: { param: '流量', func: '记录控制器' } },
        { code: 'FS', meaning: { param: '流量', func: '开关' } },
        { code: 'FSLL', meaning: { param: '流量', func: '低低开关' } },
        { code: 'FSL', meaning: { param: '流量', func: '低开关' } },
        { code: 'FSH', meaning: { param: '流量', func: '高开关' } },
        { code: 'FSHH', meaning: { param: '流量', func: '高高开关' } },
        { code: 'FSHL', meaning: { param: '流量', func: '高低开关' } },
        { code: 'FALL', meaning: { param: '流量', func: '低低报警' } },
        { code: 'FAL', meaning: { param: '流量', func: '低报警' } },
        { code: 'FAH', meaning: { param: '流量', func: '高报警' } },
        { code: 'FAHH', meaning: { param: '流量', func: '高高报警' } },
        { code: 'FQ', meaning: { param: '流量', func: '积算' } },
        { code: 'FCV', meaning: { param: '流量', func: '自力式控制阀' } },
        { code: 'FG', meaning: { param: '流量', func: '测量' } },
        { code: 'FL', meaning: { param: '流量', func: '灯' } },
        { code: 'FV', meaning: { param: '流量', func: '控制阀' } }
      ]},
      { letter: 'FF', meaning: '流比', combinations: [
        { code: 'FFE', meaning: { param: '流比', func: '元件' } },
        { code: 'FFT', meaning: { param: '流比', func: '变送' } },
        { code: 'FFY', meaning: { param: '流比', func: '转换器' } },
        { code: 'FFI', meaning: { param: '流比', func: '指示' } },
        { code: 'FFC', meaning: { param: '流比', func: '控制器' } },
        { code: 'FFIC', meaning: { param: '流比', func: '指示控制器' } }
      ]},
      { letter: 'H', meaning: '手动', combinations: [
        { code: 'HC', meaning: { param: '手动', func: '控制器' } },
        { code: 'HIC', meaning: { param: '手动', func: '指示控制器' } },
        { code: 'HS', meaning: { param: '手动', func: '开关' } },
        { code: 'HL', meaning: { param: '手动', func: '灯' } },
        { code: 'HV', meaning: { param: '手动', func: '控制阀' } }
      ]},
      { letter: 'I', meaning: '电流', combinations: [
        { code: 'IE', meaning: { param: '电流', func: '元件' } },
        { code: 'IT', meaning: { param: '电流', func: '变送' } },
        { code: 'IIT', meaning: { param: '电流', func: '指示变送' } },
        { code: 'IY', meaning: { param: '电流', func: '转换器' } },
        { code: 'II', meaning: { param: '电流', func: '指示' } },
        { code: 'IR', meaning: { param: '电流', func: '记录' } },
        { code: 'IC', meaning: { param: '电流', func: '控制器' } },
        { code: 'IIC', meaning: { param: '电流', func: '指示控制器' } },
        { code: 'IRC', meaning: { param: '电流', func: '记录控制器' } },
        { code: 'IS', meaning: { param: '电流', func: '开关' } },
        { code: 'ISLL', meaning: { param: '电流', func: '低低开关' } },
        { code: 'ISL', meaning: { param: '电流', func: '低开关' } },
        { code: 'ISHH', meaning: { param: '电流', func: '高高开关' } },
        { code: 'IALL', meaning: { param: '电流', func: '低低报警' } },
        { code: 'IAL', meaning: { param: '电流', func: '低报警' } },
        { code: 'IAH', meaning: { param: '电流', func: '高报警' } },
        { code: 'IAHH', meaning: { param: '电流', func: '高高报警' } },
        { code: 'IL', meaning: { param: '电流', func: '灯' } }
      ]},
      { letter: 'J', meaning: '功率', combinations: [
        { code: 'JC', meaning: { param: '功率', func: '控制器' } },
        { code: 'JIC', meaning: { param: '功率', func: '指示控制器' } }
      ]},
      { letter: 'K', meaning: '时间', combinations: [
        { code: 'KY', meaning: { param: '时间', func: '转换器' } },
        { code: 'KI', meaning: { param: '时间', func: '指示' } },
        { code: 'KR', meaning: { param: '时间', func: '记录' } },
        { code: 'KC', meaning: { param: '时间', func: '控制器' } },
        { code: 'KIC', meaning: { param: '时间', func: '指示控制器' } },
        { code: 'KRC', meaning: { param: '时间', func: '记录控制器' } },
        { code: 'KS', meaning: { param: '时间', func: '开关' } },
        { code: 'KSLL', meaning: { param: '时间', func: '低低开关' } },
        { code: 'KSL', meaning: { param: '时间', func: '低开关' } },
        { code: 'KSH', meaning: { param: '时间', func: '高开关' } },
        { code: 'KSHH', meaning: { param: '时间', func: '高高开关' } },
        { code: 'KALL', meaning: { param: '时间', func: '低低报警' } },
        { code: 'KAL', meaning: { param: '时间', func: '低报警' } },
        { code: 'KAH', meaning: { param: '时间', func: '高报警' } },
        { code: 'KAHH', meaning: { param: '时间', func: '高高报警' } },
        { code: 'KL', meaning: { param: '时间', func: '灯' } },
        { code: 'KV', meaning: { param: '时间', func: '控制阀' } }
      ]},
      { letter: 'L', meaning: '液位', combinations: [
        { code: 'LE', meaning: { param: '液位', func: '元件' } },
        { code: 'LT', meaning: { param: '液位', func: '变送' } },
        { code: 'LIT', meaning: { param: '液位', func: '指示变送' } },
        { code: 'LY', meaning: { param: '液位', func: '转换器' } },
        { code: 'LI', meaning: { param: '液位', func: '指示' } },
        { code: 'LR', meaning: { param: '液位', func: '记录' } },
        { code: 'LC', meaning: { param: '液位', func: '控制器' } },
        { code: 'LIC', meaning: { param: '液位', func: '指示控制器' } },
        { code: 'LRC', meaning: { param: '液位', func: '记录控制器' } },
        { code: 'LS', meaning: { param: '液位', func: '开关' } },
        { code: 'LSLL', meaning: { param: '液位', func: '低低开关' } },
        { code: 'LSL', meaning: { param: '液位', func: '低开关' } },
        { code: 'LSH', meaning: { param: '液位', func: '高开关' } },
        { code: 'LSHH', meaning: { param: '液位', func: '高高开关' } },
        { code: 'LALL', meaning: { param: '液位', func: '低低报警' } },
        { code: 'LAL', meaning: { param: '液位', func: '低报警' } },
        { code: 'LAH', meaning: { param: '液位', func: '高报警' } },
        { code: 'LAHH', meaning: { param: '液位', func: '高高报警' } },
        { code: 'LQ', meaning: { param: '液位', func: '积算' } },
        { code: 'LCV', meaning: { param: '液位', func: '自力式控制阀' } },
        { code: 'LG', meaning: { param: '液位', func: '测量' } },
        { code: 'LL', meaning: { param: '液位', func: '灯' } },
        { code: 'LV', meaning: { param: '液位', func: '控制阀' } }
      ]},
      { letter: 'M', meaning: '湿度/干度', combinations: [
        { code: 'ME', meaning: { param: '湿度/干度', func: '元件' } },
        { code: 'MT', meaning: { param: '湿度/干度', func: '变送' } },
        { code: 'MIT', meaning: { param: '湿度/干度', func: '指示变送' } },
        { code: 'MY', meaning: { param: '湿度/干度', func: '转换器' } },
        { code: 'MI', meaning: { param: '湿度/干度', func: '指示' } },
        { code: 'MR', meaning: { param: '湿度/干度', func: '记录' } },
        { code: 'MC', meaning: { param: '湿度/干度', func: '控制器' } },
        { code: 'MIC', meaning: { param: '湿度/干度', func: '指示控制器' } },
        { code: 'MRC', meaning: { param: '湿度/干度', func: '记录控制器' } },
        { code: 'MS', meaning: { param: '湿度/干度', func: '开关' } },
        { code: 'MSLL', meaning: { param: '湿度/干度', func: '低低开关' } },
        { code: 'MSL', meaning: { param: '湿度/干度', func: '低开关' } },
        { code: 'MSH', meaning: { param: '湿度/干度', func: '高开关' } },
        { code: 'MSHH', meaning: { param: '湿度/干度', func: '高高开关' } },
        { code: 'MALL', meaning: { param: '湿度/干度', func: '低低报警' } },
        { code: 'MAL', meaning: { param: '湿度/干度', func: '低报警' } },
        { code: 'MAH', meaning: { param: '湿度/干度', func: '高报警' } },
        { code: 'MAHH', meaning: { param: '湿度/干度', func: '高高报警' } },
        { code: 'ML', meaning: { param: '湿度/干度', func: '灯' } },
        { code: 'MV', meaning: { param: '湿度/干度', func: '控制阀' } }
      ]},
      { letter: 'P', meaning: '压力', combinations: [
        { code: 'PE', meaning: { param: '压力', func: '元件' } },
        { code: 'PT', meaning: { param: '压力', func: '变送' } },
        { code: 'PIT', meaning: { param: '压力', func: '指示变送' } },
        { code: 'PY', meaning: { param: '压力', func: '转换器' } },
        { code: 'PI', meaning: { param: '压力', func: '指示' } },
        { code: 'PR', meaning: { param: '压力', func: '记录' } },
        { code: 'PC', meaning: { param: '压力', func: '控制器' } },
        { code: 'PIC', meaning: { param: '压力', func: '指示控制器' } },
        { code: 'PRC', meaning: { param: '压力', func: '记录控制器' } },
        { code: 'PS', meaning: { param: '压力', func: '开关' } },
        { code: 'PSLL', meaning: { param: '压力', func: '低低开关' } },
        { code: 'PSL', meaning: { param: '压力', func: '低开关' } },
        { code: 'PSH', meaning: { param: '压力', func: '高开关' } },
        { code: 'PSHH', meaning: { param: '压力', func: '高高开关' } },
        { code: 'PALL', meaning: { param: '压力', func: '低低报警' } },
        { code: 'PAL', meaning: { param: '压力', func: '低报警' } },
        { code: 'PAH', meaning: { param: '压力', func: '高报警' } },
        { code: 'PAHH', meaning: { param: '压力', func: '高高报警' } },
        { code: 'PQ', meaning: { param: '压力', func: '积算' } },
        { code: 'PCV', meaning: { param: '压力', func: '自力式控制阀' } },
        { code: 'PG', meaning: { param: '压力', func: '测量' } },
        { code: 'PL', meaning: { param: '压力', func: '灯' } },
        { code: 'PV', meaning: { param: '压力', func: '控制阀' } }
      ]},
      { letter: 'PD', meaning: '压差', combinations: [
        { code: 'PDE', meaning: { param: '压差', func: '元件' } },
        { code: 'PDT', meaning: { param: '压差', func: '变送' } },
        { code: 'PDIT', meaning: { param: '压差', func: '指示变送' } },
        { code: 'PDY', meaning: { param: '压差', func: '转换器' } },
        { code: 'PDI', meaning: { param: '压差', func: '指示' } },
        { code: 'PDR', meaning: { param: '压差', func: '记录' } },
        { code: 'PDC', meaning: { param: '压差', func: '控制器' } },
        { code: 'PDIC', meaning: { param: '压差', func: '指示控制器' } },
        { code: 'PDRC', meaning: { param: '压差', func: '记录控制器' } }
      ]},
      { letter: 'Q', meaning: '数量', combinations: [
        { code: 'QE', meaning: { param: '数量', func: '元件' } },
        { code: 'QT', meaning: { param: '数量', func: '变送' } },
        { code: 'QIT', meaning: { param: '数量', func: '指示变送' } },
        { code: 'QY', meaning: { param: '数量', func: '转换器' } },
        { code: 'QI', meaning: { param: '数量', func: '指示' } },
        { code: 'QR', meaning: { param: '数量', func: '记录' } },
        { code: 'QC', meaning: { param: '数量', func: '控制器' } },
        { code: 'QIC', meaning: { param: '数量', func: '指示控制器' } }
      ]},
      { letter: 'S', meaning: '速度', combinations: [
        { code: 'SE', meaning: { param: '速度', func: '元件' } },
        { code: 'ST', meaning: { param: '速度', func: '变送' } },
        { code: 'SIT', meaning: { param: '速度', func: '指示变送' } },
        { code: 'SY', meaning: { param: '速度', func: '转换器' } },
        { code: 'SI', meaning: { param: '速度', func: '指示' } },
        { code: 'SR', meaning: { param: '速度', func: '记录' } },
        { code: 'SC', meaning: { param: '速度', func: '控制器' } },
        { code: 'SIC', meaning: { param: '速度', func: '指示控制器' } }
      ]},
      { letter: 'T', meaning: '温度', combinations: [
        { code: 'TE', meaning: { param: '温度', func: '元件' } },
        { code: 'TT', meaning: { param: '温度', func: '变送' } },
        { code: 'TIT', meaning: { param: '温度', func: '指示变送' } },
        { code: 'TY', meaning: { param: '温度', func: '转换器' } },
        { code: 'TI', meaning: { param: '温度', func: '指示' } },
        { code: 'TR', meaning: { param: '温度', func: '记录' } },
        { code: 'TC', meaning: { param: '温度', func: '控制器' } },
        { code: 'TIC', meaning: { param: '温度', func: '指示控制器' } },
        { code: 'TRC', meaning: { param: '温度', func: '记录控制器' } },
        { code: 'TS', meaning: { param: '温度', func: '开关' } },
        { code: 'TSLL', meaning: { param: '温度', func: '低低开关' } },
        { code: 'TSL', meaning: { param: '温度', func: '低开关' } },
        { code: 'TSH', meaning: { param: '温度', func: '高开关' } },
        { code: 'TSHH', meaning: { param: '温度', func: '高高开关' } },
        { code: 'TALL', meaning: { param: '温度', func: '低低报警' } },
        { code: 'TAL', meaning: { param: '温度', func: '低报警' } },
        { code: 'TAH', meaning: { param: '温度', func: '高报警' } },
        { code: 'TAHH', meaning: { param: '温度', func: '高高报警' } },
        { code: 'TQ', meaning: { param: '温度', func: '积算' } },
        { code: 'TCV', meaning: { param: '温度', func: '自力式控制阀' } },
        { code: 'TG', meaning: { param: '温度', func: '测量' } },
        { code: 'TL', meaning: { param: '温度', func: '灯' } },
        { code: 'TV', meaning: { param: '温度', func: '控制阀' } }
      ]},
      { letter: 'TD', meaning: '温差', combinations: [
        { code: 'TDE', meaning: { param: '温差', func: '元件' } },
        { code: 'TDT', meaning: { param: '温差', func: '变送' } },
        { code: 'TDIT', meaning: { param: '温差', func: '指示变送' } },
        { code: 'TDY', meaning: { param: '温差', func: '转换器' } },
        { code: 'TDI', meaning: { param: '温差', func: '指示' } },
        { code: 'TDR', meaning: { param: '温差', func: '记录' } },
        { code: 'TDC', meaning: { param: '温差', func: '控制器' } },
        { code: 'TDIC', meaning: { param: '温差', func: '指示控制器' } },
        { code: 'TDRC', meaning: { param: '温差', func: '记录控制器' } },
        { code: 'TDS', meaning: { param: '温差', func: '开关' } },
        { code: 'TDSLL', meaning: { param: '温差', func: '低低开关' } },
        { code: 'TDSL', meaning: { param: '温差', func: '低开关' } },
        { code: 'TDSH', meaning: { param: '温差', func: '高开关' } },
        { code: 'TDSHH', meaning: { param: '温差', func: '高高开关' } },
        { code: 'TDAL', meaning: { param: '温差', func: '低报警' } },
        { code: 'TDAH', meaning: { param: '温差', func: '高报警' } }
      ]},
      { letter: 'U', meaning: '多变量', combinations: [
        { code: 'UE', meaning: { param: '多变量', func: '元件' } },
        { code: 'UT', meaning: { param: '多变量', func: '变送' } },
        { code: 'UIT', meaning: { param: '多变量', func: '指示变送' } },
        { code: 'UY', meaning: { param: '多变量', func: '转换器' } },
        { code: 'UI', meaning: { param: '多变量', func: '指示' } },
        { code: 'UR', meaning: { param: '多变量', func: '记录' } },
        { code: 'UC', meaning: { param: '多变量', func: '控制器' } },
        { code: 'UIC', meaning: { param: '多变量', func: '指示控制器' } },
        { code: 'UL', meaning: { param: '多变量', func: '灯' } }
      ]},
      { letter: 'V', meaning: '粘度', combinations: [
        { code: 'VE', meaning: { param: '粘度', func: '元件' } },
        { code: 'VT', meaning: { param: '粘度', func: '变送' } },
        { code: 'VIT', meaning: { param: '粘度', func: '指示变送' } },
        { code: 'VY', meaning: { param: '粘度', func: '转换器' } },
        { code: 'VI', meaning: { param: '粘度', func: '指示' } },
        { code: 'VR', meaning: { param: '粘度', func: '记录' } },
        { code: 'VC', meaning: { param: '粘度', func: '控制器' } },
        { code: 'VIC', meaning: { param: '粘度', func: '指示控制器' } },
        { code: 'VRC', meaning: { param: '粘度', func: '记录控制器' } },
        { code: 'VS', meaning: { param: '粘度', func: '开关' } },
        { code: 'VSLL', meaning: { param: '粘度', func: '低低开关' } },
        { code: 'VSL', meaning: { param: '粘度', func: '低开关' } },
        { code: 'VSH', meaning: { param: '粘度', func: '高开关' } },
        { code: 'VSHH', meaning: { param: '粘度', func: '高高开关' } },
        { code: 'VALL', meaning: { param: '粘度', func: '低低报警' } },
        { code: 'VAL', meaning: { param: '粘度', func: '低报警' } },
        { code: 'VAH', meaning: { param: '粘度', func: '高报警' } },
        { code: 'VAHH', meaning: { param: '粘度', func: '高高报警' } },
        { code: 'VL', meaning: { param: '粘度', func: '灯' } },
        { code: 'VV', meaning: { param: '粘度', func: '控制阀' } }
      ]},
      { letter: 'W', meaning: '重量', combinations: [
        { code: 'WE', meaning: { param: '重量', func: '元件' } },
        { code: 'WT', meaning: { param: '重量', func: '变送' } },
        { code: 'WIT', meaning: { param: '重量', func: '指示变送' } },
        { code: 'WY', meaning: { param: '重量', func: '转换器' } },
        { code: 'WI', meaning: { param: '重量', func: '指示' } },
        { code: 'WR', meaning: { param: '重量', func: '记录' } },
        { code: 'WC', meaning: { param: '重量', func: '控制器' } },
        { code: 'WIC', meaning: { param: '重量', func: '指示控制器' } },
        { code: 'WS', meaning: { param: '重量', func: '开关' } },
        { code: 'WSLL', meaning: { param: '重量', func: '低低开关' } },
        { code: 'WSL', meaning: { param: '重量', func: '低开关' } },
        { code: 'WSH', meaning: { param: '重量', func: '高开关' } },
        { code: 'WSHH', meaning: { param: '重量', func: '高高开关' } },
        { code: 'WALL', meaning: { param: '重量', func: '低低报警' } },
        { code: 'WAL', meaning: { param: '重量', func: '低报警' } },
        { code: 'WAH', meaning: { param: '重量', func: '高报警' } },
        { code: 'WAHH', meaning: { param: '重量', func: '高高报警' } },
        { code: 'WL', meaning: { param: '重量', func: '灯' } },
        { code: 'WV', meaning: { param: '重量', func: '控制阀' } }
      ]},
      { letter: 'X', meaning: '未指定的条件或状态', combinations: [
        { code: 'XE', meaning: { param: '未指定', func: '元件' } },
        { code: 'XT', meaning: { param: '未指定', func: '变送' } },
        { code: 'XIT', meaning: { param: '未指定', func: '指示变送' } },
        { code: 'XY', meaning: { param: '未指定', func: '转换器' } },
        { code: 'XI', meaning: { param: '未指定', func: '指示' } },
        { code: 'XR', meaning: { param: '未指定', func: '记录' } },
        { code: 'XC', meaning: { param: '未指定', func: '控制器' } },
        { code: 'XIC', meaning: { param: '未指定', func: '指示控制器' } },
        { code: 'XRC', meaning: { param: '未指定', func: '记录控制器' } },
        { code: 'XS', meaning: { param: '未指定', func: '开关' } },
        { code: 'XSLL', meaning: { param: '未指定', func: '低低开关' } },
        { code: 'XSL', meaning: { param: '未指定', func: '低开关' } },
        { code: 'XSH', meaning: { param: '未指定', func: '高开关' } },
        { code: 'XSHH', meaning: { param: '未指定', func: '高高开关' } },
        { code: 'XALL', meaning: { param: '未指定', func: '低低报警' } },
        { code: 'XAL', meaning: { param: '未指定', func: '低报警' } },
        { code: 'XAH', meaning: { param: '未指定', func: '高报警' } },
        { code: 'XAHH', meaning: { param: '未指定', func: '高高报警' } },
        { code: 'XQ', meaning: { param: '未指定', func: '积算' } },
        { code: 'XCV', meaning: { param: '未指定', func: '自力式控制阀' } },
        { code: 'XG', meaning: { param: '未指定', func: '测量' } },
        { code: 'XL', meaning: { param: '未指定', func: '灯' } },
        { code: 'XV', meaning: { param: '未指定', func: '控制阀' } }
      ]},
      { letter: 'Z', meaning: '位置', combinations: [
        { code: 'ZE', meaning: { param: '位置', func: '元件' } },
        { code: 'ZT', meaning: { param: '位置', func: '变送' } },
        { code: 'ZIT', meaning: { param: '位置', func: '指示变送' } },
        { code: 'ZY', meaning: { param: '位置', func: '转换器' } },
        { code: 'ZI', meaning: { param: '位置', func: '指示' } },
        { code: 'ZR', meaning: { param: '位置', func: '记录' } },
        { code: 'ZC', meaning: { param: '位置', func: '控制器' } },
        { code: 'ZIC', meaning: { param: '位置', func: '指示控制器' } },
        { code: 'ZRC', meaning: { param: '位置', func: '记录控制器' } },
        { code: 'ZS', meaning: { param: '位置', func: '开关' } },
        { code: 'ZSL', meaning: { param: '位置', func: '低开关' } },
        { code: 'ZSH', meaning: { param: '位置', func: '高开关' } },
        { code: 'ZAL', meaning: { param: '位置', func: '低报警' } },
        { code: 'ZAH', meaning: { param: '位置', func: '高报警' } },
        { code: 'ZG', meaning: { param: '位置', func: '测量' } },
        { code: 'ZL', meaning: { param: '位置', func: '灯' } },
        { code: 'ZV', meaning: { param: '位置', func: '控制阀' } }
      ]},
      { letter: 'Special', meaning: '特殊定义', combinations: [
        { code: 'ASH', meaning: { param: '特殊定义', func: '易燃气体检测器(高浓度)' } },
        { code: 'ASHH', meaning: { param: '特殊定义', func: '易燃气体检测器(高高浓度)' } },
        { code: 'BDV', meaning: { param: '特殊定义', func: '紧急泄放阀' } },
        { code: 'FA', meaning: { param: '特殊定义', func: '阻火器' } },
        { code: 'FM', meaning: { param: '特殊定义', func: '流量计量' } },
        { code: 'XV', meaning: { param: '特殊定义', func: '消防释放阀' } },
        { code: 'NOC', meaning: { param: '特殊定义', func: '纯油自动测定仪' } },
        { code: 'PDSV', meaning: { param: '特殊定义', func: '释放阀' } },
        { code: 'PSE', meaning: { param: '特殊定义', func: '爆破片' } },
        { code: 'PSV', meaning: { param: '特殊定义', func: '压力安全阀' } },
        { code: 'PVR', meaning: { param: '特殊定义', func: '真空释放阀' } },
        { code: 'RO', meaning: { param: '特殊定义', func: '限流孔板' } },
        { code: 'ROV', meaning: { param: '特殊定义', func: '工艺遥控开关阀' } },
        { code: 'SAMP', meaning: { param: '特殊定义', func: '取样器' } },
        { code: 'SD', meaning: { param: '特殊定义', func: '烟雾探测器' } },
        { code: 'SDV', meaning: { param: '特殊定义', func: '紧急关断阀' } },
        { code: 'SCSSV', meaning: { param: '特殊定义', func: '井下安全阀' } },
        { code: 'SSV', meaning: { param: '特殊定义', func: '井上安全阀' } },
        { code: 'STR', meaning: { param: '特殊定义', func: '过滤器' } },
        { code: 'SV', meaning: { param: '特殊定义', func: '电磁阀' } },
        { code: 'TSE', meaning: { param: '特殊定义', func: '易熔塞' } },
        { code: 'TW', meaning: { param: '特殊定义', func: '温井' } },
        { code: 'UA', meaning: { param: '特殊定义', func: '公共故障' } },
        { code: 'UV', meaning: { param: '特殊定义', func: '紫外线检测器' } },
        { code: 'LDIT', meaning: { param: '特殊定义', func: '界面仪' } },
        { code: 'XPS', meaning: { param: '特殊定义', func: '清管信号' } },
        { code: 'VV', meaning: { param: '特殊定义', func: '真空释放' } },
        { code: 'SDY', meaning: { param: '特殊定义', func: '关断' } },
        { code: 'XL', meaning: { param: '特殊定义', func: '状态灯' } },
        { code: 'ZSC', meaning: { param: '特殊定义', func: '位置开关关' } },
        { code: 'ZSO', meaning: { param: '特殊定义', func: '位置开关开' } },
        { code: 'ZIC', meaning: { param: '特殊定义', func: '位置指示关' } },
        { code: 'ZIO', meaning: { param: '特殊定义', func: '位置指示开' } },
        { code: 'XI', meaning: { param: '特殊定义', func: '清管指示器' } },
        { code: 'GVV', meaning: { param: '特殊定义', func: '定压放气阀' } }
      ]}
    ],
    
    // 搜索关键字
    mediumSearchKey: '',
    systemSearchKey: '',
    equipSearchKey: '',
    abbrSearchKey: '',
    instrSearchKey: '',
    
    // 过滤后的数据
    filteredMediumCodes: [],
    filteredSystemCodes: [],
    filteredEquipCodes: [],
    filteredCommonAbbrs: [],
    filteredInstrLetters: []
  },
  
  onLoad() {
    // 模拟加载过程
    setTimeout(() => {
      this.setData({
        isLoading: false
      });
    }, 500);
    
    // 初始化过滤后的数据
    this.setData({
      filteredMediumCodes: this.data.mediumCodes,
      filteredSystemCodes: this.data.systemCodes,
      filteredEquipCodes: this.data.equipCodes,
      filteredCommonAbbrs: this.data.commonAbbrs,
      filteredInstrLetters: this.data.instrLetters
    });
  },
  
  // 切换主选项卡
  switchMainTab(e) {
    const tabId = e.currentTarget.dataset.id;
    const firstSubTab = this.data.subTabs[tabId][0].id;
    
    this.setData({
      activeMainTab: tabId,
      activeSubTab: firstSubTab,
      isLoading: true,
      mediumSearchKey: '',
      systemSearchKey: '',
      equipSearchKey: '',
      abbrSearchKey: '',
      instrSearchKey: '',
      filteredMediumCodes: this.data.mediumCodes,
      filteredSystemCodes: this.data.systemCodes,
      filteredEquipCodes: this.data.equipCodes,
      filteredCommonAbbrs: this.data.commonAbbrs,
      filteredInstrLetters: this.data.instrLetters
    });
    
    // 模拟加载过程
    setTimeout(() => {
      this.setData({
        isLoading: false
      });
    }, 300);
  },
  
  // 切换子选项卡
  switchSubTab(e) {
    const tabId = e.currentTarget.dataset.id;
    
    this.setData({
      activeSubTab: tabId,
      isLoading: true,
      mediumSearchKey: '',
      systemSearchKey: '',
      equipSearchKey: '',
      abbrSearchKey: '',
      instrSearchKey: '',
      filteredMediumCodes: this.data.mediumCodes,
      filteredSystemCodes: this.data.systemCodes,
      filteredEquipCodes: this.data.equipCodes,
      filteredCommonAbbrs: this.data.commonAbbrs,
      filteredInstrLetters: this.data.instrLetters
    });
    
    // 模拟加载过程
    setTimeout(() => {
      this.setData({
        isLoading: false
      });
    }, 200);
  },
  
  // 图片加载出错处理
  imageError(e) {
    console.log('图片加载失败', e);
    // 可以设置一个默认图片
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  },
  
  // 预览图片
  previewImage(e) {
    const src = e.currentTarget.dataset.src;
    wx.previewImage({
      current: src,
      urls: [src]
    });
  },
  
  onShareAppMessage() {
    return {
      title: '工艺及仪表符号查询',
      path: '/pages/processSymbols/processSymbols'
    };
  },
  
  onShareTimeline() {
    return {
      title: '工艺及仪表符号查询'
    };
  },
  
  // 介质代码搜索
  onSearchMedium: function(e) {
    const keyword = e.detail.value.toLowerCase();
    this.setData({
      mediumSearchKey: keyword,
      filteredMediumCodes: this.data.mediumCodes.filter(item => 
        item.code.toLowerCase().includes(keyword) || 
        item.meaning.toLowerCase().includes(keyword)
      )
    });
  },

  // 系统编码搜索
  onSearchSystem: function(e) {
    const keyword = e.detail.value.toLowerCase();
    this.setData({
      systemSearchKey: keyword,
      filteredSystemCodes: this.data.systemCodes.filter(item => 
        item.code.toLowerCase().includes(keyword) || 
        item.system.toLowerCase().includes(keyword)
      )
    });
  },

  // 设备代码搜索
  onSearchEquip: function(e) {
    const keyword = e.detail.value.toLowerCase();
    this.setData({
      equipSearchKey: keyword,
      filteredEquipCodes: this.data.equipCodes.filter(item => 
        item.code.toLowerCase().includes(keyword) || 
        item.equipment.toLowerCase().includes(keyword)
      )
    });
  },

  // 常用缩写搜索
  onSearchAbbr: function(e) {
    const keyword = e.detail.value.toLowerCase();
    this.setData({
      abbrSearchKey: keyword,
      filteredCommonAbbrs: this.data.commonAbbrs.filter(item => 
        item.abbr.toLowerCase().includes(keyword) || 
        item.meaning.toLowerCase().includes(keyword)
      )
    });
  },

  // 仪表字母定义搜索
  onSearchInstr: function(e) {
    const keyword = e.detail.value.toLowerCase();
    this.setData({
      instrSearchKey: keyword,
      filteredInstrLetters: this.data.instrLetters.map(letter => ({
        ...letter,
        combinations: letter.combinations.filter(combo =>
          combo.code.toLowerCase().includes(keyword) ||
          combo.meaning.param.toLowerCase().includes(keyword) ||
          combo.meaning.func.toLowerCase().includes(keyword)
        )
      })).filter(letter => letter.combinations.length > 0)
    });
  }
}); 