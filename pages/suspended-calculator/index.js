// pages/suspended-calculator/index.js
Page({
  data: {
    calculatorType: 'single', // single, batch
    beforeWeight: '', // 滤膜过滤前重量
    afterWeight: '', // 滤膜过滤后重量
    sampleVolume: '', // 水样体积
    showResult: false,
    result: 0,
    // 化验相关信息
    testDate: '', // 化验日期
    tester: '', // 化验人员
    // 悬浮物批量计算相关数据
    suspendedSampleLocations: ['注水泵', '海三', '气浮AB', '气浮C', '自定义'],
    suspendedBatchRecords: [],
    // 分享相关
    tempImagePath: '', // 临时图片路径
    shareType: 'suspended'  // 分享类型
  },

  onLoad() {
    this.initSuspendedBatchRecords();
    this.loadSuspendedBatchRecords();
    this.initTestDate();
  },

  // 初始化化验日期为今天
  initTestDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');

    this.setData({
      testDate: `${year}-${month}-${day}`
    });
  },

  // 处理化验日期变更
  changeTestDate(e) {
    this.setData({
      testDate: e.detail.value
    });
  },

  // 处理化验人员输入
  inputTester(e) {
    this.setData({
      tester: e.detail.value
    });
  },

  // 初始化悬浮物批量计算表格数据
  initSuspendedBatchRecords() {
    const { suspendedSampleLocations } = this.data;
    // 默认前四个取样地点
    const suspendedBatchRecords = suspendedSampleLocations.slice(0, 4).map((location, index) => {
      return {
        locationIndex: index,
        locationCustomName: '',  // 自定义地点名称
        beforeWeight: '',  // 滤膜过滤前重量
        afterWeight: '',   // 滤膜过滤后重量
        sampleVolume: '',  // 水样体积
        result: ''
      };
    });
    this.setData({ suspendedBatchRecords });
  },

  // 加载悬浮物批量计算记录
  loadSuspendedBatchRecords() {
    try {
      const savedRecords = wx.getStorageSync('batchSuspendedRecords');
      if (savedRecords && savedRecords.length > 0) {
        this.setData({ suspendedBatchRecords: savedRecords });
      }
    } catch (e) {
      console.log('加载悬浮物批量记录失败:', e);
    }
  },

  // 切换计算器类型
  switchCalculatorType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      calculatorType: type,
      showResult: false
    });

    // 如果切换到批量计算，确保加载数据
    if (type === 'batch') {
      this.loadSuspendedBatchRecords();
    }
  },

  // 输入过滤前重量
  inputBeforeWeight(e) {
    this.setData({ beforeWeight: e.detail.value });
  },

  // 输入过滤后重量
  inputAfterWeight(e) {
    this.setData({ afterWeight: e.detail.value });
  },

  // 输入水样体积
  inputSampleVolume(e) {
    this.setData({ sampleVolume: e.detail.value });
  },

  // 悬浮物计算
  calculateSuspended() {
    const { beforeWeight, afterWeight, sampleVolume } = this.data;

    if (!beforeWeight) {
      wx.showToast({
        title: '请输入过滤前重量',
        icon: 'none'
      });
      return;
    }
    if (!afterWeight) {
      wx.showToast({
        title: '请输入过滤后重量',
        icon: 'none'
      });
      return;
    }
    if (!sampleVolume) {
      wx.showToast({
        title: '请输入水样体积',
        icon: 'none'
      });
      return;
    }

    // 计算悬浮物含量(mg/L)
    // 公式：SS = (G2 - G1) * 10^6 / V
    const result = ((parseFloat(afterWeight) - parseFloat(beforeWeight)) * Math.pow(10, 6)) / parseFloat(sampleVolume);

    this.setData({
      result: result.toFixed(2),
      showResult: true
    });
  },

  // 批量计算相关方法
  // 选择悬浮物取样地点
  changeSuspendedLocation(e) {
    const index = e.currentTarget.dataset.index;
    const locationIndex = parseInt(e.detail.value);

    const { suspendedBatchRecords } = this.data;
    suspendedBatchRecords[index].locationIndex = locationIndex;

    // 如果选择了自定义，则清空自定义名称
    if (locationIndex === 4) {
      suspendedBatchRecords[index].locationCustomName = '';
    }

    this.setData({ suspendedBatchRecords });

    // 检查是否可以自动计算当前行
    this.calculateSuspendedSingleRow(index);
  },

  // 输入悬浮物自定义地点名称
  inputSuspendedCustomLocationName(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const suspendedBatchRecords = this.data.suspendedBatchRecords;

    suspendedBatchRecords[index].locationCustomName = value;
    this.setData({ suspendedBatchRecords });
  },

  // 从悬浮物自定义输入框返回到选择器
  returnToSuspendedPicker(e) {
    const index = e.currentTarget.dataset.index;
    const suspendedBatchRecords = this.data.suspendedBatchRecords;
    // 将locationIndex设为0，切换回选择器
    suspendedBatchRecords[index].locationIndex = 0;
    this.setData({ suspendedBatchRecords });
  },

  // 输入悬浮物批量过滤前重量
  inputSuspendedBeforeWeight(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const suspendedBatchRecords = this.data.suspendedBatchRecords;

    suspendedBatchRecords[index].beforeWeight = value;
    this.setData({ suspendedBatchRecords });

    // 实时计算单行结果
    this.calculateSuspendedSingleRow(index);
  },

  // 输入悬浮物批量过滤后重量
  inputSuspendedAfterWeight(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const suspendedBatchRecords = this.data.suspendedBatchRecords;

    suspendedBatchRecords[index].afterWeight = value;
    this.setData({ suspendedBatchRecords });

    // 实时计算单行结果
    this.calculateSuspendedSingleRow(index);
  },

  // 输入悬浮物批量水样体积
  inputSuspendedSampleVolume(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const suspendedBatchRecords = this.data.suspendedBatchRecords;

    suspendedBatchRecords[index].sampleVolume = value;
    this.setData({ suspendedBatchRecords });

    // 实时计算单行结果
    this.calculateSuspendedSingleRow(index);
  },

  // 计算单行悬浮物含量
  calculateSuspendedSingleRow(index) {
    const { suspendedBatchRecords } = this.data;
    const record = suspendedBatchRecords[index];

    if (record.beforeWeight && record.afterWeight && record.sampleVolume) {
      const beforeWeight = parseFloat(record.beforeWeight);
      const afterWeight = parseFloat(record.afterWeight);
      const sampleVolume = parseFloat(record.sampleVolume);

      if (afterWeight > beforeWeight) {
        // 计算悬浮物含量 SS = (G2 - G1) * 10^6 / V
        const result = ((afterWeight - beforeWeight) * 1000000) / sampleVolume;
        suspendedBatchRecords[index].result = result.toFixed(2);
        this.setData({ suspendedBatchRecords });
      }
    }
  },

  // 添加新的悬浮物行
  addNewSuspendedRow() {
    const suspendedBatchRecords = this.data.suspendedBatchRecords;
    suspendedBatchRecords.push({
      locationIndex: 4, // 默认为自定义
      locationCustomName: '',
      beforeWeight: '',
      afterWeight: '',
      sampleVolume: '',
      result: ''
    });

    this.setData({ suspendedBatchRecords });
  },

  // 删除悬浮物行
  deleteSuspendedRow(e) {
    const index = e.currentTarget.dataset.index;
    const suspendedBatchRecords = this.data.suspendedBatchRecords;

    suspendedBatchRecords.splice(index, 1);
    this.setData({ suspendedBatchRecords });

    wx.showToast({
      title: '已删除该行',
      icon: 'success'
    });
  },

  // 清除所有悬浮物记录
  clearAllSuspendedBatchRecords() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有悬浮物批量计算记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.initSuspendedBatchRecords();
          wx.removeStorageSync('batchSuspendedRecords');
          wx.showToast({
            title: '已清除所有记录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 批量计算所有悬浮物记录
  saveBatchSuspendedResults() {
    const { suspendedBatchRecords } = this.data;

    let hasValidData = false;

    suspendedBatchRecords.forEach((record, index) => {
      if (record.beforeWeight && record.afterWeight && record.sampleVolume) {
        const beforeWeight = parseFloat(record.beforeWeight);
        const afterWeight = parseFloat(record.afterWeight);
        const sampleVolume = parseFloat(record.sampleVolume);

        if (afterWeight > beforeWeight) {
          // 计算悬浮物含量 SS = (G2 - G1) * 10^6 / V
          const result = ((afterWeight - beforeWeight) * 1000000) / sampleVolume;
          suspendedBatchRecords[index].result = result.toFixed(2);
          hasValidData = true;
        }
      }
    });

    if (hasValidData) {
      this.setData({ suspendedBatchRecords });

      // 保存悬浮物批量计算结果到本地存储
      wx.setStorageSync('batchSuspendedRecords', suspendedBatchRecords);

      wx.showToast({
        title: '已保存',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '请输入有效数据',
        icon: 'none'
      });
    }
  },

  onShareAppMessage() {
    return {
      title: '平台常用计算工具-悬浮物计算器',
      path: '/pages/suspended-calculator/index'
    };
  },

  onShareTimeline() {
    return {
      title: '平台常用计算工具-悬浮物计算器',
      query: 'from=timeline'
    };
  },

  // 生成并分享悬浮物表格截图
  generateAndShareSuspendedTableImage() {
    wx.showLoading({
      title: '正在生成截图...',
    });

    try {
      const query = wx.createSelectorQuery();
      query.select('#shareCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0] || !res[0].node) {
            wx.hideLoading();
            wx.showToast({
              title: '无法获取画布',
              icon: 'none'
            });
            return;
          }

          // 获取表格信息
          query.select('.batch-table').boundingClientRect().exec((result) => {
            if (!result || !result[0]) {
              wx.hideLoading();
              wx.showToast({
                title: '获取表格失败',
                icon: 'none'
              });
              return;
            }

            const tableRect = result[0];
            const canvas = res[0].node;
            const dpr = wx.getWindowInfo().pixelRatio || 1;

            // 计算必要的表格高度
            const rowHeight = 40;
            const headerHeight = 50; // 增加表头高度，容纳两行文本
            const recordsCount = this.data.suspendedBatchRecords.length;
            const titleHeight = 50; // 标题部分高度
            const footerHeight = 40; // 底部时间信息高度

            // 设置Canvas尺寸 - 确保足够显示完整表格
            // 强制使用更大的宽度，不受页面表格宽度限制
            const canvasWidth = Math.max(tableRect.width || 300, 400); // 400px宽度，通过优化列宽分配来解决
            const canvasHeight = titleHeight + headerHeight + (recordsCount * rowHeight) + footerHeight;

            // 明确设置canvas宽高
            canvas.width = canvasWidth * dpr;
            canvas.height = canvasHeight * dpr;

            console.log('Canvas尺寸:', canvasWidth, 'x', canvasHeight, '记录数:', recordsCount);

            const ctx = canvas.getContext('2d');
            if (!ctx) {
              wx.hideLoading();
              wx.showToast({
                title: '创建绘图上下文失败',
                icon: 'none'
              });
              return;
            }

            // 清空画布并缩放
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.scale(dpr, dpr);

            // 先填充背景色
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);

            // 绘制表格内容
            this.renderSuspendedTable(ctx, canvasWidth, canvasHeight, tableRect);

            // 延迟一下再导出图片，确保绘制完成
            setTimeout(() => {
              wx.canvasToTempFilePath({
                canvas,
                success: (res) => {
                  wx.hideLoading();

                  // 保存临时文件路径
                  this.setData({
                    tempImagePath: res.tempFilePath,
                    shareType: 'suspended'
                  });

                  // 检查相册权限并分享图片
                  this.checkAndShareImage(res.tempFilePath);
                },
                fail: (err) => {
                  wx.hideLoading();
                  console.error('生成图片失败', err);
                  wx.showToast({
                    title: '生成图片失败：' + (err.errMsg || '未知错误'),
                    icon: 'none'
                  });
                }
              }, this);
            }, 300);
          });
        });
    } catch (err) {
      wx.hideLoading();
      console.error('截图过程出错', err);
      wx.showToast({
        title: '截图失败，请重试',
        icon: 'none'
      });
    }
  },

  // 检查权限并分享图片
  checkAndShareImage(imagePath) {
    // 先尝试直接调用分享
    wx.showShareImageMenu({
      path: imagePath,
      success: (res) => {
        console.log('分享成功', res);
      },
      fail: (err) => {
        console.error('分享失败', err);

        // 如果失败是因为权限问题
        if (err.errMsg && (err.errMsg.indexOf('appid privacy') >= 0 || err.errMsg.indexOf('auth deny') >= 0)) {
          // 尝试获取权限
          this.requestAlbumAuth(imagePath);
        } else {
          wx.showToast({
            title: '分享失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },

  // 请求相册权限
  requestAlbumAuth(imagePath) {
    wx.getSetting({
      success: (res) => {
        // 如果没有权限或权限被拒绝
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.showModal({
            title: '需要授权',
            content: '需要获取保存到相册的权限，才能分享图片',
            confirmText: '去授权',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 请求权限
                wx.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => {
                    // 获得权限后再次分享
                    this.shareImageAfterAuth(imagePath);
                  },
                  fail: (authErr) => {
                    // 用户拒绝授权，引导用户到设置页面开启
                    this.openSettingForAlbum(imagePath);
                  }
                });
              }
            }
          });
        } else {
          // 已有权限，直接分享
          this.shareImageAfterAuth(imagePath);
        }
      },
      fail: (err) => {
        console.error('获取设置失败', err);
        wx.showToast({
          title: '获取权限信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 打开设置页面
  openSettingForAlbum(imagePath) {
    wx.showModal({
      title: '授权提示',
      content: '需要在设置中开启"保存到相册"权限，是否去设置？',
      confirmText: '去设置',
      cancelText: '取消',
      success: (modalRes) => {
        if (modalRes.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.writePhotosAlbum']) {
                // 用户在设置页面开启了权限
                this.shareImageAfterAuth(imagePath);
              } else {
                wx.showToast({
                  title: '未获得权限，无法分享',
                  icon: 'none'
                });
              }
            }
          });
        }
      }
    });
  },

  // 获取权限后分享图片
  shareImageAfterAuth(imagePath) {
    wx.showShareImageMenu({
      path: imagePath,
      success: (res) => {
        console.log('分享成功', res);
      },
      fail: (err) => {
        console.error('分享失败', err);
        wx.showToast({
          title: '分享失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 直接渲染悬浮物表格到画布上
  renderSuspendedTable(ctx, canvasWidth, canvasHeight, tableRect) {
    // 绘制标题 - 只显示"悬浮物化验记录"
    ctx.fillStyle = '#333333';
    ctx.font = 'normal bold 16px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('悬浮物化验记录', canvasWidth / 2, 30);

    // 增加表格顶部的距离，避免与标题重叠
    const tableTop = 60;

    // 计算表格内容高度，确保能显示完整表格
    const rowHeight = 45; // 增加行高
    const headerHeight = 60; // 增加表头高度，容纳换行文本
    const recordsCount = this.data.suspendedBatchRecords.length;
    const tableHeight = headerHeight + (recordsCount * rowHeight) + 10;

    // 绘制化验信息 (如果有)
    if (this.data.testDate || this.data.tester) {
      ctx.font = 'normal 11px sans-serif';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'alphabetic';

      let infoText = '';
      if (this.data.testDate) {
        infoText += `化验日期: ${this.data.testDate}`;
      }

      if (this.data.tester) {
        if (infoText) infoText += '   ';
        infoText += `化验人员: ${this.data.tester}`;
      }

      ctx.fillText(infoText, 10, tableTop - 10);
    }

    // 绘制表格边框
    ctx.lineWidth = 1;
    ctx.strokeStyle = '#dddddd';
    ctx.strokeRect(10, tableTop, canvasWidth - 20, tableHeight);

    // 绘制表头
    ctx.fillStyle = '#f7f9fc';
    ctx.fillRect(10, tableTop, canvasWidth - 20, headerHeight);

    // 优化列宽分配 - 基于实际可用宽度计算（减去左右边距20px）
    const availableWidth = canvasWidth - 20; // 减去左右各10px边距
    const columnWidths = [
      availableWidth * 0.06,  // 序号 - 6%
      availableWidth * 0.20,  // 取样地点 - 20%
      availableWidth * 0.14,  // 空滤膜重量 - 14%
      availableWidth * 0.14,  // 过滤后重量 - 14%
      availableWidth * 0.16,  // 水相体积 - 16%
      availableWidth * 0.30   // 悬浮物含量 - 30%
    ];

    // 表头标签 - 支持换行
    const headerLabels = [
      ['序号'],
      ['取样', '地点'],
      ['空滤膜', '重量(g)'],
      ['过滤后', '重量(g)'],
      ['水相体积', '(mL)'],
      ['悬浮物', '含量(mg/L)']
    ];

    // 绘制表头分隔线
    for (let i = 1; i < headerLabels.length; i++) {
      const x = 10 + columnWidths.slice(0, i).reduce((a, b) => a + b, 0);
      ctx.beginPath();
      ctx.moveTo(x, tableTop);
      ctx.lineTo(x, tableTop + headerHeight);
      ctx.strokeStyle = '#dddddd';
      ctx.stroke();
    }

    // 绘制表头内容
    ctx.fillStyle = '#333333';
    ctx.font = 'normal 10px sans-serif'; // 稍微减小字体
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    let xPos = 10;
    for (let i = 0; i < headerLabels.length; i++) {
      const colCenterX = xPos + columnWidths[i] / 2;
      const lines = headerLabels[i];

      if (lines.length === 1) {
        // 单行文本，垂直居中
        ctx.fillText(lines[0], colCenterX, tableTop + headerHeight / 2);
      } else {
        // 多行文本，分别绘制
        const lineHeight = 14;
        const totalHeight = lines.length * lineHeight;
        const startY = tableTop + (headerHeight - totalHeight) / 2 + lineHeight / 2;

        for (let j = 0; j < lines.length; j++) {
          ctx.fillText(lines[j], colCenterX, startY + j * lineHeight);
        }
      }

      xPos += columnWidths[i];
    }

    // 数据行分隔线
    ctx.beginPath();
    ctx.moveTo(10, tableTop + headerHeight);
    ctx.lineTo(canvasWidth - 10, tableTop + headerHeight);
    ctx.strokeStyle = '#dddddd';
    ctx.lineWidth = 1.5;
    ctx.stroke();
    ctx.lineWidth = 1;

    // 绘制表格数据
    const { suspendedBatchRecords } = this.data;
    let yPos = tableTop + headerHeight;

    // 绘制数据行
    for (let i = 0; i < suspendedBatchRecords.length; i++) {
      const record = suspendedBatchRecords[i];

      // 行背景色 - 交替色
      if (i % 2 === 1) {
        ctx.fillStyle = '#f9f9f9';
        ctx.fillRect(10, yPos, canvasWidth - 20, rowHeight);
      }

      // 绘制横向分隔线
      ctx.beginPath();
      ctx.moveTo(10, yPos);
      ctx.lineTo(canvasWidth - 10, yPos);
      ctx.strokeStyle = '#eeeeee';
      ctx.stroke();

      // 设置数据文本样式
      ctx.fillStyle = '#333333';
      ctx.font = 'normal 10px sans-serif'; // 与表头字体一致
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 计算当前行中心Y坐标
      const rowMiddleY = yPos + rowHeight / 2;

      // 逐列绘制数据
      xPos = 10;

      // 序号
      ctx.fillText((i + 1).toString(), xPos + columnWidths[0] / 2, rowMiddleY);
      xPos += columnWidths[0];

      // 取样地点 - 处理长文本
      const locationName = record.locationIndex === 4 ? (record.locationCustomName || '自定义') : this.data.suspendedSampleLocations[record.locationIndex];
      this.drawTextWithWrap(ctx, locationName, xPos + columnWidths[1] / 2, rowMiddleY, columnWidths[1] - 10);
      xPos += columnWidths[1];

      // 空滤膜重量
      ctx.fillText(record.beforeWeight || '-', xPos + columnWidths[2] / 2, rowMiddleY);
      xPos += columnWidths[2];

      // 过滤后重量
      ctx.fillText(record.afterWeight || '-', xPos + columnWidths[3] / 2, rowMiddleY);
      xPos += columnWidths[3];

      // 水相体积
      ctx.fillText(record.sampleVolume || '-', xPos + columnWidths[4] / 2, rowMiddleY);
      xPos += columnWidths[4];

      // 悬浮物含量 - 确保文本居中对齐
      ctx.textAlign = 'center'; // 重新确保居中对齐
      ctx.fillText(record.result || '-', xPos + columnWidths[5] / 2, rowMiddleY);

      // 绘制垂直分隔线
      for (let j = 1; j < headerLabels.length; j++) {
        const x = 10 + columnWidths.slice(0, j).reduce((a, b) => a + b, 0);
        ctx.beginPath();
        ctx.moveTo(x, yPos);
        ctx.lineTo(x, yPos + rowHeight);
        ctx.strokeStyle = '#eeeeee';
        ctx.stroke();
      }

      yPos += rowHeight;
    }

    // 绘制底部边框
    ctx.beginPath();
    ctx.moveTo(10, yPos);
    ctx.lineTo(canvasWidth - 10, yPos);
    ctx.strokeStyle = '#dddddd';
    ctx.stroke();

    // 绘制时间
    const date = new Date();
    const dateStr = `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
    ctx.font = 'normal 9px sans-serif';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'alphabetic';
    ctx.fillText(`生成时间: ${dateStr}`, canvasWidth - 20, yPos + 20);

    // 辅助函数：格式化日期，补零
    function pad(n) {
      return n < 10 ? '0' + n : n;
    }
  },

  // 绘制带自动换行的文本
  drawTextWithWrap(ctx, text, x, y, maxWidth) {
    if (!text || text === '-') {
      ctx.fillText(text || '-', x, y);
      return;
    }

    // 如果文本长度不超过限制，直接绘制
    const textWidth = ctx.measureText(text).width;
    if (textWidth <= maxWidth) {
      ctx.fillText(text, x, y);
      return;
    }

    // 文本过长，尝试缩短显示
    let shortText = text;
    while (ctx.measureText(shortText + '...').width > maxWidth && shortText.length > 1) {
      shortText = shortText.slice(0, -1);
    }
    ctx.fillText(shortText + '...', x, y);
  }
});