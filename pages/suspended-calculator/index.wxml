<!-- pages/suspended-calculator/index.wxml -->
<view class="container">
  <!-- 隐藏的canvas，用于生成分享图片 -->
  <canvas type="2d" id="shareCanvas" style="width: 300px; height: 400px; position: absolute; left: -2000px; top: 0px;"></canvas>

  <!-- 计算器类型选择 -->
  <view class="calculator-section">
    <view class="calculator-type">
      <view class="type-item {{calculatorType === 'single' ? 'active' : ''}}" bindtap="switchCalculatorType" data-type="single">单次计算</view>
      <view class="type-item {{calculatorType === 'batch' ? 'active' : ''}}" bindtap="switchCalculatorType" data-type="batch">批量计算</view>
    </view>

    <!-- 单次悬浮物计算器 -->
    <view class="calculator-form" wx:if="{{calculatorType === 'single'}}">
      <view class="input-group">
        <text class="input-label">空白滤膜重量(g)</text>
        <input class="input-field" type="digit" placeholder="请输入空白滤膜重量" bindinput="inputBeforeWeight" value="{{beforeWeight}}"/>
      </view>
      <view class="input-group">
        <text class="input-label">滤膜过滤后重量(g)</text>
        <input class="input-field" type="digit" placeholder="请输入过滤后重量" bindinput="inputAfterWeight" value="{{afterWeight}}"/>
      </view>
      <view class="input-group">
        <text class="input-label">水样体积(mL)</text>
        <input class="input-field" type="digit" placeholder="请输入水样体积" bindinput="inputSampleVolume" value="{{sampleVolume}}"/>
      </view>
      <button class="calculate-btn" bindtap="calculateSuspended">计算</button>
    </view>

    <!-- 批量计算 -->
    <view class="batch-calculator" wx:if="{{calculatorType === 'batch'}}">
      <!-- 添加化验信息区域 -->
      <view class="test-info-container">
        <view class="test-info-item vertical">
          <view class="label-block">化验时间</view>
          <picker mode="date" value="{{testDate}}" bindchange="changeTestDate">
            <view class="picker {{testDate ? '' : 'placeholder'}}">
              <view class="date-text">{{testDate || '选择日期'}}</view>
            </view>
          </picker>
        </view>
        <view class="test-info-item vertical">
          <view class="label-block">化验人员</view>
          <input class="input-box" placeholder="请输入化验人员" value="{{tester}}" bindinput="inputTester" />
        </view>
      </view>

      <!-- 悬浮物批量计算表格 -->
      <view class="batch-table">
        <view class="table-header">
          <view class="table-cell cell-mini">序号</view>
          <view class="table-cell">取样地点</view>
          <view class="table-cell">空滤膜重量(g)</view>
          <view class="table-cell">过滤后重量(g)</view>
          <view class="table-cell">水相体积(mL)</view>
          <view class="table-cell">悬浮物含量(mg/L)</view>
          <view class="table-cell cell-mini">操作</view>
        </view>

        <block wx:for="{{suspendedBatchRecords}}" wx:key="index">
          <view class="table-row">
            <view class="table-cell cell-mini">{{index + 1}}</view>
            <view class="table-cell position-relative">
              <!-- 不是自定义时显示picker -->
              <picker wx:if="{{item.locationIndex !== 4}}" bindchange="changeSuspendedLocation" data-index="{{index}}" value="{{item.locationIndex}}" range="{{suspendedSampleLocations}}">
                <view class="picker">
                  {{suspendedSampleLocations[item.locationIndex]}}
                </view>
              </picker>
              <!-- 选择自定义时显示输入框 -->
              <input wx:if="{{item.locationIndex === 4}}" class="location-custom-input standalone" type="text" placeholder="输入地点名称" bindinput="inputSuspendedCustomLocationName" data-index="{{index}}" value="{{item.locationCustomName}}"/>
              <view wx:if="{{item.locationIndex === 4}}" class="return-to-picker" bindtap="returnToSuspendedPicker" data-index="{{index}}">
                <text>↺</text>
              </view>
            </view>
            <view class="table-cell">
              <input class="table-input" type="digit" placeholder="滤膜重量" bindinput="inputSuspendedBeforeWeight" data-index="{{index}}" value="{{item.beforeWeight}}"/>
            </view>
            <view class="table-cell">
              <input class="table-input" type="digit" placeholder="过滤后重量" bindinput="inputSuspendedAfterWeight" data-index="{{index}}" value="{{item.afterWeight}}"/>
            </view>
            <view class="table-cell">
              <input class="table-input" type="digit" placeholder="水相体积" bindinput="inputSuspendedSampleVolume" data-index="{{index}}" value="{{item.sampleVolume}}"/>
            </view>
            <view class="table-cell">
              <text>{{item.result || '-'}}</text>
            </view>
            <view class="table-cell cell-mini">
              <view class="delete-btn" bindtap="deleteSuspendedRow" data-index="{{index}}">×</view>
            </view>
          </view>
        </block>
      </view>

      <!-- 操作按钮 -->
      <view class="batch-action-buttons">
        <view class="action-btn" bindtap="addNewSuspendedRow">添加一行</view>
      </view>

      <view class="batch-buttons">
        <view class="save-btn" bindtap="saveBatchSuspendedResults">保存</view>
        <view class="clear-btn" bindtap="clearAllSuspendedBatchRecords">清除全部</view>
      </view>

      <!-- 分享按钮 -->
      <view class="share-button-container">
        <view class="share-btn" bindtap="generateAndShareSuspendedTableImage">分享表格截图</view>
      </view>
    </view>
  </view>

  <!-- 结果显示 -->
  <view class="result-section" wx:if="{{showResult && calculatorType === 'single'}}">
    <view class="result-title">计算结果</view>
    <view class="result-content">
      <view class="result-item">
        <text class="result-label">悬浮物含量：</text>
        <text class="result-value">{{result}} mg/L</text>
      </view>
      <view class="result-formula">
        <text>计算公式：SS = (G2 - G1) * 10^6 / V</text>
      </view>
    </view>
  </view>
</view>