/* pages/suspended-calculator/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 计算器区域 */
.calculator-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 15rpx rgba(0,0,0,0.08);
}

.calculator-type {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.type-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 30rpx;
  color: #7f8c8d;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.type-item.active {
  color: #3498db;
  border-bottom: 4rpx solid #3498db;
  font-weight: bold;
}

/* 输入表单 */
.calculator-form {
  padding: 20rpx 0;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #34495e;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.input-field:focus {
  border-color: #3498db;
}

.picker {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  color: #333;
  position: relative;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
}

.picker::after {
  content: '';
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #bbb;
  border-bottom: 2rpx solid #bbb;
  position: absolute;
  right: 30rpx;
  top: 50%;
  margin-top: -8rpx; /* 垂直居中调整 */
  transform: rotate(45deg);
}

/* 表格中的picker特殊样式 */
.table-cell .picker {
  padding-right: 30rpx; /* 为箭头留出空间 */
}

.table-cell .picker::after {
  right: 10rpx; /* 表格中的箭头位置更靠右 */
}

.picker .icon-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
}

.calculate-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  margin-top: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
  transition: all 0.3s;
}

.calculate-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(52, 152, 219, 0.3);
}

/* 结果区域 */
.result-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 25rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 15rpx rgba(0, 0, 0, 0.08);
}

.result-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.result-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.result-value {
  font-size: 36rpx;
  color: #e74c3c;
  font-weight: bold;
}

.result-formula {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.result-formula text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.result-formula text:last-child {
  margin-bottom: 0;
}

/* 化验信息区域样式 */
.test-info-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  width: 100%;
}

.test-info-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding-right: 15px;
}

/* 垂直布局的表单项 */
.test-info-item.vertical {
  flex-direction: column;
  align-items: flex-start;
  padding-bottom: 0;
}

.label-block {
  color: #333;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 调整选择器和输入框在垂直布局中的样式 */
.test-info-item.vertical .picker,
.test-info-item.vertical .input-box {
  height: 44px;
  line-height: 44px;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #e0e5ea;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
}

.test-info-item:first-child {
  flex: 1.1; /* 轻微增加日期选择器的宽度比例 */
}

.test-info-item:last-child {
  padding-right: 0;
  flex: 0.9; /* 轻微减少化验人员宽度比例 */
}

/* 日期选择器特定样式 */
.test-info-item.vertical .picker {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30px; /* 为箭头腾出空间 */
}

.test-info-item.vertical .picker .icon-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #999;
}

.date-text {
  color: #333;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.picker.placeholder .date-text {
  color: #999;
}

/* 输入框焦点和悬停效果 */
.test-info-item.vertical .picker:active,
.test-info-item.vertical .input-box:focus {
  border-color: #3498db;
  background-color: #fff;
  transition: all 0.3s ease;
}

/* 批量计算样式 */
.batch-calculator {
  padding: 20rpx 0;
}

/* 批量计算表格样式 */
.batch-table {
  width: 100%;
  border: 1rpx solid #eaeaea;
  border-radius: 10rpx;
  margin-top: 30rpx;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f7f9fc;
  font-weight: bold;
  border-bottom: 1rpx solid #eaeaea;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eaeaea;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 20rpx 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 26rpx;
  overflow: hidden;
  border-right: 1rpx solid #eaeaea;
}

.table-cell:last-child {
  border-right: none;
}

.cell-small {
  flex: 0.5;
}

.cell-mini {
  flex: 0.3;
}



/* 位置相关样式 */
.position-relative {
  position: relative;
}

.location-custom-input {
  width: 100%;
  padding: 8rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.location-custom-input.standalone {
  margin-bottom: 30rpx;
}

.return-to-picker {
  position: absolute;
  right: 15rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #555;
  transition: all 0.3s;
}

.return-to-picker:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.location-picker {
  width: 100%;
  padding: 8rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 22rpx;
  background-color: #f8f9fa;
  margin-bottom: 5rpx;
}

.custom-location-input {
  width: 100%;
  padding: 8rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.table-input {
  width: 90%;
  padding: 8rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 22rpx;
  text-align: center;
}

.delete-btn {
  width: 28rpx;
  height: 28rpx;
  background-color: #f5f5f5;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20rpx;
  box-shadow: none;
  transition: all 0.2s;
}

.delete-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
  color: #e74c3c;
}

/* 操作按钮 */
.batch-action-buttons {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}

.action-btn {
  padding: 15rpx 30rpx;
  background-color: #17a2b8;
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin: 0 10rpx;
}

.batch-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.save-btn, .clear-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0 10rpx;
}

.save-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.clear-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.share-button-container {
  margin-top: 30rpx;
  text-align: center;
}

.share-btn {
  display: inline-block;
  padding: 15rpx 40rpx;
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  border-radius: 10rpx;
  font-size: 26rpx;
}