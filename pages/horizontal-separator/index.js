Page({
  data: {
    diameter: '4',  // 修改默认值
    length: '16',   // 修改默认值
    liquidLevel: '',
    volume: '--',
    diagramWidth: 560,  // 示意图宽度
    diagramHeight: 240, // 示意图高度
    liquidLevelPercent: 40 // 液位百分比
  },

  onLoad() {
    // 页面加载时计算初始示意图尺寸
    this.calculateDiagramSize();
  },

  // 通用输入验证函数：限制只能输入一个小数点且不能输入负数
  validateInput(value) {
    // 如果是空字符串或只有一个点，则返回空字符串
    if (value === '' || value === '.') {
      return '';
    }
    
    // 不允许输入负号
    value = value.replace(/-/g, '');
    
    // 确保只有一个小数点
    const dotIndex = value.indexOf('.');
    if (dotIndex !== -1) {
      // 如果有小数点，确保后面的部分也只保留一个小数点
      const intPart = value.substring(0, dotIndex);
      let decimalPart = value.substring(dotIndex + 1);
      // 去除小数部分中的小数点
      decimalPart = decimalPart.replace(/\./g, '');
      value = intPart + '.' + decimalPart;
    }
    
    // 确保是有效数字
    if (isNaN(parseFloat(value))) {
      return '';
    }
    
    return value;
  },

  // 计算示意图尺寸
  calculateDiagramSize() {
    const { diameter, length, liquidLevel } = this.data;

    // 基础尺寸
    const baseWidth = 560;
    const baseHeight = 240;
    const maxWidth = 600;
    const minWidth = 300;
    const maxHeight = 300;
    const minHeight = 150;

    let diagramWidth = baseWidth;
    let diagramHeight = baseHeight;

    // 如果有输入值，根据长度和直径的比例调整
    if (diameter && length) {
      const d = parseFloat(diameter) || 4;
      const l = parseFloat(length) || 16;

      // 计算长宽比
      const ratio = l / d;

      // 根据比例调整宽度，保持高度相对稳定
      if (ratio > 4) {
        // 长度相对较大，增加宽度
        diagramWidth = Math.min(maxWidth, baseWidth * (ratio / 4));
      } else if (ratio < 2) {
        // 长度相对较小，减少宽度
        diagramWidth = Math.max(minWidth, baseWidth * (ratio / 4));
      }

      // 根据直径调整高度
      if (d > 4) {
        diagramHeight = Math.min(maxHeight, baseHeight * (d / 4));
      } else if (d < 4) {
        diagramHeight = Math.max(minHeight, baseHeight * (d / 4));
      }
    }

    // 计算液位百分比
    let liquidLevelPercent = 40; // 默认40%
    if (liquidLevel && diameter) {
      const h = parseFloat(liquidLevel) || 0;
      const d = parseFloat(diameter) || 4;
      liquidLevelPercent = Math.min(100, Math.max(0, (h / d) * 100));
    }

    this.setData({
      diagramWidth: Math.round(diagramWidth),
      diagramHeight: Math.round(diagramHeight),
      liquidLevelPercent: Math.round(liquidLevelPercent)
    });
  },

  inputDiameter(e) {
    const value = this.validateInput(e.detail.value);
    this.setData({ diameter: value }, () => {
      this.calculateDiagramSize();
    });
  },

  inputLength(e) {
    const value = this.validateInput(e.detail.value);
    this.setData({ length: value }, () => {
      this.calculateDiagramSize();
    });
  },

  inputLiquidLevel(e) {
    const value = this.validateInput(e.detail.value);
    this.setData({ liquidLevel: value }, () => {
      this.calculateDiagramSize();
    });
  },

  calculateVolume() {
    const { diameter, length, liquidLevel } = this.data;
    
    // 输入验证
    if (!diameter.trim()) {
      wx.showToast({ title: '请输入直径', icon: 'none' });
      return;
    }
    if (!length.trim()) {
      wx.showToast({ title: '请输入长度', icon: 'none' });
      return;
    }
    if (!liquidLevel.trim()) {
      wx.showToast({ title: '请输入液位高度', icon: 'none' });
      return;
    }

    const d = parseFloat(diameter);
    const L = parseFloat(length);
    const h = parseFloat(liquidLevel);

    if (isNaN(d) || d <= 0) {
      wx.showToast({ title: '直径必须大于0', icon: 'none' });
      return;
    }
    if (isNaN(L) || L <= 0) {
      wx.showToast({ title: '长度必须大于0', icon: 'none' });
      return;
    }
    if (isNaN(h) || h < 0) {
      wx.showToast({ title: '液位高度不能为负', icon: 'none' });
      return;
    }
    if (h > d) {
      wx.showToast({ title: '液位不能超过直径', icon: 'none' });
      return;
    }

    // 计算逻辑
    const r = d / 2;
    const acosParam = (r - h) / r;
    const validParam = Math.max(-1, Math.min(1, acosParam));
    const acosVal = Math.acos(validParam);

    let sqrtVal = 2 * r * h - h * h;
    sqrtVal = sqrtVal < 0 ? 0 : Math.sqrt(sqrtVal);

    const term1 = r * r * acosVal;
    const term2 = (r - h) * sqrtVal;
    const volume = (term1 - term2) * L;

    this.setData({
      volume: volume.toFixed(2)
    });
  },
  
  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-卧式分离器容积计算器',
      path: '/pages/horizontal-separator/index'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-卧式分离器容积计算器',
      query: 'from=timeline'
    };
  }
});