<view class="page-container">
  <!-- 分离器示意图 -->
  <view class="card diagram-card">
    <view class="diagram-title">
      <text class="title-text">分离器示意图</text>
    </view>
    <view class="separator-diagram" style="width: {{diagramWidth}}rpx; height: {{diagramHeight}}rpx; border-radius: {{diagramHeight/2}}rpx;">
      <view class="liquid-level" style="height: {{liquidLevelPercent}}%; border-radius: 0 0 {{diagramHeight/2}}rpx {{diagramHeight/2}}rpx;"></view>
      <view class="dimension-label length-label">L = {{length || '16'}}m</view>
      <view class="dimension-label diameter-label">D = {{diameter || '4'}}m</view>
      <view class="dimension-label level-label">H</view>
      <view class="water-drop">💧</view>
    </view>
    <view class="diagram-desc">卧式圆筒形分离器横截面图</view>
  </view>

  <view class="card form-card">
    <view class="form-header">
      <view class="form-header-title">
        <text class="form-header-icon">✏️</text>
        <text>几何参数</text>
      </view>
    </view>
    <view class="form-content">
      <view class="input-group">
        <view class="input-label">
          <text>分离器直径 (m)</text>
        </view>
        <view class="input-container">
          <input type="digit"
                placeholder="请输入直径"
                value="{{diameter}}"
                bindinput="inputDiameter"
                maxlength="10"/>
          <text class="unit">m</text>
        </view>
        <view class="input-desc">分离器内径，单位：米</view>
      </view>

      <view class="input-group">
        <view class="input-label">
          <text>分离器长度 (m)</text>
        </view>
        <view class="input-container">
          <input type="digit"
                placeholder="请输入长度"
                value="{{length}}"
                bindinput="inputLength"
                maxlength="10"/>
          <text class="unit">m</text>
        </view>
        <view class="input-desc">分离器筒体长度，单位：米</view>
      </view>

      <view class="input-group">
        <view class="input-label">
          <text>液位高度 (m)</text>
        </view>
        <view class="input-container">
          <input type="digit"
                placeholder="请输入液位高度"
                value="{{liquidLevel}}"
                bindinput="inputLiquidLevel"
                maxlength="10"/>
          <text class="unit">m</text>
        </view>
        <view class="input-desc">从分离器底部到液面的高度，单位：米</view>
      </view>

      <button class="calc-btn" hover-class="btn-hover" bindtap="calculateVolume">
        🧮 开始计算
      </button>
    </view>
  </view>
  
  <block wx:if="{{volume}}">
    <view class="result-card">
      <view class="result-title">
        <text class="result-title-icon">📊</text>
        <text>计算结果</text>
      </view>
      <view class="result-content">
        <view class="result-item">
          <view class="result-main">
            <view class="result-value">{{volume}}</view>
            <view class="result-unit">m³</view>
          </view>
        </view>
      </view>
    </view>

    <view class="formula-card">
      <view class="formula-title">
        <text class="formula-title-icon">📐</text>
        <text>计算公式</text>
      </view>
      <view class="formula-content">
        V = [r²·arccos((r-h)/r) - (r-h)√(2rh-h²)]·L
      </view>
      <view class="formula-desc">
        其中: r=罐体半径, h=液位高度, L=罐体长度
      </view>
    </view>
  </block>

  <view class="tips-card" wx:if="{{!volume}}">
    <view class="tips-title">
      <text class="tips-title-icon">💡</text>
      <text>使用说明</text>
    </view>
    <view class="tips-content">
      <view class="tip-item">本工具适用于卧式圆柱形储罐液位容积计算</view>
      <view class="tip-item">所有输入参数请使用相同单位(米)</view>
      <view class="tip-item">液位高度须小于或等于罐体直径</view>
      <view class="tip-item">仅支持输入正数和最多一个小数点</view>
    </view>
  </view>
</view>