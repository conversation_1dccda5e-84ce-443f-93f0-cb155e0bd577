/* 页面容器 */
.page-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  padding: 0 0 40rpx 0;
}

/* 标题区域 */
.header {
  background-color: #fff;
  text-align: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
  width: 100%;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
  display: block;
}

/* 卡片通用样式 */
.card {
  margin: 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 示意图卡片 */
.diagram-card {
  margin: 32rpx;
  background-color: #f8f9fa;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.diagram-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #374151;
}

/* 分离器示意图 */
.separator-diagram {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 4rpx solid #2196F3;
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.liquid-level {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #4fc3f7, #29b6f6);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.dimension-label {
  position: absolute;
  font-size: 24rpx;
  font-weight: 600;
  color: #1976d2;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid #2196F3;
}

.length-label {
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
}

.diameter-label {
  right: -60rpx;
  top: 50%;
  transform: translateY(-50%);
}

.level-label {
  left: -50rpx;
  bottom: 30%;
  transform: translateY(50%);
}

.water-drop {
  position: absolute;
  font-size: 48rpx;
  opacity: 0.5;
  z-index: 1;
}

.diagram-desc {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
  text-align: center;
}

/* 表单卡片 */
.form-card {
  padding: 0;
  margin-top: 0;
}

.form-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f3f4f6;
  background-color: #fff;
}

.form-header-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
}

.form-header-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #10b981;
}

.form-content {
  padding: 32rpx;
  background-color: #fff;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  margin-bottom: 16rpx;
}

.input-label text {
  color: #374151;
  font-size: 28rpx;
  font-weight: 500;
}

.input-desc {
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 8rpx;
  line-height: 1.4;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

input {
  width: 100%;
  height: 96rpx;
  padding: 0 70rpx 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

input:focus {
  background-color: #fff;
  border-color: #007AFF;
  outline: none;
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

.unit {
  position: absolute;
  right: 20rpx;
  color: #999;
  font-size: 26rpx;
}

/* 按钮样式 */
.calc-btn {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  border-radius: 24rpx;
  width: 100%;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 40rpx;
  padding: 0;
  line-height: 96rpx;
  text-align: center;
  border: none;
  transition: transform 0.2s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.btn-hover {
  transform: scale(0.98);
}

/* 结果卡片 */
.result-card {
  margin-top: 0;
  background: linear-gradient(135deg, #34C759, #30D158);
  color: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(52, 199, 89, 0.3);
}

.result-title {
  font-size: 32rpx;
  color: white;
  font-weight: 600;
  padding: 0;
  border-bottom: none;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}

.result-title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.result-content {
  padding: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 32rpx;
}

.result-item {
  display: flex;
  align-items: center;
  text-align: center;
  flex-direction: column;
}

.result-main {
  margin-bottom: 32rpx;
}

.result-value {
  font-size: 80rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.result-unit {
  font-size: 28rpx;
  opacity: 0.9;
}

.label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  margin-right: 10rpx;
}

.value {
  color: white;
  font-weight: 500;
  font-size: 30rpx;
}

/* 公式卡片 */
.formula-card {
  margin-top: 0;
  padding: 32rpx;
  background-color: #f8f9fa;
  border-radius: 24rpx;
  margin: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.formula-title {
  font-size: 30rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.formula-title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.formula-content {
  font-size: 26rpx;
  color: #1f2937;
  font-family: monospace;
  line-height: 1.6;
  border-left: 8rpx solid #007AFF;
  padding-left: 24rpx;
  margin: 16rpx 0;
  background: rgba(255, 255, 255, 0.8);
  padding: 24rpx;
  border-radius: 12rpx;
}

.formula-desc {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 提示卡片 */
.tips-card {
  margin: 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 30rpx;
  color: #374151;
  font-weight: 600;
  padding: 32rpx;
  border-bottom: 1rpx solid #f3f4f6;
  display: flex;
  align-items: center;
}

.tips-title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #3b82f6;
}

.tips-content {
  padding: 32rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.8;
  margin-bottom: 16rpx;
  padding-left: 24rpx;
  position: relative;
}

.tip-item::before {
  content: "•";
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 页脚 */
.footer {
  text-align: center;
  margin-top: auto;
  padding: 30rpx 0;
  color: #999;
  font-size: 26rpx;
}