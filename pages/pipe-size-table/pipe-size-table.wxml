<!-- pages/pipe-size-table/pipe-size-table.wxml -->
<view class="container">
  <!-- 管线尺寸对照表内容 -->
  <view class="module-box pipe-size-module">
    <!-- 搜索类型选择 -->
    <view class="search-type-header">
      <view class="search-title">
        <text class="title-icon">🔍</text>
        <text class="title-text">搜索方式</text>
      </view>
    </view>
    
    <view class="search-section">
      <view class="search-type-container">
        <view class="search-type-item {{searchType === 'dn' ? 'active' : ''}}" 
              bindtap="switchSearchType" data-type="dn">按公称直径</view>
        <view class="search-type-item {{searchType === 'nps' ? 'active' : ''}}" 
              bindtap="switchSearchType" data-type="nps">按英寸</view>
        <view class="search-type-item {{searchType === 'od' ? 'active' : ''}}" 
              bindtap="switchSearchType" data-type="od">按外径</view>
      </view>

      <!-- 搜索框 -->
      <view class="search-container">
        <input class="search-input" type="text" 
               placeholder="{{searchType === 'dn' ? '请输入公称直径DN' : searchType === 'nps' ? '请输入英寸值' : '请输入外径(mm)'}}" 
               bindinput="onSearchInput"
               value="{{searchValue}}"
               confirm-type="search" />
      </view>
    </view>

    <!-- 表格部分 -->
    <view class="table-section">
      <view class="table-header">
        <text class="table-title">📋 管线尺寸对照表</text>
      </view>
      
      <view class="pipe-table-container">
        <!-- 表格标题 -->
        <view class="table-head">
          <view class="table-cell">公称直径(DN)</view>
          <view class="table-cell">英寸</view>
          <view class="table-cell">φ外径(mm)</view>
        </view>

        <!-- 表格内容 -->
        <view class="table-container">
          <block wx:if="{{filteredData.length > 0}}">
            <view class="table-row" wx:for="{{filteredData}}" wx:key="dn">
              <view class="table-cell">{{item.dn}}</view>
              <view class="table-cell">{{item.nps}}</view>
              <view class="table-cell">{{item.od}}</view>
            </view>
          </block>
          <view class="no-data" wx:else>
            <text class="no-data-icon">🔍</text>
            <text class="no-data-text">未找到匹配数据</text>
          </view>
        </view>
      </view>
      
      <!-- 表格说明 -->
      <view class="info-section">
        <view class="info-item">
          <text class="info-icon">ℹ️</text>
          <text class="info-text">公称直径(DN) = 管径尺寸，英寸 = 公称管径(英寸)，φ外径 = 实际外径(mm)</text>
        </view>
      </view>
    </view>
  </view>
</view>
