/* pages/pipe-size-table/pipe-size-table.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20rpx 0 80rpx 0;
}

/* 模块卡片样式 */
.module-box {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  margin: 16rpx 12rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 搜索类型头部 */
.search-type-header {
  padding: 20rpx 24rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
}

.search-type-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

/* 搜索类型选择器 */
.search-type-container {
  display: flex;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  overflow: hidden;
}

.search-type-item {
  flex: 1;
  text-align: center;
  padding: 12rpx 8rpx;
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-type-item.active {
  color: #ffffff;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  font-weight: 600;
}

/* 搜索框样式 */
.search-container {
  margin-bottom: 8rpx;
}

.search-input {
  width: 100%;
  height: 72rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-sizing: border-box;
  font-size: 30rpx;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
}

.search-input:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.08);
}

/* 表格区域 */
.table-section {
  padding: 16rpx 24rpx 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.table-header {
  padding: 12rpx 0 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  margin-bottom: 16rpx;
}

.table-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

/* 管线尺寸表格样式 */
.pipe-table-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  overflow: hidden;
  margin-bottom: 16rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.table-head {
  display: flex;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.table-container {
  max-height: 700rpx;
  overflow-y: auto;
  background-color: white;
}

.table-row {
  display: flex;
  background-color: white;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.table-row:nth-child(even) {
  background-color: #f8fafc;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: #eff6ff;
}

.table-cell {
  flex: 1;
  padding: 16rpx 12rpx;
  text-align: center;
  color: #1e293b;
  font-weight: 500;
}

.table-head .table-cell {
  color: white;
  font-weight: 600;
  padding: 18rpx 12rpx;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: white;
}

.no-data-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  opacity: 0.6;
}

.no-data-text {
  font-size: 28rpx;
  color: #64748b;
}

/* 信息说明区域 */
.info-section {
  padding: 12rpx 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.info-icon {
  font-size: 18rpx;
  opacity: 0.7;
}

.info-text {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  line-height: 1.4;
}
