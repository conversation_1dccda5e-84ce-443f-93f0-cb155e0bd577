Page({
  data: {
    // 管线尺寸对照表数据
    pipeData: [
      { dn: 6, nps: "1/8", od: 10.3 },
      { dn: 8, nps: "1/4", od: 13.7 },
      { dn: 10, nps: "3/8", od: 17.1 },
      { dn: 15, nps: "1/2", od: 21.3 },
      { dn: 20, nps: "3/4", od: 26.7 },
      { dn: 25, nps: "1", od: 33.4 },
      { dn: 32, nps: "1¼", od: 42.2 },
      { dn: 40, nps: "1½", od: 48.3 },
      { dn: 50, nps: "2", od: 60.3 },
      { dn: 65, nps: "2½", od: 73.0 },
      { dn: 80, nps: "3", od: 88.9 },
      { dn: 90, nps: "3½", od: 101.6 },
      { dn: 100, nps: "4", od: 114.3 },
      { dn: 125, nps: "5", od: 141.3 },
      { dn: 150, nps: "6", od: 168.3 },
      { dn: 200, nps: "8", od: 219.1 },
      { dn: 250, nps: "10", od: 273.0 },
      { dn: 300, nps: "12", od: 323.8 },
      { dn: 350, nps: "14", od: 355.6 },
      { dn: 400, nps: "16", od: 406.4 },
      { dn: 450, nps: "18", od: 457 },
      { dn: 500, nps: "20", od: 508 },
      { dn: 550, nps: "22", od: 559 },
      { dn: 600, nps: "24", od: 610 },
      { dn: 650, nps: "26", od: 660 },
      { dn: 700, nps: "28", od: 711 },
      { dn: 750, nps: "30", od: 762 },
      { dn: 800, nps: "32", od: 813 }
    ],
    filteredData: [],
    searchValue: '',
    searchType: 'dn' // 默认按DN搜索
  },

  onLoad: function() {
    this.setData({
      filteredData: this.data.pipeData
    });
  },

  onShow: function() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '管线尺寸对照表'
    });
  },

  // 格式化输入值，限制只能输入数字和一个小数点，最多两位小数
  formatInputValue: function(value) {
    if (!value) return value;
    
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    
    return value;
  },

  // 管线尺寸对照表相关函数
  // 输入框输入
  onSearchInput: function(e) {
    let value = e.detail.value.trim();
    
    // 如果是按外径搜索，需要限制输入格式
    if (this.data.searchType === 'od') {
      value = this.formatInputValue(value);
    }
    
    this.setData({
      searchValue: value
    });
    this.filterData(value);
  },

  // 切换搜索类型
  switchSearchType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      searchType: type,
      searchValue: '' // 切换搜索类型时清空搜索框
    });
    this.filterData(''); // 重置筛选
  },

  // 筛选数据
  filterData: function(value) {
    if (!value) {
      // 如果搜索框为空，显示所有数据
      this.setData({
        filteredData: this.data.pipeData
      });
      return;
    }

    const searchType = this.data.searchType;
    let result = [];

    switch (searchType) {
      case 'dn':
        const dnValue = parseInt(value);
        if (!isNaN(dnValue)) {
          // 如果是数字，精确匹配 DN
          result = this.data.pipeData.filter(item => item.dn === dnValue);
          
          // 如果没有精确匹配，尝试查找接近的值
          if (result.length === 0) {
            result = this.data.pipeData.filter(item => 
              String(item.dn).includes(value)
            );
          }
        }
        break;
      
      case 'nps':
        // NPS 尺寸匹配
        result = this.data.pipeData.filter(item => 
          item.nps.toLowerCase().includes(value.toLowerCase())
        );
        break;
      
      case 'od':
        try {
          // 外径匹配，可能是浮点数
          const odValue = parseFloat(value);
          
          if (!isNaN(odValue)) {
            // 先尝试精确匹配
            result = this.data.pipeData.filter(item => 
              Math.abs(item.od - odValue) < 0.1
            );
            
            // 如果没有精确匹配，尝试查找接近的值
            if (result.length === 0) {
              result = this.data.pipeData.filter(item => 
                String(item.od).includes(value)
              );
            }
          }
        } catch (e) {
          console.error("搜索外径时出错:", e);
        }
        break;
    }

    this.setData({
      filteredData: result.length > 0 ? result : []
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-管线尺寸查询',
      path: '/pages/pipe-size-table/pipe-size-table'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-管线尺寸查询',
      query: 'from=timeline'
    };
  }
});
