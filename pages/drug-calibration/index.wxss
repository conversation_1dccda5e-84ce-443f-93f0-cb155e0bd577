/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0 0 40rpx 0;
}

/* 卡片通用样式 */
.calculator-card,
.input-section {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 28rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin: 24rpx 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.input-section {
  margin-bottom: 0;
  padding: 0;
}

/* 表单头部 */
.form-header {
  padding: 36rpx 32rpx 28rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
}

.form-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.form-header-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  letter-spacing: 0.5rpx;
}

.form-header-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

/* 表单内容 */
.form-content {
  padding: 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.input-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 36rpx;
}

.input-group {
  flex: 1;
  margin-bottom: 36rpx;
}

.input-row .input-group {
  margin-bottom: 0;
}

.input-label {
  display: block;
  margin-bottom: 12rpx;
  color: #475569;
  font-size: 26rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

.input-desc {
  font-size: 22rpx;
  color: #64748b;
  margin-top: 8rpx;
  line-height: 1.5;
  opacity: 0.8;
}

.input-field {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-sizing: border-box;
  font-size: 28rpx;
  color: #1e293b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05),
    0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.input-field:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow:
    0 0 0 6rpx rgba(59, 130, 246, 0.1),
    inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05),
    0 4rpx 12rpx rgba(59, 130, 246, 0.15);
  transform: translateY(-1rpx);
}

/* 结果显示区域 */
.result-box {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 24rpx 20rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.result-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.result-box.compact {
  padding: 28rpx;
}

.result-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.result-row:last-child {
  margin-bottom: 0;
}

.result-item {
  flex: 1;
  padding: 20rpx 16rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 72rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow:
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8),
    0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.result-item.compact-item {
  padding: 24rpx 20rpx;
}

.label {
  color: #64748b;
  font-size: 22rpx;
  margin-bottom: 8rpx;
  font-weight: 500;
  letter-spacing: 0.3rpx;
  text-transform: uppercase;
  opacity: 0.8;
}

.value {
  color: #1e293b !important;
  font-size: 30rpx !important;
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

.total-value {
  font-size: 36rpx !important;
  font-weight: 800;
  text-align: center;
  color: #0f172a !important;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 子标题样式 */
.sub-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #1e293b;
  margin: 32rpx 20rpx 20rpx;
  padding: 24rpx 28rpx;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  box-shadow:
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8),
    0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  letter-spacing: 0.5rpx;
  position: relative;
}

.sub-title::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0 3rpx 3rpx 0;
}

.sub-title::before {
  content: "⚗️";
  margin-right: 12rpx;
  font-size: 26rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
