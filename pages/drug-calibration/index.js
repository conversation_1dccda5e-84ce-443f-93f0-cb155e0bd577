// index.js
Page({
  data: {
    // 药剂标定数据
    volume: '',
    time: '',
    dailyDosage: '',
    result: { dosage: '0.00' },
    method2Results: {
      t20: '0',
      t30: '0',
      t60: '0',
      t120: '0'
    }
  },

  // 输入处理
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [field]: value
    });
    
    // 实时计算
    if (field === 'volume' || field === 'time') {
      this.calculateDosage();
    }
    if (field === 'dailyDosage') {
      this.calculateMethod2();
    }
  },

  // 计算日加药量
  calculateDosage() {
    const { volume, time } = this.data;
    const vol = parseFloat(volume) || 0;
    const t = parseFloat(time) || 0;
    
    // 日加药量(L/d) = 量杯体积(mL) * 86400(s/d) / 时间(s) / 1000(mL/L)
    let dosage = '0.00';
    if (t > 0) {
      dosage = (vol * 86400 / t / 1000).toFixed(2);
    }
    
    this.setData({
      result: { dosage }
    });
  },

  // 计算标定方法二
  calculateMethod2() {
    const { dailyDosage } = this.data;
    const dosage = parseFloat(dailyDosage) || 0;
    
    // 不同时间段的加药量计算
    const results = {
      t20: dosage > 0 ? (dosage * 20 / 1440).toFixed(0) : '0',  // 20分钟
      t30: dosage > 0 ? (dosage * 30 / 1440).toFixed(0) : '0',  // 30分钟
      t60: dosage > 0 ? (dosage * 60 / 1440).toFixed(0) : '0',  // 1小时
      t120: dosage > 0 ? (dosage * 120 / 1440).toFixed(0) : '0' // 2小时
    };
    
    this.setData({
      method2Results: results
    });
  },

  onShareAppMessage() {
    return {
      title: '平台常用计算工具-药剂标定计算器',
      path: '/pages/drug-calibration/index'
    };
  },

  onShareTimeline() {
    return {
      title: '平台常用计算工具-药剂标定计算器',
      query: 'from=timeline'
    };
  }
});
