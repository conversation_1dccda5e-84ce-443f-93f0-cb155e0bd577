// pages/oil-calculator/index.js
Page({
  data: {
    calculatorType: 'single', // single, batch
    absorbance: '', // 分光度
    waterVolume: '', // 水相体积
    showResult: false,
    result: 0,
    curveTypes: ['M=(10.79*A + 0.006)', 'M=(21.904*A + 0.3327)', '自定义'],
    curveTypeIndex: 0, // 默认选择第一种回归曲线
    customM: '', // 自定义参数M值
    customCoefA: '', // 自定义系数A
    customConstant: '', // 自定义常数项
    // 化验相关信息
    testDate: '', // 化验日期
    tester: '', // 化验人员
    // 含油批量计算相关数据
    sampleLocations: ['分离器A', '分离器B', '高频A', '高频B', '撇油器', '气浮AB', '气浮C', '海三', '注水泵', '自定义'],
    batchRecords: [],
    // 分享相关
    tempImagePath: '', // 临时图片路径
    shareType: 'oil'  // 分享类型
  },

  onLoad() {
    this.initBatchRecords();
    this.loadBatchRecords();
    this.initTestDate();
  },

  // 初始化化验日期为今天
  initTestDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');

    this.setData({
      testDate: `${year}-${month}-${day}`
    });
  },

  // 处理化验日期变更
  changeTestDate(e) {
    this.setData({
      testDate: e.detail.value
    });
  },

  // 处理化验人员输入
  inputTester(e) {
    this.setData({
      tester: e.detail.value
    });
  },

  // 初始化含油批量计算表格数据
  initBatchRecords() {
    const { sampleLocations } = this.data;
    const batchRecords = sampleLocations.slice(0, 9).map((location, index) => {
      return {
        locationIndex: index,
        locationCustomName: '', // 自定义地点名称
        absorbance: '',
        waterVolume: '',
        result: ''
      };
    });
    
    this.setData({ batchRecords });
  },

  // 加载批量计算记录
  loadBatchRecords() {
    try {
      const savedRecords = wx.getStorageSync('batchOilRecords');
      if (savedRecords && savedRecords.length > 0) {
        this.setData({ batchRecords: savedRecords });
      }
    } catch (e) {
      console.log('加载批量记录失败:', e);
    }
  },

  // 切换计算器类型
  switchCalculatorType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      calculatorType: type,
      showResult: false
    });

    // 如果切换到批量计算，确保加载数据
    if (type === 'batch') {
      this.loadBatchRecords();
    }
  },

  // 输入分光度
  inputAbsorbance(e) {
    this.setData({ absorbance: e.detail.value });
  },

  // 输入水相体积
  inputWaterVolume(e) {
    this.setData({ waterVolume: e.detail.value });
  },

  // 选择回归曲线
  changeCurveType(e) {
    this.setData({ 
      curveTypeIndex: parseInt(e.detail.value),
      customM: '',
      customCoefA: '',
      customConstant: ''
    });
  },

  // 输入自定义系数A
  inputCustomCoefA(e) {
    this.setData({ customCoefA: e.detail.value });
  },

  // 输入自定义常数项
  inputCustomConstant(e) {
    this.setData({ customConstant: e.detail.value });
  },

  // 含油计算
  calculateOil() {
    const { absorbance, waterVolume, curveTypeIndex, customCoefA, customConstant } = this.data;
    
    if (!absorbance) {
      wx.showToast({
        title: '请输入分光度',
        icon: 'none'
      });
      return;
    }
    if (!waterVolume) {
      wx.showToast({
        title: '请输入水相体积',
        icon: 'none'
      });
      return;
    }

    // 验证自定义参数
    if (curveTypeIndex === 2) {
      if (!customCoefA || !customConstant) {
        wx.showToast({
          title: '请输入自定义参数',
          icon: 'none'
        });
        return;
      }
    }

    let M = 0;
    const A = parseFloat(absorbance);
    const V = parseFloat(waterVolume);
    
    // 根据不同回归曲线计算M值
    if (curveTypeIndex === 0) {
      // M = (10.79*A + 0.006)
      M = 10.79 * A + 0.006;
    } else if (curveTypeIndex === 1) {
      // M = (21.904*A + 0.3327)
      M = 21.904 * A + 0.3327;
    } else {
      // 自定义M值 = (coefA * A + constant)
      const coefA = parseFloat(customCoefA);
      const constant = parseFloat(customConstant);
      M = coefA * A + constant;
    }
    
    // 计算含油量(mg/L): C = M/V * 1000
    const result = (M / V) * 1000;
    
    this.setData({
      result: result.toFixed(2),
      showResult: true
    });
  },

  // 批量计算相关输入处理函数
  changeSampleLocation(e) {
    const index = e.currentTarget.dataset.index;
    const locationIndex = parseInt(e.detail.value);

    const { batchRecords } = this.data;
    batchRecords[index].locationIndex = locationIndex;

    // 如果选择了自定义，则清空自定义名称
    if (locationIndex === 9) {
      batchRecords[index].locationCustomName = '';
    }

    this.setData({ batchRecords });

    // 检查是否可以自动计算当前行
    this.calculateSingleRow(index);
  },

  // 输入自定义地点名称
  inputCustomLocationName(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const batchRecords = this.data.batchRecords;

    batchRecords[index].locationCustomName = value;
    this.setData({ batchRecords });
  },

  // 从自定义输入框返回到选择器
  returnToPicker(e) {
    const index = e.currentTarget.dataset.index;
    const batchRecords = this.data.batchRecords;
    // 将locationIndex设为0，切换回选择器
    batchRecords[index].locationIndex = 0;
    this.setData({ batchRecords });
  },

  // 输入批量分光度
  inputBatchAbsorbance(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const batchRecords = this.data.batchRecords;
    
    batchRecords[index].absorbance = value;
    this.setData({ batchRecords });
    
    // 实时计算单行结果
    this.calculateSingleRow(index);
  },

  // 输入批量水相体积
  inputBatchWaterVolume(e) {
    const { index } = e.currentTarget.dataset;
    const value = e.detail.value;
    const batchRecords = this.data.batchRecords;
    
    batchRecords[index].waterVolume = value;
    this.setData({ batchRecords });
    
    // 实时计算单行结果
    this.calculateSingleRow(index);
  },

  // 计算单行含油量
  calculateSingleRow(index) {
    const { batchRecords, curveTypeIndex, customCoefA, customConstant } = this.data;
    const record = batchRecords[index];
    
    if (record.absorbance && record.waterVolume) {
      const A = parseFloat(record.absorbance);
      const V = parseFloat(record.waterVolume);
      
      if (!isNaN(A) && !isNaN(V) && V > 0) {
        let M = 0;
        
        // 根据不同回归曲线计算M值
        if (curveTypeIndex === 0) {
          M = 10.79 * A + 0.006;
        } else if (curveTypeIndex === 1) {
          M = 21.904 * A + 0.3327;
        } else {
          // 自定义参数需要验证
          const coefA = parseFloat(customCoefA);
          const constant = parseFloat(customConstant);
          if (!isNaN(coefA) && !isNaN(constant)) {
            M = coefA * A + constant;
          } else {
            return; // 自定义参数无效，不计算
          }
        }
        
        // 计算含油量(mg/L): C = M/V * 1000
        const result = (M / V) * 1000;
        batchRecords[index].result = result.toFixed(2);
        this.setData({ batchRecords });
      }
    }
  },

  // 添加新行
  addNewRow() {
    const batchRecords = this.data.batchRecords;
    batchRecords.push({
      locationIndex: 9, // 默认为自定义
      locationCustomName: '',
      absorbance: '',
      waterVolume: '',
      result: ''
    });
    
    this.setData({ batchRecords });
  },

  // 删除行
  deleteRow(e) {
    const index = e.currentTarget.dataset.index;
    const batchRecords = this.data.batchRecords;
    
    batchRecords.splice(index, 1);
    this.setData({ batchRecords });
    
    wx.showToast({
      title: '已删除该行',
      icon: 'success'
    });
  },

  // 清除所有含油记录
  clearAllBatchRecords() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有含油批量计算记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.initBatchRecords();
          wx.removeStorageSync('batchOilRecords');
          wx.showToast({
            title: '已清除所有记录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 保存批量计算结果
  saveBatchResults() {
    const { batchRecords, curveTypeIndex, customCoefA, customConstant } = this.data;
    
    // 验证自定义参数
    if (curveTypeIndex === 2) {
      if (!customCoefA || !customConstant) {
        wx.showToast({
          title: '请先设置自定义参数',
          icon: 'none'
        });
        return;
      }
    }
    
    let hasValidData = false;
    const coefA = curveTypeIndex === 2 ? parseFloat(customCoefA) : 0;
    const constant = curveTypeIndex === 2 ? parseFloat(customConstant) : 0;
    
    // 计算每一行
    batchRecords.forEach((record, index) => {
      if (record.absorbance && record.waterVolume) {
        const A = parseFloat(record.absorbance);
        const V = parseFloat(record.waterVolume);
        
        if (!isNaN(A) && !isNaN(V) && V > 0) {
          // 计算M值
          let M = 0;
          if (curveTypeIndex === 0) {
            M = 10.79 * A + 0.006;
          } else if (curveTypeIndex === 1) {
            M = 21.904 * A + 0.3327;
          } else {
            M = coefA * A + constant;
          }
          
          // 计算含油量(mg/L): C = M/V * 1000
          const result = (M / V) * 1000;
          
          batchRecords[index].result = result.toFixed(2);
          hasValidData = true;
        }
      }
    });
    
    if (hasValidData) {
      this.setData({ batchRecords });
      
      // 保存批量计算结果到本地存储
      wx.setStorageSync('batchOilRecords', batchRecords);
      
      wx.showToast({
        title: '已保存',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '请输入有效数据',
        icon: 'none'
      });
    }
  },

  onShareAppMessage() {
    return {
      title: '平台常用计算工具-含油计算器',
      path: '/pages/oil-calculator/index'
    };
  },

  onShareTimeline() {
    return {
      title: '平台常用计算工具-含油计算器',
      query: 'from=timeline'
    };
  },

  // 生成并分享表格截图
  generateAndShareTableImage() {
    wx.showLoading({
      title: '正在生成截图...',
    });

    try {
      const query = wx.createSelectorQuery();
      query.select('#shareCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0] || !res[0].node) {
            wx.hideLoading();
            wx.showToast({
              title: '无法获取画布',
              icon: 'none'
            });
            return;
          }

          // 获取表格信息
          query.select('.batch-table').boundingClientRect().exec((result) => {
            if (!result || !result[0]) {
              wx.hideLoading();
              wx.showToast({
                title: '获取表格失败',
                icon: 'none'
              });
              return;
            }

            const tableRect = result[0];
            const canvas = res[0].node;
            const dpr = wx.getWindowInfo().pixelRatio || 1;

            // 计算必要的表格高度
            const rowHeight = 40;
            const headerHeight = 50; // 增加表头高度，容纳两行文本
            const recordsCount = this.data.batchRecords.length;
            const titleHeight = 50; // 标题部分高度
            const footerHeight = 40; // 底部时间信息高度

            // 设置Canvas尺寸 - 确保足够显示完整表格
            const canvasWidth = tableRect.width || 320;
            const canvasHeight = titleHeight + headerHeight + (recordsCount * rowHeight) + footerHeight;

            // 明确设置canvas宽高
            canvas.width = canvasWidth * dpr;
            canvas.height = canvasHeight * dpr;

            console.log('Canvas尺寸:', canvasWidth, 'x', canvasHeight, '记录数:', recordsCount);

            const ctx = canvas.getContext('2d');
            if (!ctx) {
              wx.hideLoading();
              wx.showToast({
                title: '创建绘图上下文失败',
                icon: 'none'
              });
              return;
            }

            // 清空画布并缩放
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.scale(dpr, dpr);

            // 先填充背景色
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);

            // 绘制表格内容
            this.renderTable(ctx, canvasWidth, canvasHeight, tableRect);

            // 延迟一下再导出图片，确保绘制完成
            setTimeout(() => {
              wx.canvasToTempFilePath({
                canvas,
                success: (res) => {
                  wx.hideLoading();

                  // 保存临时文件路径
                  this.setData({
                    tempImagePath: res.tempFilePath,
                    shareType: 'oil'
                  });

                  // 检查相册权限并分享图片
                  this.checkAndShareImage(res.tempFilePath);
                },
                fail: (err) => {
                  wx.hideLoading();
                  console.error('生成图片失败', err);
                  wx.showToast({
                    title: '生成图片失败：' + (err.errMsg || '未知错误'),
                    icon: 'none'
                  });
                }
              }, this);
            }, 300);
          });
        });
    } catch (err) {
      wx.hideLoading();
      console.error('截图过程出错', err);
      wx.showToast({
        title: '截图失败，请重试',
        icon: 'none'
      });
    }
  },

  // 检查权限并分享图片
  checkAndShareImage(imagePath) {
    // 先尝试直接调用分享
    wx.showShareImageMenu({
      path: imagePath,
      success: (res) => {
        console.log('分享成功', res);
      },
      fail: (err) => {
        console.error('分享失败', err);

        // 如果失败是因为权限问题
        if (err.errMsg && (err.errMsg.indexOf('appid privacy') >= 0 || err.errMsg.indexOf('auth deny') >= 0)) {
          // 尝试获取权限
          this.requestAlbumAuth(imagePath);
        } else {
          wx.showToast({
            title: '分享失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },

  // 请求相册权限
  requestAlbumAuth(imagePath) {
    wx.getSetting({
      success: (res) => {
        // 如果没有权限或权限被拒绝
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.showModal({
            title: '需要授权',
            content: '需要获取保存到相册的权限，才能分享图片',
            confirmText: '去授权',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 请求权限
                wx.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => {
                    // 获得权限后再次分享
                    this.shareImageAfterAuth(imagePath);
                  },
                  fail: (authErr) => {
                    // 用户拒绝授权，引导用户到设置页面开启
                    this.openSettingForAlbum(imagePath);
                  }
                });
              }
            }
          });
        } else {
          // 已有权限，直接分享
          this.shareImageAfterAuth(imagePath);
        }
      },
      fail: (err) => {
        console.error('获取设置失败', err);
        wx.showToast({
          title: '获取权限信息失败',
          icon: 'none'
        });
      }
    });
  },

  // 打开设置页面
  openSettingForAlbum(imagePath) {
    wx.showModal({
      title: '授权提示',
      content: '需要在设置中开启"保存到相册"权限，是否去设置？',
      confirmText: '去设置',
      cancelText: '取消',
      success: (modalRes) => {
        if (modalRes.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.writePhotosAlbum']) {
                // 用户在设置页面开启了权限
                this.shareImageAfterAuth(imagePath);
              } else {
                wx.showToast({
                  title: '未获得权限，无法分享',
                  icon: 'none'
                });
              }
            }
          });
        }
      }
    });
  },

  // 获取权限后分享图片
  shareImageAfterAuth(imagePath) {
    wx.showShareImageMenu({
      path: imagePath,
      success: (res) => {
        console.log('分享成功', res);
      },
      fail: (err) => {
        console.error('分享失败', err);
        wx.showToast({
          title: '分享失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 直接渲染表格到画布上
  renderTable(ctx, canvasWidth, canvasHeight, tableRect) {
    // 绘制标题 - 只显示"含油化验记录"
    ctx.fillStyle = '#333333';
    ctx.font = 'normal bold 16px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle'; // 确保标题也垂直居中
    ctx.fillText('含油化验记录', canvasWidth / 2, 30);

    // 增加表格顶部的距离，避免与标题重叠
    const tableTop = 60;

    // 计算表格内容高度，确保能显示完整表格
    const rowHeight = 40;
    const headerHeight = 50; // 增加表头高度，容纳两行文本
    const recordsCount = this.data.batchRecords.length;
    const tableHeight = headerHeight + (recordsCount * rowHeight) + 10; // 额外添加10px的边距

    // 绘制化验信息 (如果有)
    if (this.data.testDate || this.data.tester) {
      ctx.font = 'normal 12px sans-serif';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'alphabetic';

      let infoText = '';
      if (this.data.testDate) {
        infoText += `化验日期: ${this.data.testDate}`;
      }

      if (this.data.tester) {
        if (infoText) infoText += '   ';
        infoText += `化验人员: ${this.data.tester}`;
      }

      ctx.fillText(infoText, 10, tableTop - 10);
    }

    // 绘制表格边框
    ctx.lineWidth = 1;
    ctx.strokeStyle = '#dddddd';
    ctx.strokeRect(10, tableTop, canvasWidth - 20, tableHeight);

    // 绘制表头
    ctx.fillStyle = '#f7f9fc';
    ctx.fillRect(10, tableTop, canvasWidth - 20, headerHeight);

    // 绘制表头和数据前先统一设置文本属性 - 确保垂直居中
    ctx.fillStyle = '#333333';
    ctx.font = 'normal 12px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle'; // 确保垂直居中

    // 计算表头实际的垂直中心位置
    const headerTitleY = tableTop + headerHeight * 0.33; // 第一行位置（33%处）
    const headerUnitY = tableTop + headerHeight * 0.77; // 第二行位置（77%处）

    // 调整表头列宽
    const columnWidths = [canvasWidth * 0.08, canvasWidth * 0.22, canvasWidth * 0.2, canvasWidth * 0.2, canvasWidth * 0.27];
    const headerLabels = ['序号', '取样地点', '分光度', '水相体积', '含油量'];
    const headerUnits = ['', '', '(A)', '(mL)', '(mg/L)'];

    // 绘制表头分隔线
    for (let i = 1; i < headerLabels.length; i++) {
      const x = 10 + columnWidths.slice(0, i).reduce((a, b) => a + b, 0);
      ctx.beginPath();
      ctx.moveTo(x, tableTop);
      ctx.lineTo(x, tableTop + headerHeight);
      ctx.strokeStyle = '#dddddd';
      ctx.stroke();
    }

    // 绘制表头内容
    let xPos = 10;
    for (let i = 0; i < headerLabels.length; i++) {
      const colCenterX = xPos + columnWidths[i] / 2;

      // 绘制表头第一行（主标题）- 确保中心对齐
      ctx.fillText(headerLabels[i], colCenterX, headerTitleY);

      // 绘制表头第二行（单位）- 确保中心对齐
      if (headerUnits[i]) {
        ctx.fillText(headerUnits[i], colCenterX, headerUnitY);
      }

      xPos += columnWidths[i];
    }

    // 数据行分隔线
    ctx.beginPath();
    ctx.moveTo(10, tableTop + headerHeight);
    ctx.lineTo(canvasWidth - 10, tableTop + headerHeight);
    ctx.strokeStyle = '#dddddd';
    ctx.lineWidth = 1.5; // 加粗表头和数据区域的分隔线
    ctx.stroke();
    ctx.lineWidth = 1; // 恢复默认线宽

    // 绘制表格数据
    const { batchRecords } = this.data;
    let yPos = tableTop + headerHeight; // 从表头下方开始

    // 绘制数据行
    for (let i = 0; i < batchRecords.length; i++) {
      const record = batchRecords[i];

      // 行背景色 - 交替色
      if (i % 2 === 1) {
        ctx.fillStyle = '#f9f9f9';
        ctx.fillRect(10, yPos, canvasWidth - 20, rowHeight);
      }

      // 绘制横向分隔线
      ctx.beginPath();
      ctx.moveTo(10, yPos);
      ctx.lineTo(canvasWidth - 10, yPos);
      ctx.strokeStyle = '#eeeeee';
      ctx.stroke();

      // 再次确认文本设置，防止在绘制过程中被改变
      ctx.fillStyle = '#333333';
      ctx.font = 'normal 12px sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle'; // 明确重设基线为中点

      // 计算当前行中心Y坐标 - 确保垂直居中
      const rowMiddleY = yPos + rowHeight / 2;

      // 逐列绘制数据
      xPos = 10;

      // 序号
      ctx.fillText((i + 1).toString(), xPos + columnWidths[0] / 2, rowMiddleY);
      xPos += columnWidths[0];

      // 取样地点
      const locationName = record.locationIndex === 9 ? (record.locationCustomName || '自定义') : this.data.sampleLocations[record.locationIndex];
      ctx.fillText(locationName, xPos + columnWidths[1] / 2, rowMiddleY);
      xPos += columnWidths[1];

      // 分光度
      ctx.fillText(record.absorbance || '-', xPos + columnWidths[2] / 2, rowMiddleY);
      xPos += columnWidths[2];

      // 水相体积
      ctx.fillText(record.waterVolume || '-', xPos + columnWidths[3] / 2, rowMiddleY);
      xPos += columnWidths[3];

      // 含油量
      ctx.fillText(record.result || '-', xPos + columnWidths[4] / 2, rowMiddleY);

      // 绘制垂直分隔线
      for (let j = 1; j < headerLabels.length; j++) {
        const x = 10 + columnWidths.slice(0, j).reduce((a, b) => a + b, 0);
        ctx.beginPath();
        ctx.moveTo(x, yPos);
        ctx.lineTo(x, yPos + rowHeight);
        ctx.strokeStyle = '#eeeeee';
        ctx.stroke();
      }

      yPos += rowHeight;
    }

    // 绘制底部边框
    ctx.beginPath();
    ctx.moveTo(10, yPos);
    ctx.lineTo(canvasWidth - 10, yPos);
    ctx.strokeStyle = '#dddddd';
    ctx.stroke();

    // 绘制时间
    const date = new Date();
    const dateStr = `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
    ctx.font = 'normal 10px sans-serif';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'alphabetic'; // 只在绘制时间时恢复默认基线
    ctx.fillText(`生成时间: ${dateStr}`, canvasWidth - 20, yPos + 20);

    // 辅助函数：格式化日期，补零
    function pad(n) {
      return n < 10 ? '0' + n : n;
    }
  }
});
