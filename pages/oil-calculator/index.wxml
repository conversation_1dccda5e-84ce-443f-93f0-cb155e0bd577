<!-- pages/oil-calculator/index.wxml -->
<view class="container">
  <!-- 隐藏的canvas，用于生成分享图片 -->
  <canvas type="2d" id="shareCanvas" style="width: 300px; height: 400px; position: absolute; left: -2000px; top: 0px;"></canvas>

  <!-- 计算器类型选择 -->
  <view class="calculator-section">
    <view class="calculator-type">
      <view class="type-item {{calculatorType === 'single' ? 'active' : ''}}" bindtap="switchCalculatorType" data-type="single">单次计算</view>
      <view class="type-item {{calculatorType === 'batch' ? 'active' : ''}}" bindtap="switchCalculatorType" data-type="batch">批量计算</view>
    </view>

    <!-- 单次含油计算器 -->
    <view class="calculator-form" wx:if="{{calculatorType === 'single'}}">
      <view class="input-group">
        <text class="input-label">选择回归曲线</text>
        <picker bindchange="changeCurveType" value="{{curveTypeIndex}}" range="{{curveTypes}}">
          <view class="picker">
            {{curveTypes[curveTypeIndex]}}
          </view>
        </picker>
      </view>
      
      <!-- 自定义参数输入 -->
      <view class="input-group" wx:if="{{curveTypeIndex === 2}}">
        <text class="input-label">自定义参数M</text>
        <view class="custom-m-container">
          <text class="formula-text">M = </text>
          <text class="formula-text">(</text>
          <input class="input-field-inline" type="digit" placeholder="请输入" bindinput="inputCustomCoefA" value="{{customCoefA}}"/>
          <text class="formula-text">*A + </text>
          <input class="input-field-inline" type="digit" placeholder="请输入" bindinput="inputCustomConstant" value="{{customConstant}}"/>
          <text class="formula-text">)</text>
        </view>
      </view>
      
      <view class="input-group">
        <text class="input-label">分光度(A)</text>
        <input class="input-field" type="digit" placeholder="请输入分光度" bindinput="inputAbsorbance" value="{{absorbance}}"/>
      </view>
      <view class="input-group">
        <text class="input-label">水相体积(mL)</text>
        <input class="input-field" type="digit" placeholder="请输入水相体积" bindinput="inputWaterVolume" value="{{waterVolume}}"/>
      </view>
      <button class="calculate-btn" bindtap="calculateOil">计算</button>
    </view>

    <!-- 批量计算 -->
    <view class="batch-calculator" wx:if="{{calculatorType === 'batch'}}">
      <view class="curve-selector">
        <view class="label">选择回归曲线</view>
        <picker bindchange="changeCurveType" range="{{curveTypes}}">
          <view class="picker">
            {{curveTypes[curveTypeIndex]}}
          </view>
        </picker>
      </view>
      
      <!-- 自定义参数输入 -->
      <view class="custom-parameters" wx:if="{{curveTypeIndex == 2}}">
        <view class="parameter-item">
          <view class="label">系数A</view>
          <input class="input-box" type="digit" placeholder="请输入系数A" value="{{customCoefA}}" bindinput="inputCustomCoefA" />
        </view>
        <view class="parameter-item">
          <view class="label">常数项</view>
          <input class="input-box" type="digit" placeholder="请输入常数项" value="{{customConstant}}" bindinput="inputCustomConstant" />
        </view>
      </view>

      <!-- 添加化验信息区域 -->
      <view class="test-info-container">
        <view class="test-info-item vertical">
          <view class="label-block">化验时间</view>
          <picker mode="date" value="{{testDate}}" bindchange="changeTestDate">
            <view class="picker {{testDate ? '' : 'placeholder'}}">
              <view class="date-text">{{testDate || '选择日期'}}</view>
            </view>
          </picker>
        </view>
        <view class="test-info-item vertical">
          <view class="label-block">化验人员</view>
          <input class="input-box" placeholder="请输入化验人员" value="{{tester}}" bindinput="inputTester" />
        </view>
      </view>

      <!-- 含油批量计算表格 -->
      <view class="batch-table">
        <view class="table-header">
          <view class="table-cell cell-mini">序号</view>
          <view class="table-cell">取样地点</view>
          <view class="table-cell">分光度(A)</view>
          <view class="table-cell">水相体积(mL)</view>
          <view class="table-cell">含油量(mg/L)</view>
          <view class="table-cell cell-mini">操作</view>
        </view>

        <block wx:for="{{batchRecords}}" wx:key="index">
          <view class="table-row">
            <view class="table-cell cell-mini">{{index + 1}}</view>
            <view class="table-cell position-relative">
              <picker wx:if="{{item.locationIndex !== 9}}" bindchange="changeSampleLocation" data-index="{{index}}" value="{{item.locationIndex}}" range="{{sampleLocations}}">
                <view class="picker">
                  {{sampleLocations[item.locationIndex]}}
                </view>
              </picker>
              <!-- 自定义地点名称输入框，当选择"自定义"选项时显示 -->
              <input wx:if="{{item.locationIndex === 9}}" class="location-custom-input standalone" type="text" placeholder="输入地点名称" bindinput="inputCustomLocationName" data-index="{{index}}" value="{{item.locationCustomName}}"/>
              <!-- 返回选择器按钮 -->
              <view wx:if="{{item.locationIndex === 9}}" class="return-to-picker" bindtap="returnToPicker" data-index="{{index}}">
                <text>↺</text>
              </view>
            </view>
            <view class="table-cell">
              <input class="table-input" type="digit" placeholder="分光度" bindinput="inputBatchAbsorbance" data-index="{{index}}" value="{{item.absorbance}}"/>
            </view>
            <view class="table-cell">
              <input class="table-input" type="digit" placeholder="水相体积" bindinput="inputBatchWaterVolume" data-index="{{index}}" value="{{item.waterVolume}}"/>
            </view>
            <view class="table-cell">
              <text>{{item.result || '-'}}</text>
            </view>
            <view class="table-cell cell-mini">
              <view class="delete-btn" bindtap="deleteRow" data-index="{{index}}">×</view>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 操作按钮 -->
      <view class="batch-action-buttons">
        <view class="action-btn" bindtap="addNewRow">添加一行</view>
      </view>
      
      <view class="batch-buttons">
        <view class="save-btn" bindtap="saveBatchResults">保存</view>
        <view class="clear-btn" bindtap="clearAllBatchRecords">清除全部</view>
      </view>
      
      <!-- 分享按钮 -->
      <view class="share-button-container">
        <view class="share-btn" bindtap="generateAndShareTableImage">分享表格截图</view>
      </view>
    </view>
  </view>

  <!-- 结果显示 -->
  <view class="result-section" wx:if="{{showResult && calculatorType === 'single'}}">
    <view class="result-title">计算结果</view>
    <view class="result-content">
      <view class="result-item">
        <text class="result-label">含油量：</text>
        <text class="result-value">{{result}} mg/L</text>
      </view>
      <view class="result-formula">
        <text>计算公式：C = M/V * 1000</text>
        <text wx:if="{{curveTypeIndex === 0}}">回归曲线 M = (10.79*A + 0.006)</text>
        <text wx:if="{{curveTypeIndex === 1}}">回归曲线 M = (21.904*A + 0.3327)</text>
        <text wx:if="{{curveTypeIndex === 2}}">回归曲线 M = ({{customCoefA || '?'}}*A + {{customConstant || '?'}})</text>
      </view>
    </view>
  </view>
</view>
