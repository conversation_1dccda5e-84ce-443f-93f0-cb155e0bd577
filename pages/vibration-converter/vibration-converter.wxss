/* pages/vibration-converter/vibration-converter.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20rpx 0 80rpx 0;
}

/* 模块卡片样式 */
.module-box {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  margin: 16rpx 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 计算器头部 */
.calculator-header {
  padding: 20rpx 24rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
}

.calculator-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.calculator-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

/* 输入区域 */
.input-section {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.input-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.input-group {
  flex: 1;
}

.input-label {
  display: block;
  margin-bottom: 8rpx;
  color: #475569;
  font-size: 28rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.input-field {
  width: 100%;
  height: 72rpx;
  padding: 0 16rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-sizing: border-box;
  font-size: 28rpx;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
}

.input-field:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.08);
}

/* 一键清除按钮 */
.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  margin: 0 auto;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  width: 160rpx;
  height: 64rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.25);
  transition: all 0.3s ease;
}

.clear-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.25);
}

.clear-icon {
  font-size: 20rpx;
}

.clear-text {
  font-size: 26rpx;
}

/* 表格区域 */
.table-section {
  padding: 16rpx 24rpx 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.table-header {
  padding: 12rpx 0 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  margin-bottom: 16rpx;
}

.table-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

/* 振速位移表格样式 */
.vibration-table {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  overflow: hidden;
  margin-bottom: 16rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  height: 50vh;
  display: flex;
  flex-direction: column;
}

.vibration-table-header {
  display: flex;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.vibration-table-content {
  background-color: white;
  flex: 1;
  overflow-y: auto;
}

.vibration-table-row {
  display: flex;
  background-color: white;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.vibration-table-row:nth-child(even) {
  background-color: #f8fafc;
}

.vibration-table-row:last-child {
  border-bottom: none;
}

.vibration-table-row:hover {
  background-color: #eff6ff;
}

.vibration-table-cell {
  flex: 1;
  padding: 16rpx 12rpx;
  text-align: center;
  color: #1e293b;
  font-weight: 500;
}

.vibration-table-header .vibration-table-cell {
  color: white;
  font-weight: 600;
  padding: 18rpx 12rpx;
}

/* 信息说明区域 */
.info-section {
  padding: 12rpx 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.info-icon {
  font-size: 18rpx;
  opacity: 0.7;
}

.info-text {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  line-height: 1.4;
}
