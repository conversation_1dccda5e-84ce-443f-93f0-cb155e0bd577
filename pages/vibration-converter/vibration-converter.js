Page({
  data: {
    // 振速与位移对照表数据
    directConversionData: [
      { speed: 1, amplitude: 0.25 },
      { speed: 2, amplitude: 0.5 },
      { speed: 3, amplitude: 0.75 },
      { speed: 4, amplitude: 1 },
      { speed: 5, amplitude: 1.25 },
      { speed: 6, amplitude: 1.5 },
      { speed: 7, amplitude: 1.75 },
      { speed: 8, amplitude: 2 },
      { speed: 9, amplitude: 2.25 },
      { speed: 10, amplitude: 2.5 },
      { speed: 15, amplitude: 3.75 },
      { speed: 20, amplitude: 5 },
      { speed: 25, amplitude: 6.25 },
      { speed: 30, amplitude: 7.5 },
      { speed: 40, amplitude: 10 },
      { speed: 50, amplitude: 12.5 }
    ],
    directSpeedValue: '',
    directAmplitudeValue: ''
  },

  onShow: function() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '振速位移换算'
    });
  },

  // 格式化输入值，限制只能输入数字和一个小数点，最多两位小数
  formatInputValue: function(value) {
    if (!value) return value;
    
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    
    return value;
  },

  // 振速位移直接换算相关函数
  // 振速输入
  onDirectSpeedInput: function(e) {
    const value = this.formatInputValue(e.detail.value);
    if (value === '') {
      this.setData({
        directSpeedValue: '',
        directAmplitudeValue: ''
      });
      return;
    }
    
    const speed = parseFloat(value);
    if (!isNaN(speed)) {
      // 根据比例计算位移值：位移(mm) = 振速(mm/s) * 0.25
      const amplitude = this.formatNumber(speed * 0.25);
      
      this.setData({
        directSpeedValue: value,
        directAmplitudeValue: amplitude
      });
    }
  },
  
  // 位移输入
  onDirectAmplitudeInput: function(e) {
    const value = this.formatInputValue(e.detail.value);
    if (value === '') {
      this.setData({
        directSpeedValue: '',
        directAmplitudeValue: ''
      });
      return;
    }
    
    const amplitude = parseFloat(value);
    if (!isNaN(amplitude)) {
      // 根据比例计算振速值：振速(mm/s) = 位移(mm) / 0.25
      const speed = this.formatNumber(amplitude / 0.25);
      
      this.setData({
        directSpeedValue: speed,
        directAmplitudeValue: value
      });
    }
  },
  
  // 一键清除输入
  clearDirectInputs: function() {
    this.setData({
      directSpeedValue: '',
      directAmplitudeValue: ''
    });
  },

  // 数字格式化，保留4位小数
  formatNumber: function(num) {
    return parseFloat(num.toFixed(4)).toString();
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-振速位移换算',
      path: '/pages/vibration-converter/vibration-converter'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-振速位移换算',
      query: 'from=timeline'
    };
  }
});
