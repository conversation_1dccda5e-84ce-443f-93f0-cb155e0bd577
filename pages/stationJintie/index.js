// 一站津贴查询主页面
Page({
  data: {
    // 用户信息
    username: '',
    role: '',
    roleText: '',

    // 页面状态
    currentTab: 'current',
    isLoading: true, // 初始加载状态
    isHistoryLoading: false,
    showAllUsers: false, // 个人用户是否查看全部

    // 登录状态
    isLoggedIn: false,
    isGuestMode: false,
    hasPermission: false, // 是否有一站人员权限

    // 当前津贴数据
    jintieRecords: [],
    currentDataInfo: {}, // 当前数据信息（月度、时间等）

    // 历史记录数据
    selectedYear: '',
    selectedDate: '', // 选中的历史日期
    yearRange: [], // 年份选择范围
    selectedYearIndex: 0, // 选中年份的索引
    historyRecords: [],

    // 修改密码功能已移至"我的"页面

    // 个人年度数据相关
    showPersonalDataModal: false,
    personalDataList: [],
    selectedPersonName: '',
    isLoadingPersonalData: false,
    currentYear: new Date().getFullYear(),
    personalDataTotal: 0
  },

  onLoad: function(options) {
    // 检查微信登录状态和权限
    this.checkWechatLoginStatus();

    // 初始化年份（用于历史记录）
    const today = new Date();
    const currentYear = today.getFullYear();

    // 生成年份选择范围：从2025年到当前年份，不包含未来年份
    const yearRange = [];
    const startYear = 2025; // 津贴系统从2025年开始
    for (let year = startYear; year <= currentYear; year++) {
      yearRange.push(year.toString());
    }

    // 计算当前年份在数组中的索引
    const currentYearIndex = yearRange.indexOf(currentYear.toString());

    this.setData({
      selectedYear: currentYear.toString(),
      yearRange: yearRange,
      selectedYearIndex: currentYearIndex >= 0 ? currentYearIndex : yearRange.length - 1
    });
  },

  onShow: function() {
    // 每次显示页面时检查微信登录状态和权限
    this.checkWechatLoginStatus();
  },

  // 检查微信登录状态和权限
  checkWechatLoginStatus: function() {
    // 检查是否已通过微信登录并绑定系统账号
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isGuestMode = wx.getStorageSync('guest_mode');

    if (wechatToken && userInfo && !isGuestMode) {
      // 已登录并绑定系统账号，检查权限
      const role = userInfo.role;
      const hasPermission = role === 'admin' || role === 'station_staff';

      const roleTexts = {
        'admin': '系统管理员',
        'station_staff': '一站人员',
        'manager': '普通管理员',
        'user': '普通用户'
      };

      this.setData({
        isLoggedIn: true,
        isLoading: false,
        isGuestMode: false,
        hasPermission: hasPermission,
        username: userInfo.real_name || userInfo.username, // 优先显示真实姓名
        role: role,
        roleText: roleTexts[role] || '普通用户'
      });

      // 如果有权限，加载数据
      if (hasPermission) {
        this.loadLatestJintieData();
      }
    } else if (isGuestMode) {
      // 游客模式，需要绑定系统账号
      this.setData({
        isLoggedIn: false,
        isLoading: false,
        isGuestMode: true,
        hasPermission: false
      });
    } else {
      // 未登录，需要先登录
      this.setData({
        isLoggedIn: false,
        isLoading: false,
        isGuestMode: false,
        hasPermission: false
      });
    }
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.showModal({
      title: '需要登录',
      content: '请先在"我的"页面进行微信登录并绑定系统账号',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 提示绑定账号
  showBindTip: function() {
    wx.showModal({
      title: '需要绑定账号',
      content: '您当前是游客模式，需要绑定系统账号才能使用一站津贴查询功能',
      confirmText: '去绑定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 提示权限不足
  showPermissionTip: function() {
    wx.showModal({
      title: '权限不足',
      content: '抱歉，只有一站人员和管理员才能查看津贴信息',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });

    if (tab === 'history' && this.data.historyRecords.length === 0) {
      this.loadHistoryData();
    }
  },

  // 切换查看模式（个人/全部）
  toggleViewAll: function() {
    const showAll = !this.data.showAllUsers;
    this.setData({
      showAllUsers: showAll
    });
    this.loadLatestJintieData();
  },

  // 点击姓名显示个人年度数据
  onNameClick: function(e) {
    const name = e.currentTarget.dataset.name;
    if (!name) return;

    this.setData({
      selectedPersonName: name,
      showPersonalDataModal: true,
      personalDataList: [],
      isLoadingPersonalData: true
    });

    this.loadPersonalYearData(name);
  },

  // 加载个人年度数据
  loadPersonalYearData: function(name) {
    // 检查权限
    if (!this.data.hasPermission) {
      this.showPermissionTip();
      return;
    }

    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');

    if (!wechatToken || !userInfo) {
      // 关闭模态框并显示错误，避免调用checkWechatLoginStatus导致页面状态混乱
      this.setData({
        showPersonalDataModal: false,
        isLoadingPersonalData: false
      });
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'get_personal_yearly_data',
        token: wechatToken,
        username: userInfo.username,
        real_name: userInfo.real_name, // 添加真实姓名
        person_name: name,
        year: this.data.currentYear
      },
      success: (res) => {
        // 处理统一API的返回格式（success字段）和子API的返回格式（status字段）
        const isSuccess = res.data.success === true || res.data.status === 'success';

        if (isSuccess) {
          const records = res.data.data.records || [];
          // 计算合计金额
          let total = 0;
          records.forEach(record => {
            // 移除金额中的逗号并转换为数字
            const amount = parseFloat(record.amount.replace(/,/g, '')) || 0;
            total += amount;
          });

          this.setData({
            personalDataList: records,
            personalDataTotal: total.toLocaleString()
          });
        } else {
          const message = res.data.message || '';
          // 关闭模态框并显示错误
          this.setData({
            showPersonalDataModal: false,
            isLoadingPersonalData: false
          });
          wx.showToast({
            title: message || '获取个人数据失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoadingPersonalData: false });
      }
    });
  },

  // 关闭个人数据模态框
  hidePersonalDataModal: function() {
    this.setData({
      showPersonalDataModal: false,
      personalDataList: [],
      selectedPersonName: '',
      isLoadingPersonalData: false,
      personalDataTotal: 0
    });
  },

  // 查看备注内容
  viewRemark: function(e) {
    const remark = e.currentTarget.dataset.remark;
    if (remark && remark !== '-') {
      wx.showModal({
        title: '备注内容',
        content: remark,
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  // 用户管理功能已移至"我的"页面

  // 修改密码功能已移至"我的"页面
  showChangePassword: function() {
    wx.showModal({
      title: '修改密码',
      content: '请在"我的"页面进行密码修改',
      confirmText: '去我的页面',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 年份选择
  onYearChange: function(e) {
    const selectedIndex = e.detail.value;
    const selectedYear = this.data.yearRange[selectedIndex];
    this.setData({
      selectedYear: selectedYear,
      selectedYearIndex: selectedIndex
    });
    this.loadHistoryData();
  },

  // 加载最新或指定日期的津贴数据
  loadLatestJintieData: function(date) {
    // 检查权限
    if (!this.data.hasPermission) {
      this.showPermissionTip();
      return;
    }

    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');

    if (!wechatToken || !userInfo) {
      this.checkWechatLoginStatus();
      return;
    }

    this.setData({ isLoading: true });

    // 根据用户角色和查看模式决定查询参数
    const { role, showAllUsers } = this.data;
    const isAdmin = role === 'admin' || role === 'manager';
    const queryAllUsers = isAdmin || showAllUsers;

    let requestData = {
      action: 'get_latest_jintie_data',
      token: wechatToken,
      username: userInfo.username,
      real_name: userInfo.real_name, // 添加真实姓名
      view_all: queryAllUsers
    };
    if (date) {
      requestData['date'] = date; // 增加日期参数
    }

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: requestData,
      success: (res) => {
        // 处理统一API的返回格式（success字段）和子API的返回格式（status字段）
        const isSuccess = res.data.success === true || res.data.status === 'success';

        if (isSuccess) {
          const data = res.data.data;
          this.setData({
            jintieRecords: data.records || [],
            currentDataInfo: {
              record_date: data.record_date,
              month_year: data.month_year
            }
          });
        } else {
          const message = res.data.message || '';
          if (message.includes('未登录') || message.includes('token无效') || message.includes('权限不足')) {
            // token失效或权限不足，重新检查登录状态
            this.checkWechatLoginStatus();
          } else {
            wx.showToast({
              title: message || '获取数据失败',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 一键复制功能
  copyJintieData: function() {
    const { currentDataInfo, jintieRecords } = this.data;
    
    if (!currentDataInfo.month_year || jintieRecords.length === 0) {
      wx.showToast({
        title: '暂无数据可复制',
        icon: 'none'
      });
      return;
    }
    
    // 构建复制内容
    let copyContent = `数据月度：${currentDataInfo.month_year}\n`;
    copyContent += `记录时间：${currentDataInfo.record_date}\n\n`;
    
    // 添加每个人的信息
    jintieRecords.forEach((item, index) => {
      copyContent += `${item.name}：${item.total_amount}\n`;
    });
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: copyContent,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载历史数据
  loadHistoryData: function() {
    // 检查权限
    if (!this.data.hasPermission) {
      this.showPermissionTip();
      return;
    }

    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');

    if (!wechatToken || !userInfo) {
      this.checkWechatLoginStatus();
      return;
    }

    this.setData({ isHistoryLoading: true });

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'get_jintie_history',
        token: wechatToken,
        username: userInfo.username,
        real_name: userInfo.real_name, // 添加真实姓名
        year: this.data.selectedYear
      },
      success: (res) => {
        // 处理统一API的返回格式（success字段）和子API的返回格式（status字段）
        const isSuccess = res.data.success === true || res.data.status === 'success';

        if (isSuccess) {
          this.setData({
            historyRecords: res.data.data.records || []
          });
        } else {
          const message = res.data.message || '';
          if (message.includes('未登录') || message.includes('token无效') || message.includes('权限不足')) {
            // token失效或权限不足，重新检查登录状态
            this.checkWechatLoginStatus();
          } else {
            wx.showToast({
              title: message || '获取历史数据失败',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isHistoryLoading: false });
      }
    });
  },

  // 查看历史详情
  viewHistoryDetail: function(e) {
    const date = e.currentTarget.dataset.date;
    this.setData({
      selectedDate: date,
      currentTab: 'current'
    });
    this.loadLatestJintieData(date); // 传递选中的日期
  },

  // 退出登录（跳转到我的页面进行微信登录管理）
  handleLogout: function() {
    wx.showModal({
      title: '退出登录',
      content: '请在"我的"页面管理您的登录状态',
      confirmText: '去我的页面',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    if (this.data.currentTab === 'current') {
      this.loadLatestJintieData();
    } else {
      this.loadHistoryData();
    }

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡，用于模态框内容区域
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '一站津贴查询',
      path: '/pages/stationJintie/index'
    };
  }
});
