/* 一站津贴查询主页面样式 */
.container {
  min-height: 100vh;
  background: #f7f9fc;
  padding-bottom: 40rpx;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f7f9fc;
}

.loading-content {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 登录界面样式 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 40rpx;
  background: #f7f9fc;
}

.login-box {
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600rpx;
}

.login-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 40rpx;
}

.login-notice {
  text-align: center;
  padding: 40rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
}

.notice-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 16rpx;
}

.notice-desc {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 头部样式 - 简约设计 */
.header {
  background: #fff;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid #f0f0f0;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}



.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 用户名容器样式 */
.username-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.username-label {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

.username {
  font-size: 32rpx;
  font-weight: 600;
  color: #2196F3;
  letter-spacing: 1rpx;
}

/* 角色容器样式 */
.role-container {
  display: flex;
  align-items: center;
}

.role-badge {
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  display: inline-block;
}



.header-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
  min-width: 60rpx;
  border: 1rpx solid #e9ecef;
}

.action-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
  margin-bottom: 2rpx;
}

.action-text {
  font-size: 18rpx;
  color: #666;
}

/* 导航样式 - 简约设计 */
.nav-section {
  display: flex;
  background: white;
  margin: 15rpx 20rpx;
  border-radius: 6rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.nav-item {
  flex: 1;
  padding: 16rpx 20rpx;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.nav-item.active {
  background: #2196F3;
  color: white;
}

.nav-text {
  font-size: 24rpx;
  font-weight: 400;
}

/* 内容区域 */
.content {
  padding: 0 20rpx;
}

/* 数据信息显示样式 - 简约设计 */
.data-info {
  margin: 20rpx;
  padding: 24rpx;
  background: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #2196F3;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
  min-width: 120rpx;
}

.info-value {
  font-size: 26rpx;
  color: #2196F3;
  font-weight: 500;
}

/* 一键复制按钮 */
.copy-section {
  margin: 12rpx 20rpx;
  text-align: center;
}

.copy-btn {
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
  font-weight: 400;
  box-shadow: 0 2rpx 6rpx rgba(82, 196, 26, 0.2);
  transition: all 0.3s ease;
  display: inline-block;
}

.copy-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 2rpx rgba(82, 196, 26, 0.2);
}

/* 年份选择器（历史记录用） - 简约设计 */
.year-selector {
  background: white;
  margin-bottom: 20rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
}

.year-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 400;
}

.year-picker {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #2196F3;
  font-weight: 400;
  border: 1rpx solid #e9ecef;
}

.picker-arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 80rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 查看全部按钮 */
.view-all-section {
  margin: 20rpx;
  text-align: center;
}

.view-all-btn {
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  font-weight: 400;
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.2);
  transition: all 0.3s ease;
}

.view-all-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(33, 150, 243, 0.2);
}

.view-all-btn.secondary {
  background: #666;
  box-shadow: 0 2rpx 8rpx rgba(102, 102, 102, 0.2);
}

/* 津贴数据表格样式 - 简约设计 */
.jintie-table {
  margin: 20rpx;
  background: white;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  color: #333;
  border-bottom: 1rpx solid #e9ecef;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:nth-child(even) {
  background: #fafafa;
}

.table-row:active {
  background: #f0f8ff;
}

.table-cell {
  flex: 1;
  padding: 20rpx 12rpx;
  text-align: center;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all;
  color: #333;
}

.table-cell:first-child {
  flex: 0.6;
}

.table-cell:nth-child(2) {
  flex: 1.2;
}

.table-cell:nth-child(3) {
  flex: 1.2;
}

.table-cell:last-child {
  flex: 1.5;
}

.header-cell {
  font-weight: 500;
  font-size: 26rpx;
}

.table-cell.amount {
  color: #2196F3;
  font-weight: 500;
}

.table-cell.remark {
  color: #666;
  font-size: 24rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-cell.remark .remark-view-btn {
  font-size: 24rpx;
  color: #2196F3;
  text-decoration: underline;
  cursor: pointer;
}

/* 姓名点击样式 */
.table-cell.name-cell {
  color: #007aff;
  cursor: pointer;
  text-decoration: underline;
}

.table-cell.name-cell:hover {
  background-color: #f0f8ff;
}

/* 个人数据模态框样式 */
.personal-data-modal {
  max-width: 90%;
  max-height: 80%;
  width: 600rpx;
}

.personal-data-modal .modal-header {
  background: #2196F3;
  color: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.personal-data-modal .modal-title {
  color: #fff;
  font-weight: 600;
}

.personal-data-modal .modal-close {
  color: #fff;
  font-size: 32rpx;
}

.personal-data-list {
  max-height: 500rpx;
  overflow-y: auto;
}

.personal-data-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.personal-data-item:last-child {
  border-bottom: none;
}

.data-date {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.data-record-date {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.data-amount {
  font-size: 30rpx;
  color: #007aff;
  font-weight: 600;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.data-remark {
  font-size: 24rpx;
  color: #999;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 备注查看按钮样式 */
.remark-view-btn {
  font-size: 24rpx;
  color: #2196F3;
  text-decoration: underline;
  cursor: pointer;
}

/* 表头样式 */
.personal-data-header {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
  font-weight: 600;
}

.data-header-cell {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 合计行样式 */
.personal-data-total {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  background: #f0f8ff;
  border-top: 2rpx solid #2196F3;
  font-weight: 600;
  margin-top: 10rpx;
}

.data-total-cell {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.data-total-cell:first-child {
  color: #2196F3;
}

.data-total-amount {
  font-size: 32rpx;
  color: #2196F3;
  font-weight: 700;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 表头样式 */
.personal-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
  font-weight: 600;
}

.data-header-cell {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: center;
}

.data-header-cell:first-child {
  text-align: left;
}

.data-header-cell:last-child {
  text-align: right;
}

/* 合计行样式 */
.personal-data-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  background: #f0f8ff;
  border-top: 2rpx solid #2196F3;
  font-weight: 600;
  margin-top: 10rpx;
}

.data-total-cell {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: center;
}

.data-total-cell:first-child {
  text-align: left;
  color: #2196F3;
}

.data-total-cell:last-child {
  text-align: right;
}

.data-total-amount {
  font-size: 32rpx;
  color: #2196F3;
  font-weight: 700;
  flex: 1;
  text-align: center;
}

/* 历史记录样式 - 简约设计 */
.history-list {
  margin-bottom: 20rpx;
}

.history-item {
  background: white;
  margin-bottom: 12rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1rpx solid #f0f0f0;
}

.history-item:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.history-date {
  margin-right: 30rpx;
}

.date {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.month {
  font-size: 24rpx;
  color: #999;
}

.history-info {
  flex: 1;
  text-align: right;
}

.record-count {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.total-amount {
  font-size: 28rpx;
  color: #667eea;
  font-weight: bold;
}

.history-arrow {
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #ccc;
}

/* 无数据样式 - 简约设计 */
.no-data {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  margin: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.no-data-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
  opacity: 0.4;
}

.no-data-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.no-data-tip {
  display: block;
  font-size: 22rpx;
  color: #999;
}

/* 修改密码模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
  box-sizing: border-box;
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

.modal-body {
  padding: 40rpx;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.modal-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.modal-input:focus {
  border-color: #667eea;
  outline: none;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 32rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #f8f9fa;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.modal-btn.cancel {
  background: #e9ecef;
  color: #666;
}

.modal-btn.cancel:active {
  background: #dee2e6;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn.confirm:active {
  transform: translateY(2rpx);
}

.modal-btn[disabled] {
  opacity: 0.6;
  background: #ccc !important;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header {
    padding: 30rpx 20rpx;
  }

  .header-actions {
    gap: 12rpx;
  }

  .action-btn {
    padding: 8rpx 12rpx;
    min-width: 60rpx;
  }

  .action-icon {
    font-size: 24rpx;
  }

  .action-text {
    font-size: 18rpx;
  }

  .nav-section {
    margin: 15rpx;
  }

  .content {
    padding: 0 15rpx;
  }

  .jintie-table {
    margin: 15rpx;
  }

  .table-cell {
    padding: 16rpx 8rpx;
    font-size: 24rpx;
  }

  .header-cell {
    font-size: 26rpx;
  }

  .modal-overlay {
    padding: 20rpx;
  }

  .modal-header, .modal-footer {
    padding: 24rpx 30rpx;
  }

  .modal-body {
    padding: 30rpx;
  }
}
