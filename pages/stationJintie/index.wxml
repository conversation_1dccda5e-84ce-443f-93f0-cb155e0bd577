<!--一站津贴查询主页面-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading && !isLoggedIn}}">
    <view class="loading-content">
      <text class="loading-text">正在检查登录状态...</text>
    </view>
  </view>

  <!-- 未登录提示界面 -->
  <view class="login-container" wx:if="{{!isLoggedIn && !isLoading && !isGuestMode}}">
    <view class="login-box">
      <view class="login-title">一站津贴查询</view>
      <view class="login-notice">
        <view class="notice-icon">🔐</view>
        <view class="notice-title">需要登录</view>
        <view class="notice-desc">请先在"我的"页面进行微信登录并绑定系统账号</view>
        <button class="login-btn" bindtap="goToLogin">去登录</button>
      </view>
    </view>
  </view>

  <!-- 游客模式提示界面 -->
  <view class="login-container" wx:if="{{!isLoggedIn && !isLoading && isGuestMode}}">
    <view class="login-box">
      <view class="login-title">一站津贴查询</view>
      <view class="login-notice">
        <view class="notice-icon">👤</view>
        <view class="notice-title">需要绑定账号</view>
        <view class="notice-desc">您当前是游客模式，需要绑定系统账号才能使用津贴查询功能</view>
        <button class="login-btn" bindtap="showBindTip">去绑定</button>
      </view>
    </view>
  </view>

  <!-- 权限不足提示界面 -->
  <view class="login-container" wx:if="{{isLoggedIn && !isLoading && !hasPermission}}">
    <view class="login-box">
      <view class="login-title">一站津贴查询</view>
      <view class="login-notice">
        <view class="notice-icon">⚠️</view>
        <view class="notice-title">权限不足</view>
        <view class="notice-desc">抱歉，只有一站人员和管理员才能查看津贴信息</view>
        <view class="notice-desc">您当前的角色是：{{roleText}}</view>
        <button class="login-btn" bindtap="handleLogout">返回我的页面</button>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view wx:if="{{isLoggedIn && !isLoading && hasPermission}}">
    <!-- 头部用户信息 -->
    <view class="header">
      <view class="user-info">
        <view class="user-details">
          <view class="username-container">
            <text class="username-label">当前用户</text>
            <text class="username">{{username}}</text>
          </view>
          <view class="role-container">
            <text class="role-badge">{{roleText}}</text>
          </view>
        </view>
      </view>
      <view class="header-actions">
        <view class="action-btn" bindtap="handleLogout">
          <text class="action-icon">🚪</text>
          <text class="action-text">我的页面</text>
        </view>
      </view>
    </view>

  <!-- 功能导航 -->
  <view class="nav-section">
    <view class="nav-item {{currentTab === 'current' ? 'active' : ''}}" bindtap="switchTab" data-tab="current">
      <text class="nav-text">当前津贴</text>
    </view>
    <view class="nav-item {{currentTab === 'history' ? 'active' : ''}}" bindtap="switchTab" data-tab="history">
      <text class="nav-text">历史记录</text>
    </view>
  </view>

  <!-- 当前津贴页面 -->
  <view class="content" wx:if="{{currentTab === 'current'}}">
    <!-- 数据信息显示 -->
    <view class="data-info" wx:if="{{currentDataInfo.record_date}}">
      <view class="info-item">
        <text class="info-label">数据月度：</text>
        <text class="info-value">{{currentDataInfo.month_year}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">记录时间：</text>
        <text class="info-value">{{currentDataInfo.record_date}}</text>
      </view>
    </view>

    <!-- 一键复制按钮 -->
    <view class="copy-section" wx:if="{{jintieRecords.length > 0}}">
      <button class="copy-btn" bindtap="copyJintieData">一键复制</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 津贴数据表格 -->
    <view class="jintie-table" wx:if="{{jintieRecords.length > 0}}">
      <view class="table-header">
        <view class="table-cell header-cell">序号</view>
        <view class="table-cell header-cell">姓名</view>
        <view class="table-cell header-cell">总金额</view>
        <view class="table-cell header-cell">备注</view>
      </view>
      <view class="table-row" wx:for="{{jintieRecords}}" wx:key="id">
        <view class="table-cell">{{index + 1}}</view>
        <view class="table-cell name-cell" bindtap="onNameClick" data-name="{{item.name}}">{{item.name}}</view>
        <view class="table-cell amount">¥{{item.total_amount}}</view>
        <view class="table-cell remark">
          <text wx:if="{{item.remark && item.remark !== '-'}}" class="remark-view-btn" bindtap="viewRemark" data-remark="{{item.remark}}">查看</text>
          <text wx:else>-</text>
        </view>
      </view>
    </view>

    <!-- 个人用户查看全部/收起按钮 -->
    <view class="view-all-section" wx:if="{{role !== 'admin' && role !== 'manager'}}">
      <button class="view-all-btn" wx:if="{{!showAllUsers}}" bindtap="toggleViewAll">查看全部人员</button>
      <button class="view-all-btn secondary" wx:if="{{showAllUsers}}" bindtap="toggleViewAll">只看自己</button>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data" wx:if="{{jintieRecords.length === 0 && !isLoading}}">
      <text class="no-data-icon">📋</text>
      <text class="no-data-text">暂无津贴数据</text>
      <text class="no-data-tip">请查看历史记录或联系管理员</text>
    </view>
  </view>

  <!-- 历史记录页面 -->
  <view class="content" wx:if="{{currentTab === 'history'}}">
    <!-- 年份选择 -->
    <view class="year-selector">
      <view class="year-label">查询年份：</view>
      <picker mode="selector" range="{{yearRange}}" value="{{selectedYearIndex}}" bindchange="onYearChange">
        <view class="year-picker">
          <text>{{selectedYear}}年</text>
          <text class="picker-arrow">📅</text>
        </view>
      </picker>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isHistoryLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-list" wx:elif="{{historyRecords.length > 0}}">
      <view class="history-item" wx:for="{{historyRecords}}" wx:key="date" bindtap="viewHistoryDetail" data-date="{{item.date}}">
        <view class="history-date">
          <text class="date">{{item.date}}</text>
          <text class="month">{{item.month_year}}</text>
        </view>
        <view class="history-info">
          <view class="record-count">查看详情</view>
          <view class="total-amount">{{item.month_year}}</view>
        </view>
        <view class="history-arrow">›</view>
      </view>
    </view>

    <!-- 无历史数据 -->
    <view class="no-data" wx:else>
      <text class="no-data-icon">📊</text>
      <text class="no-data-text">暂无{{selectedYear}}年的历史记录</text>
      <text class="no-data-tip">请选择其他年份查询</text>
    </view>
  </view>

  <!-- 修改密码功能已移至"我的"页面 -->

  <!-- 个人年度数据模态框 -->
  <view class="modal-overlay" wx:if="{{showPersonalDataModal}}" bindtap="hidePersonalDataModal">
    <view class="modal-content personal-data-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{selectedPersonName}} - {{currentYear}}年度津贴记录</text>
        <view class="modal-close" bindtap="hidePersonalDataModal">✕</view>
      </view>
      <view class="modal-body">
        <!-- 加载状态 -->
        <view class="loading" wx:if="{{isLoadingPersonalData}}">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 个人数据列表 -->
        <view class="personal-data-list" wx:elif="{{personalDataList.length > 0}}">
          <!-- 表头 -->
          <view class="personal-data-header">
            <view class="data-header-cell">月度</view>
            <view class="data-header-cell">日期</view>
            <view class="data-header-cell">金额</view>
            <view class="data-header-cell">备注</view>
          </view>
          <!-- 数据行 -->
          <view class="personal-data-item" wx:for="{{personalDataList}}" wx:key="record_date">
            <view class="data-date">{{item.month_year}}</view>
            <view class="data-record-date">{{item.record_date}}</view>
            <view class="data-amount">¥{{item.amount}}</view>
            <view class="data-remark">
              <text wx:if="{{item.remark && item.remark !== '-'}}" class="remark-view-btn" bindtap="viewRemark" data-remark="{{item.remark}}">查看</text>
              <text wx:else>-</text>
            </view>
          </view>
          <!-- 合计行 -->
          <view class="personal-data-total">
            <view class="data-total-cell">合计</view>
            <view class="data-total-cell">{{personalDataList.length}}条记录</view>
            <view class="data-total-amount">¥{{personalDataTotal}}</view>
            <view class="data-total-cell">-</view>
          </view>
        </view>

        <!-- 无数据提示 -->
        <view class="no-data" wx:else>
          <text class="no-data-icon">📊</text>
          <text class="no-data-text">暂无{{currentYear}}年度记录</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域结束 -->
  </view>
</view>
