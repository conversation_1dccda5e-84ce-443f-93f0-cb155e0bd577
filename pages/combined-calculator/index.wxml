<view class="container">
  <!-- 海费计算模块 -->
  <view class="module-box sea-calculator">
    <view class="calculator-header">
      <view class="calculator-title">
        <text class="title-icon">🌊</text>
        <text class="title-text">海费计算器</text>
      </view>
    </view>

    <view class="input-section">
      <view class="input-row">
        <view class="input-group">
          <text class="input-label">出海天数：</text>
          <input type="number" value="{{seaDays}}" bindinput="onSeaDaysChange" placeholder="请输入天数" class="input-field"/>
        </view>
        <view class="input-group">
          <text class="input-label">住台天数：</text>
          <input type="number" value="{{stayDays}}" bindinput="onStayDaysChange" placeholder="请输入天数" class="input-field"/>
        </view>
      </view>
    </view>

    <view class="result-section">
      <view class="result-row">
        <view class="result-item">
          <text class="result-label">合计海费</text>
          <text class="result-value">{{seaTotal}}元</text>
        </view>
      </view>
      <view class="result-row">
        <view class="result-item">
          <text class="result-label">艰苦边远地区补贴</text>
          <text class="result-value">{{subsidyTotal}}元</text>
        </view>
      </view>
    </view>

    <view class="detail-toggle" bindtap="toggleSeaDetail">
      <text class="toggle-text">{{showSeaDetail ? '收起详情' : '展开详情'}}</text>
      <text class="toggle-icon">{{showSeaDetail ? '▲' : '▼'}}</text>
    </view>

    <view class="formula-section" wx:if="{{showSeaDetail}}">
      <view class="formula-header">
        <text class="formula-title">💡 计算公式明细</text>
      </view>
      <view class="formula-content">
        <view class="formula-item">
          <text class="formula-label">出海费用</text>
          <text class="formula-value">55元 × {{seaDays}}天 = {{55 * seaDays}}元</text>
        </view>
        <view class="formula-item">
          <text class="formula-label">住台费用</text>
          <text class="formula-value">75元 × {{stayDays}}天 = {{75 * stayDays}}元</text>
        </view>
        <view class="formula-item">
          <text class="formula-label">出海补贴</text>
          <text class="formula-value">35元 × {{seaDays}}天 = {{35 * seaDays}}元</text>
        </view>
        <view class="formula-item">
          <text class="formula-label">住台补贴</text>
          <text class="formula-value">45元 × {{stayDays}}天 = {{45 * stayDays}}元</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 夜班费计算模块 -->
  <view class="module-box night-calculator">
    <view class="calculator-header">
      <view class="calculator-title">
        <text class="title-icon">🌙</text>
        <text class="title-text">夜班费计算器</text>
      </view>
    </view>

    <view class="input-section">
      <view class="input-row">
        <view class="input-group">
          <text class="input-label">小夜班次：</text>
          <input type="number" value="{{smallNight}}" bindinput="onSmallNightChange" placeholder="请输入班次" class="input-field"/>
        </view>
        <view class="input-group">
          <text class="input-label">大夜班次：</text>
          <input type="number" value="{{bigNight}}" bindinput="onBigNightChange" placeholder="请输入班次" class="input-field"/>
        </view>
      </view>
    </view>

    <!-- 说明信息 -->
    <view class="info-section">
      <view class="info-item">
        <text class="info-icon">ℹ️</text>
        <text class="info-text">一个完整的夜班为一个小夜+一个大夜</text>
      </view>
    </view>

    <view class="result-section">
      <view class="result-row">
        <view class="result-item">
          <text class="result-label">合计夜班费</text>
          <text class="result-value">{{nightTotal}}元</text>
        </view>
      </view>
    </view>

    <view class="detail-toggle" bindtap="toggleNightDetail">
      <text class="toggle-text">{{showNightDetail ? '收起详情' : '展开详情'}}</text>
      <text class="toggle-icon">{{showNightDetail ? '▲' : '▼'}}</text>
    </view>

    <view class="formula-section" wx:if="{{showNightDetail}}">
      <view class="formula-header">
        <text class="formula-title">💡 计算公式明细</text>
      </view>
      <view class="formula-content">
        <view class="formula-item">
          <text class="formula-label">小夜班费</text>
          <text class="formula-value">20元 × {{smallNight}}班 = {{20 * smallNight}}元</text>
        </view>
        <view class="formula-item">
          <text class="formula-label">大夜班费</text>
          <text class="formula-value">30元 × {{bigNight}}班 = {{30 * bigNight}}元</text>
        </view>
      </view>
    </view>
  </view>
</view>