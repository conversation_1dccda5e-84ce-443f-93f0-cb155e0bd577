Page({
  data: {
    seaDays: '',
    stayDays: '',
    showSeaDetail: false,
    seaTotal: 0,
    subsidyTotal: 0,
    
    smallNight: '',
    bigNight: '',
    showNightDetail: false,
    nightTotal: 0 
  },

  // 统一验证方法（修改点）
  validateInput(value, max = 999) {
    // 过滤非数字字符
    let filtered = value.replace(/[^0-9]/g, '')
    
    // 处理空值时直接返回空字符串（核心修改）
    if (filtered === '') return ''
    
    // 修正前导零
    filtered = filtered.replace(/^0+/, '') || '0'
    
    // 超限修正
    const numValue = parseInt(filtered)
    if (numValue > max) {
      wx.showToast({ 
        title: `最大值${max}`, 
        icon: 'none', 
        duration: 1500 
      })
      return String(max)
    }
    return String(numValue)
  },

  // 统一输入处理器
  handleInput(field, value, max) {
    const validated = this.validateInput(value, max)
    this.setData({ 
      [field]: validated 
    }, this.calculateTotals)
  },

  // 输入事件处理
  onSeaDaysChange(e) {
    this.handleInput('seaDays', e.detail.value, 999)
  },
  onStayDaysChange(e) {
    this.handleInput('stayDays', e.detail.value, 999)
  },
  onSmallNightChange(e) {
    this.handleInput('smallNight', e.detail.value, 999)
  },
  onBigNightChange(e) {
    this.handleInput('bigNight', e.detail.value, 999)
  },

  // 计算总和
  calculateTotals() {
    const getNum = (val) => Math.min(Number(val || 0), 999)
    
    this.setData({
      seaTotal: 55 * getNum(this.data.seaDays) + 75 * getNum(this.data.stayDays),
      subsidyTotal: 35 * getNum(this.data.seaDays) + 45 * getNum(this.data.stayDays),
      nightTotal: 20 * getNum(this.data.smallNight) + 30 * getNum(this.data.bigNight)
    })
  },

  // 详情切换
  toggleSeaDetail() {
    this.setData({ showSeaDetail: !this.data.showSeaDetail })
  },
  toggleNightDetail() {
    this.setData({ showNightDetail: !this.data.showNightDetail })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-海费与夜班计算器',
      path: '/pages/combined-calculator/index'
    };
  },

  onShareTimeline() {
    return {
      title: '平台常用计算工具-海费与夜班计算器',
      query: 'from=timeline'
    };
  }
})