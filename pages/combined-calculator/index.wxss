/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20rpx 0 80rpx 0;
}

/* 模块卡片样式 */
.module-box {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  margin: 16rpx 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 计算器图标 */
.sea-calculator::before {
  content: '🌊';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

.night-calculator::before {
  content: '🌙';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}
/* 计算器头部 */
.calculator-header {
  padding: 28rpx 24rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
}

.calculator-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.calculator-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.title-icon {
  font-size: 32rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: 0.5rpx;
}

/* 海费计算器头部 - 蓝色主题 */
.sea-calculator .calculator-header {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.sea-calculator .calculator-header::after {
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.2) 50%, transparent 100%);
}

.sea-calculator .title-text {
  color: #1e40af;
  text-shadow: 0 1rpx 2rpx rgba(30, 64, 175, 0.1);
}

/* 夜班费计算器头部 - 紫色主题 */
.night-calculator .calculator-header {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.night-calculator .calculator-header::after {
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.2) 50%, transparent 100%);
}

.night-calculator .title-text {
  color: #6b21a8;
  text-shadow: 0 1rpx 2rpx rgba(107, 33, 168, 0.1);
}

/* 通用内容区域背景 */
.input-section,
.info-section,
.result-section {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

/* 输入区域 */
.input-section {
  padding: 20rpx 24rpx 8rpx;
}

.input-row {
  display: flex;
  gap: 16rpx;
}

.input-group {
  flex: 1;
}

.input-label {
  display: block;
  margin-bottom: 8rpx;
  color: #475569;
  font-size: 28rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-sizing: border-box;
  font-size: 30rpx;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
}

.input-field:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.08);
}

/* 信息说明区域 */
.info-section {
  padding: 8rpx 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.info-icon {
  font-size: 18rpx;
  opacity: 0.7;
}

.info-text {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

/* 结果区域 */
.result-section {
  padding: 8rpx 24rpx 20rpx;
}

.result-row {
  margin-bottom: 12rpx;
}

.result-row:last-child {
  margin-bottom: 0;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.result-label {
  color: #64748b;
  font-size: 26rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.result-value {
  color: #1e293b;
  font-size: 30rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

/* 海费计算器结果区域 */
.sea-calculator .result-item {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1rpx solid #bae6fd;
}

/* 夜班费计算器结果区域 */
.night-calculator .result-item {
  background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
  border: 1rpx solid #c4b5fd;
}

/* 详情切换按钮 */
.detail-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.toggle-text {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 500;
}

.toggle-icon {
  font-size: 24rpx;
  color: #3b82f6;
  transition: transform 0.3s ease;
}

/* 公式明细区域 */
.formula-section {
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.formula-header {
  padding: 16rpx 24rpx 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.formula-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

.formula-content {
  padding: 16rpx 24rpx 20rpx;
}

.formula-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 10rpx;
  border: 1rpx solid #e2e8f0;
}

.formula-item:last-child {
  margin-bottom: 0;
}

.formula-label {
  color: #64748b;
  font-size: 24rpx;
  font-weight: 500;
}

.formula-value {
  color: #1e293b;
  font-size: 26rpx;
  font-weight: 600;
}
