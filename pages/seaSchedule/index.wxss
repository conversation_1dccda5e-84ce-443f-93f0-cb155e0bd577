  /* pages/seaSchedule/index.wxss */

  /* 容器样式 */
  .container {
    background: #f5f5f5;
    min-height: 100vh;
  }

  /* 下次倒班时间横幅 */
  .next-shift-banner {
    background: linear-gradient(135deg, #007AFF, #5856D6);
    color: white;
    border-radius: 24rpx;
    padding: 40rpx;
    margin: 32rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.3);
  }

  .banner-title {
    font-size: 36rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .banner-icon {
    margin-right: 16rpx;
    font-size: 40rpx;
  }

  .banner-date {
    font-size: 60rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }

  .banner-countdown {
    font-size: 28rpx;
    opacity: 0.9;
    margin-bottom: 24rpx;
  }

  .banner-current-info {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16rpx;
    padding: 16rpx;
    font-size: 24rpx;
    opacity: 0.9;
  }

  /* 年份选择和视图切换卡片 */
  .schedule-card {
    background: white;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
    margin: 32rpx;
    overflow: hidden;
  }

  .card-content {
    padding: 32rpx;
  }

  .header-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .year-info-section {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }

  .compact-picker {
    background: white;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    padding: 12rpx 24rpx;
    font-size: 28rpx;
    color: #6c757d;
  }

  .station-info {
    font-size: 24rpx;
    color: #6c757d;
    display: flex;
    align-items: center;
  }

  .info-icon {
    margin-right: 8rpx;
    font-size: 28rpx;
  }

  /* 管理员面板 */
  .admin-bar {
    background: #fff3cd;
    border: 2rpx solid #ffeaa7;
    border-radius: 24rpx;
    padding: 32rpx;
    margin: 32rpx;
    display: flex;
    flex-direction: column;
  }

  /* 第一行：管理员信息 */
  .admin-info-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .admin-icon {
    margin-right: 16rpx;
    color: #f39c12;
    font-size: 32rpx;
  }

  /* 第二行：管理按钮 */
  .admin-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16rpx;
  }

  .username {
    font-size: 26rpx;
    color: #7f8c8d;
  }

  .mini-btn {
    font-size: 22rpx !important;
    padding: 12rpx 20rpx !important;
    line-height: 1.2 !important;
    border-radius: 12rpx !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 100rpx !important;
    height: 50rpx !important;
    border: none !important;
  }

  .add-btn {
    background: #007AFF !important;
    color: white !important;
  }

  .logout-btn {
    background: #6c757d !important;
    color: white !important;
  }

  .small-login-btn {
    width: auto !important;
    padding: 8rpx 30rpx !important;
    font-size: 26rpx !important;
    background: #3498db !important;
    color: white !important;
    border-radius: 30rpx !important;
    margin-left: auto;
  }

  /* 月份卡片样式 */
  .month-card {
    background: white;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
    margin: 32rpx;
    overflow: hidden;
  }

  .month-header {
    padding: 10rpx 32rpx 0 32rpx;
    border-bottom: 2rpx solid #f0f4f8;
    margin-bottom: 0;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16rpx 0;
    min-height: 60rpx;
  }

  .month-title {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .month-title text {
    font-size: 28rpx;
    color: #2c3e50;
    font-weight: 600;
    line-height: 1.4;
  }

  .month-icon {
    margin-right: 16rpx;
    color: #007AFF;
    font-size: 32rpx;
  }

  .remark-badge {
    background: #fff3cd;
    color: #856404;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    margin-left: auto;
    margin-right: 16rpx;
  }



/* 新增Flex布局样式 */
.info-line {
  display: flex;
  align-items: center;
  gap: 24rpx;
  width: 100%;
}

.station-tag {
  flex-shrink: 0;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: white;
  background: #007AFF;
}

.station-tag.station-two {
  background: #34C759;
}



  .record-item {
    position: relative;
    padding: 24rpx 32rpx;
    margin: 16rpx 0;
    background: #f8f9fa;
    border-left: 8rpx solid #007AFF;
    border-radius: 16rpx;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .record-item.station-two {
    border-left-color: #34C759;
    background: #f0f9f0;
  }

  .record-item.active-record {
    background: #e3f2fd !important;
    border-left-color: #2196F3 !important;
    box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.2);
  }

  /* 数据行样式 */
  .data-row {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 24rpx;
  }

  .schedule-items {
    padding: 0 32rpx 32rpx 32rpx;
  }

  .no-data {
    text-align: center;
    color: #95a5a6;
    font-size: 24rpx;
    padding: 60rpx 0;
    background: #f8f9fa;
    border-radius: 16rpx;
    margin: 16rpx 0;
  }

  .content-wrapper {
    flex: 1;
    margin-left: 0;
    padding: 0;
  }

  .schedule-details {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    flex: 1;
    min-width: 0;
  }

  .days-info {
    font-size: 24rpx;
    color: #7f8c8d;
  }

  .schedule-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .status-badge {
    background: #2196F3;
    color: white;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
    font-size: 20rpx;
    font-weight: 600;
    flex-shrink: 0;
  }





.date-range {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.days-info {
  font-size: 20rpx;
  color: #7f8c8d;
  margin-top: 4rpx;
}

  .action-btns {
    display: flex !important;
    justify-content: flex-end !important;
    gap: 20rpx !important;
    margin-top: 16rpx !important;
    padding: 0 !important;
    width: 100% !important;
  }

  .edit-btn, .delete-btn {
    min-width: 100rpx !important;
    max-width: 100rpx !important;
    height: 50rpx !important;
    border-radius: 12rpx !important;
    font-size: 22rpx !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
    padding: 0 !important;
  }

  .edit-btn {
    background: #007AFF !important;
    color: white !important;
  }

  .delete-btn {
    background: #FF3B30 !important;
    color: white !important;
  }

  /* 全年统计卡片 */
  .total-card {
    background: white;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
    padding: 32rpx;
    margin: 32rpx;
  }

  .total-title {
    font-size: 28rpx;
    color: #2c3e50;
    font-weight: 600;
    text-align: center;
    margin-bottom: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .total-icon {
    margin-right: 16rpx;
    font-size: 32rpx;
  }

  .total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    font-size: 26rpx;
    border-bottom: 2rpx solid #f8f9fa;
  }

  .total-item:last-child {
    border-bottom: none;
  }

  .total-label {
    display: flex;
    align-items: center;
  }

  .station-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }

  .first-station {
    color: #007AFF;
    font-weight: 500;
  }

  .first-station .station-dot {
    background: #007AFF;
  }

  .second-station {
    color: #34C759;
    font-weight: 500;
  }

  .second-station .station-dot {
    background: #34C759;
  }








  /* 月份管理按钮 */
  .month-admin-btns {
    display: flex !important;
    justify-content: center !important;
    padding: 0rpx 0 32rpx 0 !important;
    border-top: 2rpx solid #f0f4f8 !important;
    margin-top: 8rpx !important;
    width: 100% !important;
  }

  .month-remark-btn {
    min-width: 100rpx !important;
    max-width: 100rpx !important;
    height: 50rpx !important;
    background: #FF9500 !important;
    color: white !important;
    border-radius: 12rpx !important;
    font-size: 22rpx !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
    padding: 0 !important;
  }







.scroll-view {
  scroll-behavior: smooth;
  height: 70vh;
  max-height: calc(100vh - 350rpx);
}

/* 底部提示样式 */
.footer-notice {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  padding: 32rpx;
  margin: 32rpx;
  font-size: 24rpx;
  color: #f39c12;
  text-align: center;
  line-height: 1.6;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  border: 2rpx solid #fff3cd;
}

.notice-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.notice-text {
  color: #856404;
  text-align: left;
  line-height: 1.6;
  flex: 1;
}

/* 显示模式切换 */
.view-mode-switcher {
  display: flex;
  background: #f0f0f0;
  border-radius: 16rpx;
  padding: 8rpx;
}

.switch-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  background: transparent;
  color: #6c757d;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s;
  flex: 1;
  justify-content: center;
}

.switch-btn.active {
  background: #007AFF;
  color: white;
  border-color: #007AFF;
}

.mode-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* 日历模式样式 */
.calendar-outer {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 220rpx);
  overflow: hidden;
}

.calendar-content-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.calendar-scroll-view {
  flex: 1;
  padding: 15rpx;
  box-sizing: border-box;
  height: calc(100vh - 280rpx); /* 调整高度 */
  overflow-x: hidden;
}

.calendar-month-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  height: auto;
}

.calendar-month-header {
  padding: 25rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eef2f7;
  background-color: #f8fafc;
  z-index: 10;
}

.calendar-month-title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.calendar-month-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #34495e;
}

.calendar-remark-text {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-top: 6rpx;
}

.month-nav-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin: 0 10rpx;
  transition: all 0.2s ease;
}

.month-nav-button:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.nav-arrow {
  color: #3498db;
  font-size: 26rpx;
  font-weight: bold;
}

/* 日历表头样式 */
.calendar-header-row {
  display: flex;
  flex-direction: row;
  background-color: #f8fafc;
  border-bottom: 1px solid #eef2f7;
  width: 100%;
}

.calendar-weekday {
  width: 14.28%;
  font-size: 26rpx;
  color: #7f8c8d;
  font-weight: bold;
  padding: 15rpx 0;
  text-align: center;
}

.calendar-body {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.calendar-cell {
  width: 14.28%;
  height: 110rpx;
  padding: 12rpx 4rpx;
  box-sizing: border-box;
  border-right: 1px solid #f0f4f8;
  border-bottom: 1px solid #f0f4f8;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.calendar-cell:hover {
  background-color: #f5f9ff;
}

.calendar-date {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 8rpx;
  width: 52rpx;
  height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.today-date {
  background-color: #e74c3c;
  color: white;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 60, 0.3);
}

.other-month {
  background-color: #fafbfc;
}

.other-month .calendar-date {
  color: #bdc3c7;
}

.calendar-shift-tag {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  color: #fff;
  text-align: center;
  width: 85%;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.first-station-bg {
  background-color: #3498db;
  background-image: linear-gradient(135deg, #3498db, #2980b9);
}

.second-station-bg {
  background-color: #2ecc71;
  background-image: linear-gradient(135deg, #2ecc71, #27ae60);
}

.change-day-bg {
  background-color: #f39c12;
  background-image: linear-gradient(135deg, #f39c12, #e67e22);
  font-weight: bold;
}

/* 日历图例 */
.calendar-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 25rpx;
  margin: 25rpx 0 30rpx;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 15rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #34495e;
}

.other-month-shift {
  background-color: #bdc3c7;
  background-image: linear-gradient(135deg, #bdc3c7, #95a5a6);
  opacity: 0.8;
}

.other-month-change {
  background-color: #d35400;
  background-image: linear-gradient(135deg, #d35400, #bdc3c7);
  opacity: 0.8;
}

/* 优化非当前月的日期显示 */
.other-month {
  background-color: #fafbfc;
}

.other-month .calendar-date {
  color: #bdc3c7;
}