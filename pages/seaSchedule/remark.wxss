/* pages/seaSchedule/remark.wxss */
.container {
  padding: 40rpx 30rpx;
  min-height: 100vh;
  background: #f5f6fa;
}

.form-item {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
}

.label {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 20rpx;
}

textarea {
  width: 100%;
  height: 300rpx;
  padding: 20rpx;
  border: 2rpx solid #bdc3c7;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
}

button[form-type="submit"] {
  background: #27ae60 !important;
  width: 200rpx;
  margin: 40rpx auto 0;
  border-radius: 50rpx !important;
  font-size: 28rpx !important;
}