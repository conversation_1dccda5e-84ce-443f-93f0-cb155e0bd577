// pages/seaSchedule/remark.js
Page({
  data: {
    year: '',
    month: '',
    remarkContent: ''
  },

  onLoad(options) {
    this.setData({
      year: options.year,
      month: options.month
    });
    this.loadRemark();
  },

  // 加载现有备注
  loadRemark() {
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      data: {
        action: 'get_remarks',
        year: this.data.year
      },
      success: (res) => {
        if (res.data.status === 'success') {
          this.setData({
            remarkContent: res.data.remarks[this.data.month] || ''
          });
        }
      }
    });
  },

  submitForm(e) {
    const remark = e.detail.value.remark || '';
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      header: {
        'Authorization': wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      data: {
        action: 'update_remark',
        year: this.data.year,
        month: this.data.month,
        remark: remark
      },
      success: () => {
        wx.navigateBack();
      }
    });
  }
})