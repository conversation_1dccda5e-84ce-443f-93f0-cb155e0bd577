// pages/seaSchedule/index.js
const app = getApp()
  // Base64解码工具
  const decodeBase64 = (base64) => {
    try {
      const arrayBuffer = wx.base64ToArrayBuffer(base64);
      const uint8Array = new Uint8Array(arrayBuffer);
      return String.fromCharCode.apply(null, uint8Array);
    } catch (e) {
      console.error('Base64解码失败:', e);
      return '';
    }
};

Page({
    data: {
      year: new Date().getFullYear().toString(),
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      displayMonth: new Date().getMonth() + 1, // 新增：当前显示的月份
      schedules: {},
      calendarData: {}, // 日历数据
      viewMode: 'list', // 默认为列表模式
      total: { first: 0, second: 0 },
      isAdmin: false,
      hasPermission: false,
      username: '',
      role: '',
      roleText: '',
      isLoggedIn: false,
      isGuestMode: false,
      isEditMode: false, // 新增：是否处于编辑模式
      yearRange: [],
      tapCount: 0,
      nextShift: null,
      currentShift: null,
      touchStartX: 0, // 触摸开始位置
      touchEndX: 0,    // 触摸结束位置
      touchStartY: 0,  // 触摸开始Y位置
      touchEndY: 0,    // 触摸结束Y位置
      isSwiping: false, // 是否正在滑动
      scrollIntoView: '', // 用于控制滚动位置
      isChangeDayToday: false // 是否是倒班当日
    },
    
    // 处理触摸开始事件
    calendarTouchStart(e) {
      // 记录初始触摸点
      this.setData({
        touchStartX: e.touches[0].clientX,
        touchStartY: e.touches[0].clientY,
        touchEndX: e.touches[0].clientX,
        touchEndY: e.touches[0].clientY,
        isSwiping: false
      });
    },
    
    // 处理触摸移动事件
    calendarTouchMove(e) {
      // 更新当前触摸点
      this.setData({
        touchEndX: e.touches[0].clientX,
        touchEndY: e.touches[0].clientY
      });
      
      // 计算X和Y方向上的移动距离
      const moveX = Math.abs(this.data.touchEndX - this.data.touchStartX);
      const moveY = Math.abs(this.data.touchEndY - this.data.touchStartY);
      
      // 如果X方向移动距离大于Y方向，且超过30，则判定为横向滑动
      if (moveX > moveY && moveX > 30) {
        this.setData({
          isSwiping: true
        });
      }
    },
    
    // 处理触摸结束事件
    calendarTouchEnd(e) {
      // 确保使用touches或changedTouches获取最终触摸点
      if (e.changedTouches && e.changedTouches.length > 0) {
        this.setData({
          touchEndX: e.changedTouches[0].clientX,
          touchEndY: e.changedTouches[0].clientY
        });
      }
      
      const { touchStartX, touchEndX, isSwiping } = this.data;
      const moveDistance = touchEndX - touchStartX;
      
      // 如果正在进行横向滑动且移动距离足够
      if (isSwiping && Math.abs(moveDistance) > 50) {
        // 右滑切换到上一个月
        if (moveDistance > 0) {
          this.switchToPrevMonth();
        } 
        // 左滑切换到下一个月
        else {
          this.switchToNextMonth();
        }
      }
      
      // 重置滑动状态
      this.setData({
        isSwiping: false
      });
    },
    
    // 长按处理，阻止默认长按事件
    calendarLongTap() {
      return false;
    },
    
    // 切换到列表模式
    switchToListMode() {
      this.setData({ viewMode: 'list' });
      // 切换到列表模式时自动滚动到当前月份
      this.scrollToCurrentMonth();
    },
    
    // 切换到日历模式
    switchToCalendarMode() {
      this.setData({ 
        viewMode: 'calendar',
        displayMonth: this.data.currentMonth // 重置为当前月份
      });
      // 如果日历数据还没生成，生成日历数据
      if (Object.keys(this.data.calendarData).length === 0) {
        this.generateCalendarData();
      }
    },
    
    // 切换到上一个月
    switchToPrevMonth() {
      let { displayMonth, year } = this.data;
      displayMonth--;
      
      if (displayMonth < 1) {
        displayMonth = 12;
        year = (parseInt(year) - 1).toString();
        this.setData({ 
          year,
          displayMonth
        });
        this.loadData(); // 加载新的年份数据
      } else {
        this.setData({ displayMonth });
        
        // 如果日历数据已经生成，确保当前月的数据已经加载
        if (this.data.calendarData && Object.keys(this.data.calendarData).length > 0) {
          if (!this.data.calendarData[displayMonth]) {
            this.generateCalendarData();
          }
        }
      }
    },
    
    // 切换到下一个月
    switchToNextMonth() {
      let { displayMonth, year } = this.data;
      displayMonth++;
      
      if (displayMonth > 12) {
        displayMonth = 1;
        year = (parseInt(year) + 1).toString();
        this.setData({ 
          year,
          displayMonth
        });
        this.loadData(); // 加载新的年份数据
      } else {
        this.setData({ displayMonth });
        
        // 如果日历数据已经生成，确保当前月的数据已经加载
        if (this.data.calendarData && Object.keys(this.data.calendarData).length > 0) {
          if (!this.data.calendarData[displayMonth]) {
            this.generateCalendarData();
          }
        }
      }
    },
    
    // 生成日历数据
    generateCalendarData() {
      const { year, schedules } = this.data;
      const yearNum = parseInt(year);
      const calendarData = {};
      
      // 合并所有月份的班次数据，用于处理跨月情况
      const allSchedules = [];
      for (let month = 1; month <= 12; month++) {
        if (schedules[month] && schedules[month].length > 0) {
          allSchedules.push(...schedules[month]);
        }
      }
      
      // 为每个月生成日历数据
      for (let month = 1; month <= 12; month++) {
        calendarData[month] = this.getMonthCalendar(yearNum, month, allSchedules);
      }
      
      this.setData({ calendarData });
    },
    
    // 获取指定月份的日历数据
    getMonthCalendar(year, month, monthSchedules) {
      const firstDay = new Date(year, month - 1, 1);
      const lastDay = new Date(year, month, 0);
      const daysInMonth = lastDay.getDate();
      
      // 获取这个月第一天是星期几(0是星期日)
      const firstDayOfWeek = firstDay.getDay();
      
      // 获取上个月的最后几天
      const prevMonthLastDate = new Date(year, month - 1, 0).getDate();
      const prevMonthDays = [];
      for (let i = 0; i < firstDayOfWeek; i++) {
        const day = prevMonthLastDate - firstDayOfWeek + i + 1;
        // 计算上个月的日期
        const prevMonthDate = new Date(year, month - 2, day);
        const dateString = this.formatDate(prevMonthDate);
        const shiftData = this.getShiftForDate(dateString, monthSchedules);
        
        prevMonthDays.push({
          day: day,
          currentMonth: false,
          date: prevMonthDate,
          shift: shiftData.shift,
          isChangeDay: shiftData.isChangeDay,
          id: `prev-${month}-${day}` // 添加唯一ID
        });
      }
      
      // 获取当前月的所有天
      const currentMonthDays = [];
      const today = new Date();
      const isCurrentYearMonth = year === today.getFullYear() && month === today.getMonth() + 1;
      
      for (let i = 1; i <= daysInMonth; i++) {
        const currentDate = new Date(year, month - 1, i);
        const dateString = this.formatDate(currentDate);
        const shiftData = this.getShiftForDate(dateString, monthSchedules);
        
        // 检查是否是今天
        const isToday = isCurrentYearMonth && i === today.getDate();
        
        currentMonthDays.push({
          day: i,
          currentMonth: true,
          date: currentDate,
          shift: shiftData.shift,
          isChangeDay: shiftData.isChangeDay,
          isToday: isToday,
          id: `current-${month}-${i}` // 添加唯一ID
        });
      }
      
      // 计算需要补充的下个月的天数，只补充到当前行结束
      const totalDaysDisplayed = prevMonthDays.length + currentMonthDays.length;
      const remainingDays = 7 - (totalDaysDisplayed % 7 || 7); // 计算需要补充的天数，如果刚好是7的倍数就不补充
      
      const nextMonthDays = [];
      if (remainingDays < 7) { // 只有当需要补充的天数小于7时才补充
        for (let i = 1; i <= remainingDays; i++) {
          // 计算下个月的日期
          const nextMonthDate = new Date(year, month, i);
          const dateString = this.formatDate(nextMonthDate);
          const shiftData = this.getShiftForDate(dateString, monthSchedules);
          
          nextMonthDays.push({
            day: i,
            currentMonth: false,
            date: nextMonthDate,
            shift: shiftData.shift,
            isChangeDay: shiftData.isChangeDay,
            id: `next-${month}-${i}` // 添加唯一ID
          });
        }
      }
      
      return {
        year,
        month,
        days: [...prevMonthDays, ...currentMonthDays, ...nextMonthDays]
      };
    },
    
    // 获取指定日期的班次信息
    getShiftForDate(dateString, schedules) {
      let result = { shift: null, isChangeDay: false };
      
      // 将日期字符串转为日期对象以便比较
      const targetDate = this.parseDate(dateString);
      if (!targetDate) return result;
      
      for (const schedule of schedules) {
        const startDate = this.parseDate(schedule.start_date);
        const endDate = this.parseDate(schedule.end_date);
        
        if (!startDate || !endDate) continue;
        
        // 日期转字符串，用于精确比较是否是同一天
        const targetDateStr = this.formatDate(targetDate);
        const startDateStr = this.formatDate(startDate);
        const endDateStr = this.formatDate(endDate);
        
        // 如果当前日期是起始日期，这是新班次的开始日(换班日)
        if (targetDateStr === startDateStr) {
          result = { 
            shift: schedule,  // 显示新开始的班次
            isChangeDay: startDateStr !== endDateStr  // 只有不是同一天结束才是换班日
          };
          break;
        }
        // 如果当前日期是结束日期，这是前一个班次的结束日(换班日)
        else if (targetDateStr === endDateStr) {
          result = { 
            shift: schedule,  // 显示即将结束的班次
            isChangeDay: startDateStr !== endDateStr  // 只有不是同一天开始才是换班日
          };
          break;
        }
        // 如果当前日期在班次期间内
        else if (targetDate > startDate && targetDate < endDate) {
          result = { 
            shift: schedule,
            isChangeDay: false
          };
          break;
        }
      }
      
      return result;
    },
    
    // 解析日期字符串为日期对象
    parseDate(dateStr) {
      if (!dateStr) return null;
      
      // 支持 "YYYY-MM-DD" 格式
      const parts = dateStr.split('-');
      if (parts.length !== 3) return null;
      
      const year = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // 月份从0开始
      const day = parseInt(parts[2], 10);
      
      return new Date(year, month, day);
    },
    
    // 新增标题点击处理方法
    handleTitleClick() {
      let { tapCount } = this.data;
      tapCount++;
      if (tapCount >= 5) {
        // 使用统一的微信登录状态检查
        this.checkWechatLoginAndNavigate();
        tapCount = 0;
      }

      this.setData({ tapCount });

      // 2秒后重置计数器
      setTimeout(() => {
        this.setData({ tapCount: 0 });
      }, 2000);
    },

    // 检查微信登录状态并导航到管理页面
    checkWechatLoginAndNavigate() {
      const wechatToken = wx.getStorageSync('wechat_token');
      const userInfo = wx.getStorageSync('user_info');
      const isGuestMode = wx.getStorageSync('guest_mode');

      if (wechatToken && userInfo && !isGuestMode) {
        // 已登录并绑定系统账号，检查权限
        const role = userInfo.role;
        const isAdmin = role === 'admin' || role === 'manager';

        if (isAdmin) {
          // 管理员权限，进入管理页面
          wx.navigateTo({
            url: '/pages/seaSchedule/admin',
            fail: (err) => {
              console.error('Navigation to admin failed:', err);
            }
          });
        } else {
          // 非管理员，显示权限提示
          wx.showToast({
            title: '只有管理员可管理',
            icon: 'none',
            duration: 2000
          });
        }
      } else if (isGuestMode) {
        // 游客模式，需要绑定系统账号
        wx.showModal({
          title: '需要绑定账号',
          content: '请先绑定系统账号后再使用管理功能',
          confirmText: '去绑定',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 跳转到个人资料页面进行绑定
              wx.switchTab({
                url: '/pages/profile/index',
                fail: (err) => {
                  console.error('Guest navigation failed:', err);
                  wx.showToast({
                    title: '跳转失败: ' + err.errMsg,
                    icon: 'none',
                    duration: 3000
                  });
                }
              });
            }
          }
        });
      } else {
        // 未登录，跳转到登录页面
        wx.showModal({
          title: '需要登录',
          content: '请先登录后再使用管理功能',
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({
                url: '/pages/profile/index',
                fail: (err) => {
                  console.error('Navigation failed:', err);
                  wx.showToast({
                    title: '跳转失败',
                    icon: 'none'
                  });
                }
              });
            }
          }
        });
      }
    },
    
    // 数据处理（添加排序）
    processData(rawData = []) {
      const schedules = {};
      const total = { first: 0, second: 0 };
      const currentDate = new Date(); // 获取当前日期
      const currentHour = currentDate.getHours(); // 获取当前小时
      const today = currentDate.toISOString().split('T')[0]; // 获取当前日期的字符串表示

      // 初始化所有月份的空数组
      for (let month = 1; month <= 12; month++) {
        schedules[month] = [];
      }

      // 用于存储当前活动班次
      let activeShift = null;

      if (rawData && rawData.length > 0) {
        rawData.sort((a, b) => new Date(a.start_date) - new Date(b.start_date));

        rawData.forEach(item => {
          const month = item.month;
          if (month >= 1 && month <= 12) {
            // 进行中状态判断
            const startDate = new Date(item.start_date);
            const endDate = new Date(item.end_date);
            let isActive = false;
            let status = '';

            // 在同一天交接的班次处理
            if (item.end_date === today && item.start_date !== today) {
              // 当天结束的班次，12点前标记为活动
              isActive = currentDate >= startDate && currentDate <= endDate && currentHour < 12;
            } else if (item.start_date === today && item.end_date !== today) {
              // 当天开始的班次，12点后标记为活动
              isActive = currentDate >= startDate && currentDate <= endDate && currentHour >= 12;
            } else {
              // 其他情况正常判断
              isActive = currentDate >= startDate && currentDate <= endDate;
            }

            // 确定状态
            if (isActive) {
              status = '进行中';
              activeShift = item;
            } else if (item.end_date === today && item.start_date !== today && currentHour >= 12) {
              // 当天结束的班次，12点后标记为已完成
              status = '已完成';
            } else if (item.start_date === today && item.end_date !== today && currentHour < 12) {
              // 当天开始的班次，12点前标记为计划中
              status = '计划中';
            } else if (endDate < currentDate) {
              status = '已完成';
            } else if (startDate > currentDate) {
              // 判断是即将开始还是计划中
              const daysToStart = Math.ceil((startDate - currentDate) / (1000 * 60 * 60 * 24));
              if (daysToStart <= 7) {
                status = '即将开始';
              } else {
                status = '计划中';
              }
            }

            // 标记活动状态和状态
            item.isActive = isActive;
            item.status = status;

            schedules[month].push(item);
            // 确保使用数字运算
            const days = parseInt(item.days);
            item.station === '一站' ?
              total.first += days :
              total.second += days;
          }
        });
      }

      this.setData({
        schedules,
        total
      });
    },

    // 计算下次倒班时间（基于当前年份数据）
    calculateNextShift(allData = []) {
      const currentDate = new Date();
      const currentHour = currentDate.getHours();
      const today = currentDate.toISOString().split('T')[0];

      let nextShift = null;
      let currentShift = null;
      let isChangeDayToday = false; // 是否是倒班当日

      if (allData && allData.length > 0) {
        // 按开始日期排序
        allData.sort((a, b) => new Date(a.start_date) - new Date(b.start_date));

        // 首先检查今天是否是倒班日
        allData.forEach(item => {
          // 检查今天是否是班次开始日（倒班日）
          if (item.start_date === today && item.start_date !== item.end_date) {
            isChangeDayToday = true;
          }
          // 检查今天是否是班次结束日（也是倒班日）
          if (item.end_date === today && item.start_date !== item.end_date) {
            isChangeDayToday = true;
          }
        });

        allData.forEach(item => {
          const startDate = new Date(item.start_date);
          const endDate = new Date(item.end_date);
          let isActive = false;

          // 判断是否为当前活动班次
          if (item.end_date === today && item.start_date !== today) {
            // 今天是结束日，12点前显示这个班次
            isActive = currentDate >= startDate && currentDate <= endDate && currentHour < 12;
          } else if (item.start_date === today && item.end_date !== today) {
            // 今天是开始日，12点后显示这个班次
            isActive = currentDate >= startDate && currentDate <= endDate && currentHour >= 12;
          } else {
            // 普通情况
            isActive = currentDate >= startDate && currentDate <= endDate;
          }

          if (isActive) {
            // 计算当前班次剩余天数
            const remainingDays = Math.ceil((endDate - currentDate) / (1000 * 60 * 60 * 24));

            // 在倒班当日，顶部横幅显示特殊信息，但当前班次显示实际班次信息
            if (isChangeDayToday) {
              currentShift = {
                station: '倒班', // 顶部横幅用
                remainingDays: 0, // 顶部横幅用
                actualStation: item.station, // 实际班次信息
                actualRemainingDays: remainingDays > 0 ? remainingDays : 0 // 实际剩余天数
              };
            } else {
              currentShift = {
                station: item.station,
                remainingDays: remainingDays > 0 ? remainingDays : 0
              };
            }
          } else if (startDate > currentDate) {
            // 找到下次倒班时间（最近的未来班次）
            if (!nextShift || startDate < new Date(nextShift.start_date)) {
              const daysToNext = Math.ceil((startDate - currentDate) / (1000 * 60 * 60 * 24));
              nextShift = {
                ...item,
                daysToNext: isChangeDayToday ? 0 : (daysToNext > 0 ? daysToNext : 0),
                isChangeDayToday: isChangeDayToday
              };
            }
          }
        });
      }

      this.setData({
        nextShift: nextShift,
        currentShift: currentShift,
        isChangeDayToday: isChangeDayToday
      });
    },

    formatDate(date) {
      const pad = n => n.toString().padStart(2, '0')
      return `${date.getFullYear()}-${pad(date.getMonth()+1)}-${pad(date.getDate())}`
    },

    onLoad() {
      // 生成年份选择范围
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      const years = [];
      for (let y = 2019; y <= currentYear + 1; y++) {
        years.push(y + '');
      }

      this.setData({
        yearRange: years, // 用于picker组件
        currentMonth: currentMonth,
        year: currentYear.toString(), // 设置默认年份为当前年份
        // 确保首次加载时至少有一个空的nextShift对象
        nextShift: this.data.nextShift || null
      });
      this.checkWechatLoginStatus();
      this.loadData();
    },

    onShow() {
      this.checkWechatLoginStatus();
      this.loadData();

      // 检查是否从管理页面返回，如果是则激活编辑模式
      this.checkEditModeFromAdmin();
    },

    // 检查是否从管理页面返回
    checkEditModeFromAdmin() {
      // 检查是否有编辑模式标记
      const editModeFlag = wx.getStorageSync('edit_mode_activated');
      if (editModeFlag) {
        this.setData({ isEditMode: true });
        wx.showToast({
          title: '已进入编辑模式',
          icon: 'success',
          duration: 1500
        });
        // 清除标记，避免重复激活
        wx.removeStorageSync('edit_mode_activated');
      }
    },

    // 检查微信登录状态和权限
    checkWechatLoginStatus: function() {
      const wechatToken = wx.getStorageSync('wechat_token');
      const userInfo = wx.getStorageSync('user_info');
      const isGuestMode = wx.getStorageSync('guest_mode');

      if (wechatToken && userInfo && !isGuestMode) {
        // 已登录并绑定系统账号，检查权限
        const role = userInfo.role;
        const hasPermission = role === 'admin' || role === 'manager';

        const roleTexts = {
          'admin': '系统管理员',
          'manager': '普通管理员',
          'station_staff': '一站人员',
          'user': '普通用户'
        };

        this.setData({
          isLoggedIn: true,
          isAdmin: hasPermission, // 保持兼容性
          hasPermission: hasPermission,
          isGuestMode: false,
          username: userInfo.real_name || userInfo.username,
          role: role,
          roleText: roleTexts[role] || '普通用户'
          // 注意：不在这里设置 isEditMode，保持默认的 false
        });
      } else if (isGuestMode) {
        // 游客模式
        this.setData({
          isLoggedIn: false,
          isAdmin: false,
          hasPermission: false,
          isGuestMode: true,
          username: '',
          role: '',
          roleText: ''
        });
      } else {
        // 未登录
        this.setData({
          isLoggedIn: false,
          isAdmin: false,
          hasPermission: false,
          isGuestMode: false,
          username: '',
          role: '',
          roleText: ''
        });
      }
    },

    // 退出编辑模式（不退出账号）
    handleLogout() {
      wx.showModal({
        title: '退出编辑模式',
        content: '确定要退出编辑模式吗？账号不会退出登录。',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 退出编辑模式
            this.setData({
              isEditMode: false,
              isAdmin: false,
              hasPermission: false
            });
            wx.showToast({
              title: '已退出编辑模式',
              icon: 'success'
            });
          }
        }
      });
    },

    // 加载数据
    loadData() {
      wx.showLoading({ title: '加载中...' });
      const currentYear = new Date().getFullYear();
      const endYear = (currentYear + 1).toString();

      Promise.all([
        // 获取选中年份的数据（用于显示列表）
        new Promise((resolve, reject) => {
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
            data: {
              action: 'get_data',
              year: this.data.year,
              start_year: "2019",
              end_year: endYear
            },
            success: (res) => resolve(res.data),
            fail: (err) => reject(err)
          });
        }),
        // 获取当前年份及未来的数据（用于计算下次倒班时间）
        new Promise((resolve, reject) => {
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
            data: {
              action: 'get_data',
              year: currentYear.toString(),
              start_year: currentYear.toString(),
              end_year: endYear
            },
            success: (res) => resolve(res.data),
            fail: (err) => reject(err)
          });
        }),
        new Promise((resolve, reject) => {
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
            data: {
              action: 'get_remarks',
              year: this.data.year
            },
            success: (res) => resolve(res.data),
            fail: (err) => reject(err)
          });
        })
      ]).then(([scheduleData, nextShiftData, remarkData]) => {
        // 处理排班数据（显示用）
        if (scheduleData?.status === 'success') {
          this.processData(scheduleData.data || []);

          // 在数据处理完成后生成日历数据(如果当前是日历模式)
          if (this.data.viewMode === 'calendar') {
            this.generateCalendarData();
          }
        } else {
          console.error('排班数据异常:', scheduleData);
        }

        // 处理下次倒班时间数据（基于当前年份）
        if (nextShiftData?.status === 'success') {
          this.calculateNextShift(nextShiftData.data || []);
        }

        // 处理备注数据
        if (remarkData?.status === 'success') {
          this.setData({ remarks: remarkData.remarks || {} });
        } else {
          console.error('备注数据异常:', remarkData);
        }

        // 数据加载完成后，如果是列表模式且是当前年份，自动滚动到当前月份
        this.scrollToCurrentMonth();
      }).catch((error) => {
        console.error('请求失败:', error);
        wx.showToast({ title: '加载失败', icon: 'none' });
      }).finally(() => {
        wx.hideLoading();
      });
    },

    // 滚动到当前月份
    scrollToCurrentMonth() {
      if (this.data.viewMode === 'list') {
        const currentYear = new Date().getFullYear();
        // 只有当查看的是当前年份时才自动滚动到当前月份
        if (parseInt(this.data.year) === currentYear) {
          // 先清空，再设置，确保触发滚动
          this.setData({
            scrollIntoView: ''
          });
          setTimeout(() => {
            const targetId = 'month-' + this.data.currentMonth;
            this.setData({
              scrollIntoView: targetId
            });
          }, 300);
        }
      }
    },

    // 新增跳转方法
    navToRemark(e) {
      const { year, month } = e.currentTarget.dataset;
      wx.navigateTo({
        url: `/pages/seaSchedule/remark?year=${year}&month=${month}`
      });
    },

    // 删除记录
    deleteRecord(e) {
      const id = e.currentTarget.dataset.id;
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        success: (res) => {
          if (res.confirm) {
            wx.showLoading({ title: '删除中...' });

            // 使用微信登录状态创建临时认证
            const wechatToken = wx.getStorageSync('wechat_token');
            const userInfo = wx.getStorageSync('user_info');

            wx.request({
              url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
              method: 'POST',
              data: {
                action: 'delete_schedule',
                id,
                wechat_auth: true,
                user_info: wx.getStorageSync('user_info')
              },
              complete: () => wx.hideLoading(),
              success: () => {
                this.loadData();
                wx.showToast({ title: '删除成功' });
              },
              fail: () => {
                wx.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    changeYear(e) {
      const selectedIndex = e.detail.value;
      const year = this.data.yearRange[selectedIndex];
      this.setData({
        year,
        displayMonth: this.data.currentMonth, // 切换年份时重置为当前月份
        calendarData: {}, // 清空日历数据，以便重新生成
        scrollIntoView: '' // 清空滚动位置
      });
      this.loadData();
    },

    // 跳转编辑页面
    navToEdit(e) {
      const item = e.currentTarget.dataset.item;
      wx.navigateTo({
        url: `/pages/seaSchedule/admin?editData=${JSON.stringify(item)}`
      });
    },

    // 跳转新增页面
    navToAdmin() {
      wx.navigateTo({
        url: '/pages/seaSchedule/admin'
      });
    },

    // 转发给朋友
    onShareAppMessage() {
      return {
        title: '平台常用计算工具-倒班时间查询',
        path: 'pages/seaSchedule/index'
      };
    },
  
    // 分享到朋友圈（需基础库 2.11.3+）
    onShareTimeline() {
      return {
        title: '平台常用计算工具-倒班时间查询',
        query: 'from=timeline'
      };
  }
});