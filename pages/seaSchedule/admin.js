// pages/seaSchedule/admin.js

Page({
  data: {
    isLoggedIn: false,
    hasPermission: false,
    username: '',
    password: '',
    station: '一站',
    stationIndex: 0,
    startDate: '',
    endDate: '',
    days: 0,
    editMode: false,
    editId: null,
    remark: '',
    rememberUsername: false,
    rememberPassword: false,
    role: '',
    roleText: '',
    isGuestMode: false,
    isSubmitting: false
  },

  onLoad(options) {
    // 检查微信登录状态
    this.checkWechatLoginStatus();

    // 设置编辑模式标记，表示用户通过管理页面进入
    wx.setStorageSync('edit_mode_activated', true);

    if (options.editData) {
      const editData = JSON.parse(options.editData);
      this.setData({
        station: editData.station,
        stationIndex: editData.station === '一站' ? 0 : 1,
        startDate: editData.start_date,
        endDate: editData.end_date,
        editMode: true,
        editId: editData.id
      });
      this.calculateDays();
    }
  },

  // 输入处理
  inputRemark(e) {
    this.setData({ remark: e.detail.value });
  },

  onShow() {
    // 每次显示页面时检查微信登录状态
    this.checkWechatLoginStatus();
  },

  // 检查微信登录状态和权限
  checkWechatLoginStatus: function() {
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isGuestMode = wx.getStorageSync('guest_mode');

    if (wechatToken && userInfo && !isGuestMode) {
      // 已登录并绑定系统账号，检查权限
      const role = userInfo.role;
      const hasPermission = role === 'admin' || role === 'manager';

      const roleTexts = {
        'admin': '系统管理员',
        'manager': '普通管理员',
        'station_staff': '一站人员',
        'user': '普通用户'
      };

      this.setData({
        isLoggedIn: true,
        hasPermission: hasPermission,
        isGuestMode: false,
        username: userInfo.real_name || userInfo.username,
        role: role,
        roleText: roleTexts[role] || '普通用户'
      });

      if (!hasPermission) {
        // 没有管理权限，显示提示并返回
        wx.showModal({
          title: '权限不足',
          content: '只有管理员可以使用此功能',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    } else if (isGuestMode) {
      // 游客模式，需要绑定系统账号
      this.setData({
        isLoggedIn: false,
        hasPermission: false,
        isGuestMode: true
      });
      wx.showModal({
        title: '需要绑定账号',
        content: '请先绑定系统账号后再使用管理功能',
        confirmText: '去绑定',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/index'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
    } else {
      // 未登录，需要先登录
      this.setData({
        isLoggedIn: false,
        hasPermission: false,
        isGuestMode: false
      });
      wx.showModal({
        title: '需要登录',
        content: '请先登录后再使用管理功能',
        confirmText: '去登录',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/index'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
    }
  },





  // 提交表单
  submitForm() {
    if (!this.validateForm()) return

    // 防止重复提交
    if (this.data.isSubmitting) {
      return;
    }

    this.setData({ isSubmitting: true });
    wx.showLoading({ title: '提交中...' })

    const requestData = this.prepareData();

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      data: requestData,
      complete: () => {
        this.setData({ isSubmitting: false });
        try {
          wx.hideLoading();
        } catch (e) {
          console.warn('hideLoading failed:', e);
        }
      },
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({ title: '提交成功', icon: 'success' })
          wx.navigateBack()
          this.refreshPreviousPage()
        } else {
          console.error('Submit failed:', res.data);
          wx.showToast({ title: '提交失败: ' + (res.data.message || '未知错误'), icon: 'none' })
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误，请重试', icon: 'none' })
      }
    })
  },

  // 其他辅助方法
  changeStation(e) {
    const value = e.detail.value;
    this.setData({
      station: value,
      stationIndex: value === '一站' ? 0 : 1
    });
  },

  changeStartDate(e) { 
    this.setData({ startDate: e.detail.value }, this.calculateDays);
  },

  changeEndDate(e) { 
    this.setData({ endDate: e.detail.value }, this.calculateDays);
  },

  calculateDays() {
    if (this.data.startDate && this.data.endDate) {
      // 统一使用ISO格式处理
      const start = new Date(this.data.startDate.replace(/ /g, 'T'));
      const end = new Date(this.data.endDate.replace(/ /g, 'T'));
      const days = Math.ceil((end - start) / 86400000) + 1;
      this.setData({ days });
    }
  },


  // 新增提交后停留方法
  submitAndStay() {
    if (!this.validateForm()) return

    // 防止重复提交
    if (this.data.isSubmitting) {
      return;
    }

    this.setData({ isSubmitting: true });
    wx.showLoading({ title: '提交中...' })
    const requestData = this.prepareData();

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      data: requestData,
      complete: () => {
        this.setData({ isSubmitting: false });
        try {
          wx.hideLoading();
        } catch (e) {
          console.warn('hideLoading failed:', e);
        }
      },
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({ title: '提交成功', icon: 'success' })
          this.resetForm()
          // 刷新上一页数据
          this.refreshPreviousPage()
        } else {
          wx.showToast({ title: '提交失败: ' + (res.data.message || '未知错误'), icon: 'none' })
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误，请重试', icon: 'none' })
      }
    })
  },

  // 辅助方法
  validateForm() {
    if (!this.data.startDate || !this.data.endDate) {
      wx.showToast({ title: '请填写完整日期', icon: 'none' })
      return false
    }
    return true
  },

  getHeaders() {
    // 尝试使用原来的token，如果没有则创建临时认证
    const oldToken = wx.getStorageSync('token');
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');

    if (oldToken) {
      // 如果有旧的token，使用旧的认证方式
      return {
        'Authorization': oldToken,
        'Content-Type': 'application/json'
      }
    } else {
      // 否则使用微信认证
      return {
        'Authorization': `Bearer ${wechatToken}`,
        'Content-Type': 'application/json',
        'X-User-Info': JSON.stringify(userInfo),
        'X-Wechat-Token': wechatToken
      }
    }
  },

  prepareData() {
    const data = {
      action: this.data.editMode ? 'update_schedule' : 'add_schedule',
      station: this.data.station,
      startDate: this.data.startDate,
      endDate: this.data.endDate,
      remark: this.data.remark || '',
      // 添加微信用户信息用于后端验证
      wechat_auth: true,
      user_info: wx.getStorageSync('user_info')
    };

    // 如果是编辑模式，添加ID
    if (this.data.editMode && this.data.editId) {
      data.id = this.data.editId;
    }

    return data;
  },



  resetForm() {
    this.setData({
      startDate: '',
      endDate: '',
      days: 0,
      station: '一站',
      stationIndex: 0
    })
  },

  refreshPreviousPage() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2]
      // 使用setTimeout确保当前页面的loading已经结束
      setTimeout(() => {
        prevPage.onLoad()
      }, 100)
    }
  }
})