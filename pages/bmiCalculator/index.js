Page({
  data: {
    height: '',
    weight: '',
    bmi: '',
    bmiStatus: '请输入身高和体重',
    standardWeight: '',
    riskLevel: '',
    bmiStatusClass: '',
    currentStandard: 'china' // 默认使用中国标准
  },

  onHeightInput(e) {
    const formattedValue = this.formatInputValue(e.detail.value);
    this.setData({
      height: formattedValue
    });
  },

  onWeightInput(e) {
    const formattedValue = this.formatInputValue(e.detail.value);
    this.setData({
      weight: formattedValue
    });
  },

  // 格式化输入值，限制只能输入数字和一个小数点，最多两位小数
  formatInputValue(value) {
    if (!value) return value;
    
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    
    return value;
  },

  // 切换BMI标准
  changeStandard(e) {
    const standard = e.currentTarget.dataset.standard;
    this.setData({ 
      currentStandard: standard 
    });
    
    // 如果已经计算过BMI，切换标准时重新计算
    if (this.data.bmi) {
      this.calculateBMI();
    }
  },

  calculateBMI() {
    const height = parseFloat(this.data.height);
    const weight = parseFloat(this.data.weight);

    if (!height || !weight || height <= 0 || weight <= 0) {
      wx.showToast({
        title: '请输入有效的身高和体重',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 计算BMI: 体重(kg) / 身高(m)的平方
    const heightInMeters = height / 100;
    const bmi = (weight / (heightInMeters * heightInMeters)).toFixed(1);
    
    // 计算标准体重: (身高(cm) - 100) * 0.9
    const standardWeight = ((height - 100) * 0.9).toFixed(1);

    // 根据BMI值判断健康状况
    let bmiStatus = '';
    let riskLevel = '';
    let bmiStatusClass = '';

    // 根据不同标准判断BMI状态
    const standard = this.data.currentStandard;

    if (standard === 'china') {
      // 中国标准
      if (bmi < 18.5) {
        bmiStatus = '偏瘦';
        riskLevel = '低';
        bmiStatusClass = 'status-underweight';
      } else if (bmi >= 18.5 && bmi < 24) {
        bmiStatus = '正常';
        riskLevel = '平均';
        bmiStatusClass = 'status-normal';
      } else if (bmi >= 24 && bmi < 28) {
        bmiStatus = '偏胖';
        riskLevel = '增加';
        bmiStatusClass = 'status-overweight';
      } else {
        bmiStatus = '肥胖';
        riskLevel = '高';
        bmiStatusClass = 'status-obese';
      }
    } else if (standard === 'international') {
      // 国际标准
      if (bmi < 18.5) {
        bmiStatus = '偏瘦';
        riskLevel = '低';
        bmiStatusClass = 'status-underweight';
      } else if (bmi >= 18.5 && bmi < 25) {
        bmiStatus = '正常';
        riskLevel = '平均';
        bmiStatusClass = 'status-normal';
      } else if (bmi >= 25 && bmi < 30) {
        bmiStatus = '偏胖';
        riskLevel = '增加';
        bmiStatusClass = 'status-overweight';
      } else if (bmi >= 30 && bmi < 35) {
        bmiStatus = '肥胖';
        riskLevel = '高';
        bmiStatusClass = 'status-obese';
      } else if (bmi >= 35 && bmi < 40) {
        bmiStatus = '重度肥胖';
        riskLevel = '很高';
        bmiStatusClass = 'status-obese';
      } else {
        bmiStatus = '极重度肥胖';
        riskLevel = '极高';
        bmiStatusClass = 'status-obese';
      }
    } else if (standard === 'asia') {
      // 亚洲标准
      if (bmi < 18.5) {
        bmiStatus = '偏瘦';
        riskLevel = '低';
        bmiStatusClass = 'status-underweight';
      } else if (bmi >= 18.5 && bmi < 23) {
        bmiStatus = '正常';
        riskLevel = '平均';
        bmiStatusClass = 'status-normal';
      } else if (bmi >= 23 && bmi < 25) {
        bmiStatus = '偏胖';
        riskLevel = '增加';
        bmiStatusClass = 'status-overweight';
      } else if (bmi >= 25 && bmi < 30) {
        bmiStatus = '肥胖';
        riskLevel = '高';
        bmiStatusClass = 'status-obese';
      } else {
        bmiStatus = '重度肥胖';
        riskLevel = '很高';
        bmiStatusClass = 'status-obese';
      }
    }

    this.setData({
      bmi,
      bmiStatus,
      standardWeight,
      riskLevel,
      bmiStatusClass
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 页面加载时设置初始的提示文字
    this.setData({
      bmiStatus: '请输入身高和体重',
      bmiStatusClass: ''  // 确保初始状态无颜色
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新时重置数据
    this.setData({
      height: '',
      weight: '',
      bmi: '',
      bmiStatus: '请输入身高和体重',
      standardWeight: '',
      riskLevel: '',
      bmiStatusClass: ''
    });
    wx.stopPullDownRefresh();
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-BMI计算器',
      path: '/pages/bmiCalculator/index'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-BMI计算器',
      query: 'from=timeline'
    };
  }
});