.container {
  display: flex;
  flex-direction: column;
  padding: 25rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #21a557, #19975e);
  color: #fff;
}

.bmi-display {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 35rpx 0;
  margin: 15rpx 0 20rpx;
  text-align: center;
  position: relative;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.bmi-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 5rpx;
  display: block;
  letter-spacing: 2rpx;
}

.bmi-value {
  font-size: 90rpx;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 20rpx;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.status-line {
  margin-bottom: 15rpx;
}

.bmi-status {
  font-size: 30rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin-top: 15rpx;
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.25);
}

.standard-weight {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 12rpx;
}

.risk-level {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  text-align: center;
  padding: 15rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.input-group {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 10rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-row {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.input-row:last-child {
  border-bottom: none;
}

.input-label {
  width: 70rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.input-field {
  flex: 1;
  margin: 0 20rpx;
  height: 55rpx;
  font-size: 34rpx;
  color: #333;
}

.input-unit {
  font-size: 28rpx;
  color: #666;
  width: 60rpx;
  text-align: right;
}

.calculate-btn {
  background-color: #ffffff;
  color: #21a557;
  font-size: 34rpx;
  font-weight: bold;
  border-radius: 50rpx;
  padding: 16rpx 0;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  letter-spacing: 4rpx;
}

.calculate-btn::after {
  border: none;
}

/* 标准选择器 */
.standard-selector {
  display: flex;
  width: 100%;
  margin: 10rpx 0;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 10rpx;
  overflow: hidden;
}

.standard-item {
  flex: 1;
  text-align: center;
  padding: 12rpx 8rpx;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.standard-item.active {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 500;
}

/* 旧的标准标签样式，保留向后兼容 */
.standard-label {
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30rpx;
  padding: 10rpx 0;
  width: 180rpx;
  margin: 0 auto 20rpx;
  letter-spacing: 2rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.bmi-table {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  background-color: rgba(0, 0, 0, 0.15);
  font-weight: bold;
}

.table-row {
  display: flex;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.table-col {
  flex: 1;
  padding: 14rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  letter-spacing: 1rpx;
}

/* 不同状态的颜色 */
.status-underweight {
  background-color: #81c2f2;
  color: white;
}

.status-normal {
  background-color: #58c176;
  color: white;
}

.status-overweight {
  background-color: #ffb856;
  color: white;
}

.status-obese {
  background-color: #ff6d6d;
  color: white;
} 