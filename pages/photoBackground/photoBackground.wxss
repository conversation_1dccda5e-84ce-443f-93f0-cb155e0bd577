/* 证件照换背景页面样式 - Apple风格 */

.container {
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.main-content {
  padding: 20rpx;
}

/* 上传区域 - Apple风格 */
.upload-section {
  margin-bottom: 24rpx;
}

.upload-area {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
  transition: all 0.2s ease;
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

.upload-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.8;
}

.upload-text {
  font-size: 28rpx;
  color: #1d1d1f;
  margin-bottom: 8rpx;
  font-weight: 500;
  letter-spacing: -0.5rpx;
}

.upload-hint {
  font-size: 22rpx;
  color: #86868b;
  line-height: 1.4;
}

/* 图片预览 - Apple风格 */
.image-preview-container {
  text-align: center;
  margin-bottom: 24rpx;
}

.image-preview {
  display: inline-block;
  background: white;
  border-radius: 16rpx;
  padding: 12rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
  line-height: 0;
}

.preview-image {
  max-width: 280rpx;
  max-height: 380rpx;
  border-radius: 12rpx;
  display: block;
  vertical-align: top;
}

.image-actions {
  margin-top: 16rpx;
}

/* 操作行布局 */
.action-row {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  margin-bottom: 12rpx;
  gap: 16rpx;
  height: 64rpx;
}

.image-actions .btn {
  height: 64rpx;
  font-size: 24rpx;
  border-radius: 12rpx;
  flex: 1;
  margin: 0;
  padding: 0;
  line-height: 64rpx;
  text-align: center;
  border: none;
  background: #007AFF;
  color: white;
}

/* 智能优化开关样式 */
.smart-optimization-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  height: 64rpx;
  min-width: 200rpx;
  box-sizing: border-box;
}

.toggle-text {
  font-size: 24rpx;
  color: #1d1d1f;
  font-weight: 500;
  margin-right: 16rpx;
  white-space: nowrap;
}

.toggle-hint {
  font-size: 20rpx;
  color: #86868b;
  line-height: 1.4;
  text-align: center;
}

/* 背景色选择 - Apple风格 */
.color-selection {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 16rpx;
  letter-spacing: -0.5rpx;
}

.color-grid {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.color-item {
  text-align: center;
  padding: 12rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.color-item.selected {
  background: rgba(0,122,255,0.08);
}

.color-option {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  margin: 0 auto 8rpx;
  border: 3rpx solid transparent;
  transition: all 0.2s ease;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.color-option.selected {
  border-color: #007aff;
  transform: scale(1.1);
  box-shadow: 0 6rpx 16rpx rgba(0,122,255,0.4);
}

.color-option.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.3);
}

.color-option.red { background: #ff3b30; }
.color-option.blue { background: #007aff; }
.color-option.white {
  background: #ffffff;
  border: 3rpx solid #e5e5ea;
}

.color-option.white.selected::after {
  color: #007aff;
  text-shadow: none;
}
.color-option.blue-white {
  background: linear-gradient(180deg, #007aff 0%, #5ac8fa 50%, #ffffff 100%);
}

.color-label {
  font-size: 22rpx;
  color: #86868b;
  font-weight: 400;
  transition: all 0.2s ease;
}

.color-item .color-option.selected + .color-label {
  color: #007aff;
  font-weight: 600;
}

/* 尺寸选择 - Apple风格 */
.size-selection {
  margin-bottom: 24rpx;
}

/* 功能选择 - Apple风格 */
.function-selection {
  margin-bottom: 20rpx;
}

.function-options {
  background: white;
  border-radius: 16rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}

.function-card {
  background: #f9f9f9;
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  text-align: center;
  border: 1rpx solid rgba(0,0,0,0.06);
  transition: all 0.2s ease;
}

.function-card:active {
  transform: scale(0.98);
  background: rgba(0,122,255,0.05);
  border-color: #007aff;
}

.function-icon {
  font-size: 32rpx;
  margin-bottom: 6rpx;
}

.function-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4rpx;
  letter-spacing: -0.3rpx;
}

.function-desc {
  font-size: 20rpx;
  color: #86868b;
  line-height: 1.3;
}

/* 压缩选项 - Apple风格 */
.compress-selection {
  margin-bottom: 24rpx;
}

.compress-options {
  background: white;
  border-radius: 16rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}

.compress-option {
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  background: #f9f9f9;
  border: 1rpx solid rgba(0,0,0,0.06);
  transition: all 0.2s ease;
}

.compress-option.selected {
  background: rgba(0,122,255,0.05);
  border-color: #007aff;
}

.compress-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4rpx;
  letter-spacing: -0.3rpx;
}

.compress-desc {
  font-size: 20rpx;
  color: #86868b;
}

.compress-option.selected .compress-value {
  color: #007aff;
}

.compress-option.selected .compress-desc {
  color: #007aff;
}

/* 自定义大小输入 */
.custom-size-input {
  margin-top: 12rpx;
  text-align: center;
}

.input-container {
  display: inline-flex;
  align-items: center;
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  min-width: 160rpx;
  max-width: 240rpx;
}

.size-input {
  width: 120rpx;
  font-size: 24rpx;
  color: #1d1d1f;
  background: transparent;
  border: none;
  outline: none;
  text-align: center;
}

.input-unit {
  font-size: 20rpx;
  color: #86868b;
  margin-left: 4rpx;
  font-weight: 500;
}

.size-range-hint {
  font-size: 20rpx;
  color: #86868b;
  margin-top: 8rpx;
  line-height: 1.4;
  white-space: nowrap;
  overflow: visible;
}

.size-grid {
  background: white;
  border-radius: 16rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}

.size-option {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  border-radius: 12rpx;
  background: #f9f9f9;
  transition: all 0.2s ease;
}

.size-option.selected {
  border-color: #007aff;
  background: rgba(0,122,255,0.05);
}

.size-preview {
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
}

.size-rect {
  background: #007aff;
  border-radius: 3rpx;
  opacity: 0.8;
}

.size-info {
  flex: 1;
}

.size-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 2rpx;
  letter-spacing: -0.3rpx;
}

.size-detail {
  font-size: 20rpx;
  color: #86868b;
}

/* 进度条 */
.progress-section {
  margin-bottom: 30rpx;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.status-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* 按钮样式 - Apple风格 */
.action-buttons {
  margin-bottom: 24rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
  letter-spacing: -0.3rpx;
}

.btn-primary {
  background: #007aff;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0,122,255,0.2);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0,122,255,0.3);
}

.btn-primary.disabled {
  background: #e5e5ea;
  color: #86868b;
  box-shadow: none;
}

.btn-secondary {
  background: #f2f2f7;
  color: #007aff;
  border: none;
  font-weight: 500;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  border: none;
  font-weight: 500;
}

.action-btn.secondary {
  background: #f2f2f7;
  color: #007aff;
}

/* 结果展示 */
.result-section {
  margin-bottom: 30rpx;
}



.result-actions {
  display: flex;
  gap: 20rpx;
}

.result-actions .btn {
  flex: 1;
}

/* 功能说明 - Apple风格 */
.features-section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 24rpx;
  border: 1rpx solid rgba(0,0,0,0.06);
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);
}

.feature-list {
  margin-top: 16rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #1d1d1f;
  line-height: 1.4;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  background: #34c759;
  border-radius: 50%;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
}

/* 隐私保护提示 */
.privacy-notice {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1rpx solid #dee2e6;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
}

.notice-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8rpx;
  letter-spacing: -0.3rpx;
}

.notice-text {
  font-size: 22rpx;
  color: #6c757d;
  line-height: 1.6;
  letter-spacing: -0.2rpx;
}

/* Canvas隐藏 */
.process-canvas {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  width: 1rpx;
  height: 1rpx;
}
