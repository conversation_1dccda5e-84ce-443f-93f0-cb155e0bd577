Page({
  data: {
    activeTab: 0,
    tabs: [
      { id: 0, name: "住房租金", selected: true },
      { id: 1, name: "住房贷款利息", selected: false },
      { id: 2, name: "继续教育", selected: false },
      { id: 3, name: "赡养老人", selected: false },
      { id: 4, name: "3岁以下婴幼儿照护", selected: false },
      { id: 5, name: "子女教育", selected: false },
      { id: 6, name: "大病医疗", selected: false }
    ],
    deductionDetails: [
      {
        title: "住房租金",
        content: "根据城市等级不同：直辖市、省会城市每月1500元；人口超过100万的城市每月1100元；人口不超过100万的城市每月800元。",
        applicable: "适用于纳税人在主要工作城市没有自有住房而发生的住房租金支出。",
        color: "#F3E5F5",
        extraInfo: {
          range: "在主要工作城市没有自有住房而发生的住房租金支出。",
          standards: [
            { title: "直辖市、省会（首府）城市、计划单列市以及国务院确定的其他城市", amount: "1500元/月", amountType: "（定额扣除）" },
            { title: "除第一项所列城市以外，市辖区户籍人口超过100万的城市", amount: "1100元/月", amountType: "（定额扣除）" },
            { title: "除第一项所列城市以外，市辖区户籍人口不超过100万（含）的城市", amount: "800元/月", amountType: "（定额扣除）" }
          ],
          subjects: [
            "纳税人未婚：由本人扣除",
            "夫妻双方主要工作城市相同：只能由一方扣除",
            "夫妻双方主要工作城市不相同：可以分别扣除"
          ],
          notes: [
            "1.纳税人的配偶在纳税人的主要工作城市有自有住房的，视同纳税人在主要工作城市有自有住房。",
            "2.纳税人及其配偶在一个纳税年度内不能同时分别享受住房贷款利息和住房租金专项附加扣除。"
          ]
        }
      },
      {
        title: "住房贷款利息",
        content: "首套住房贷款每月1000元，可选择由一方全额扣除或夫妻双方各扣除50%。",
        applicable: "适用于纳税人本人或配偶使用商业银行或住房公积金个人住房贷款为本人或配偶购买中国境内住房发生的首套住房贷款利息支出。",
        color: "#E0F7FA",
        extraInfo: {
          range: "本人或配偶单独或共同使用商业银行或住房公积金个人住房贷款为本人或其配偶购买中国境内住房，发生的首套住房贷款利息支出。\n实际发生贷款利息的年度（扣除期限最长不超过240个月）。",
          standards: [
            { title: "", amount: "1000元/月", amountType: "（定额扣除）" }
          ],
          subjects: [
            "纳税人未婚：由本人扣除。",
            "纳税人已婚：经夫妻双方约定，可以选择由其中一方扣除。",
            "夫妻双方婚前分别购买住房发生的首套住房贷款利息支出：婚后可以选择其中一套购买的住房，由购买方按扣除标准的100%扣除；也可以由夫妻双方对各自购买的住房分别按扣除标准的50%扣除，具体扣除方式在一个纳税年度内不能变更。"
          ],
          notes: [
            "1.纳税人只能享受一次首套住房贷款的利息扣除。",
            "2.首套住房贷款是指购买住房享受首套住房贷款利率的住房贷款。",
            "3.纳税人及其配偶在一个纳税年度内不能同时分别享受住房贷款利息和住房租金专项附加扣除。"
          ]
        }
      },
      {
        title: "继续教育",
        content: "学历继续教育：每月400元，扣除期限不超过48个月；职业资格继续教育：每年3600元。",
        applicable: "学历继续教育可由本人或父母扣除，职业资格继续教育只能由本人扣除。",
        color: "#FFF3E0",
        extraInfo: {
          range: "在中国境内接受继续教育的支出。",
          standards: [
            { title: "学历（学位）继续教育", amount: "400元/月", amountType: "（定额扣除）" },
            { title: "职业资格继续教育", amount: "3600元/年", amountType: "（定额扣除）" }
          ],
          subjects: [
            "本人扣除",
            "个人接受本科及以下学历（学位）继续教育，符合规定扣除条件的，可以选择由其父母扣除。"
          ],
          notes: []
        }
      },
      {
        title: "赡养老人",
        content: "独生子女每月3000元；非独生子女每月1500元（可由兄弟姐妹分摊）。",
        applicable: "适用于纳税人赡养60岁（含）以上父母以及其他法定赡养人的支出。",
        color: "#FFEBEE",
        extraInfo: {
          range: "纳税人赡养一位及以上年满60周岁（含）的父母，以及子女均已去世的年满60周岁（含）的祖父母、外祖父母的支出。",
          standards: [
            { title: "独生子女", amount: "3000元/月", amountType: "（定额扣除）" },
            { title: "非独生子女", amount: "纳税人与兄弟姐妹分摊每月3000元的扣除额度，每人分摊的额度不能超过1500元/月", amountType: "（定额扣除）" }
          ],
          subjects: [
            "本人扣除",
            "平均分摊：赡养人均摊",
            "约定分摊：赡养人自行约定分摊比例",
            "指定分摊：由被赡养人指定分摊比例"
          ],
          notes: [
            "1.约定或指定分摊的须签订书面分摊协议，指定分摊优先于约定分摊。",
            "2.分摊方式和额度在一个纳税年度内不能变更。"
          ]
        }
      },
      {
        title: "3岁以下婴幼儿照护",
        content: "每个3岁以下婴幼儿每月2000元，可选择由一方全额扣除或夫妻双方各扣除50%。",
        applicable: "适用于纳税人抚养3岁以下婴幼儿的情况。",
        color: "#E8F5E9",
        extraInfo: {
          range: "纳税人未满三周岁的子女在照护期间产生的相关支出。",
          standards: [
            { title: "", amount: "2000元/月/每个子女", amountType: "（定额扣除）" }
          ],
          subjects: [
            "父母（法定监护人）可以选择一方按扣除标准的100%扣除。",
            "父母（法定监护人）可以选择由双方分别按扣除标准的50%扣除。"
          ],
          notes: [
            "具体扣除方式在一个纳税年度内不能变更。"
          ]
        }
      },
      {
        title: "子女教育",
        content: "每个子女每月2000元，可选择由一方全额扣除或夫妻双方各扣除50%。",
        applicable: "适用于在中国境内接受学前教育和学历教育的子女。",
        color: "#E3F2FD",
        extraInfo: {
          range: [
            { title: "子女处于学前教育阶段的相关支出", desc: "子女年满3岁至小学入学前处于学前教育阶段" },
            { title: "子女接受全日制学历教育的相关支出", desc: "学历教育包括义务教育（小学、初中教育）、高中阶段教育（普通高中、中等职业、技工教育）、高等教育（大学专科、大学本科、硕士研究生、博士研究生教育）" }
          ],
          standards: [
            { title: "", amount: "2000元/月/每个子女", amountType: "（定额扣除）" }
          ],
          subjects: [
            "父母（法定监护人）可以选择一方按扣除标准的100%扣除。",
            "父母（法定监护人）可以选择由双方分别按扣除标准的50%扣除。"
          ],
          notes: [
            "具体扣除方式在一个纳税年度内不能变更。"
          ]
        }
      },
      {
        title: "大病医疗",
        content: "一个纳税年度内，在社会医疗保险管理信息系统记录的个人负担超过15000元的医药费用支出部分，为大病医疗专项附加扣除，最高扣除限额为80000元。",
        applicable: "可以由纳税人本人或其配偶扣除。",
        color: "#EDE7F6",
        extraInfo: {
          range: "在一个纳税年度内，纳税人发生的与基本医保相关的医药费用支出，扣除医保报销后个人负担（指医保目录范围内的自付部分）累计超过15000元的部分。",
          standards: [
            { title: "", amount: "纳税人在办理年度汇算清缴时，在80000元限额内据实扣除", amountType: "" }
          ],
          subjects: [
            "纳税人发生的医药费用支出可以选择由本人或其配偶扣除。",
            "未成年子女发生的医药费用支出可以选择由其父母一方扣除。"
          ],
          notes: []
        }
      }
    ]
  },
  
  onLoad: function(options) {
    // 页面加载时执行
    wx.setNavigationBarTitle({
      title: '专项附加扣除详情'
    });
    
    // 检查是否有指定的tab
    if (options && options.tab) {
      const tabIndex = parseInt(options.tab);
      if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex < this.data.tabs.length) {
        this.switchTab(null, tabIndex);
      }
    }
  },
  
  // 切换标签页
  switchTab: function(e, tabIndex) {
    let index = tabIndex;
    if (e) {
      index = e.currentTarget.dataset.index;
    }
    
    const tabs = this.data.tabs.map((tab, i) => {
      return {
        ...tab,
        selected: i === index
      };
    });
    
    this.setData({
      activeTab: index,
      tabs: tabs
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-养老金税费计算器',
      path: '/pages/pensionTaxCalculator/index'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-养老金税费计算器',
      query: 'from=timeline'
    };
  }
}); 