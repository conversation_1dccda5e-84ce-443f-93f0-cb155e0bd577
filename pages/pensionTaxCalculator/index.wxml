<view class="container">
  <view class="header">
    <text class="title">个人养老金节税计算器</text>
    <text class="subtitle">计算缴纳个人养老金后，在不同收入情况下可节省的税费金额</text>
  </view>
  
  <view class="calculator-card">
    <view class="card-title">
      <text class="icon">💰</text>
      <text>填写您的税务信息</text>
    </view>
    
    <view class="input-group">
      <text class="label">扣除五险一金后的年收入（元）</text>
      <text class="input-hint text-blue">包含全年一次性奖金等所有收入</text>
      <view class="input-container">
        <input type="digit" model:value="{{income}}" placeholder="请输入您的年收入" value="100000" />
        <text class="input-unit">元</text>
      </view>
    </view>
    
    <!-- 全年一次性奖金 -->
    <view class="input-group">
      <view class="deduction-header-row">
        <view class="label-wrapper">
          <text class="label nowrap">其中全年一次性奖金（元）</text>
        </view>
        <view class="tax-policy-tag">政策有效期至2027年12月31日</view>
      </view>
      <view class="input-container">
        <input type="digit" model:value="{{annualBonus}}" placeholder="请输入全年一次性奖金" value="0" />
        <text class="input-unit">元</text>
      </view>
      <view class="bonus-tax-option">
        <radio-group class="bonus-radio-group" bindchange="changeBonusTaxMethod">
          <label class="bonus-radio">
            <radio value="separate" checked="{{bonusTaxMethod === 'separate'}}" />
            <text>单独计税</text>
          </label>
          <label class="bonus-radio">
            <radio value="combined" checked="{{bonusTaxMethod === 'combined'}}" />
            <text>并入综合所得</text>
          </label>
        </radio-group>
        <text class="bonus-tax-note text-orange">注：一个纳税年度内仅允许使用一次单独计税政策</text>
      </view>
    </view>
    
    <view class="input-group">
      <view class="deduction-header-row">
        <text class="label">专项附加扣除总额（元）</text>
        <view class="view-details" bindtap="viewDeductionDetails" style="margin-left: 20rpx; margin-top: 10rpx; margin-bottom: 10rpx;">查看扣除标准</view>
      </view>
      <text class="input-hint text-green">可直接输入金额或在下方选择扣除项目</text>
      <view class="input-container">
        <input type="digit" model:value="{{deductions}}" placeholder="专项附加扣除总额" bindinput="onDeductionsInput" />
        <text class="input-unit">元</text>
      </view>
      
      <view class="deduction-section">
        <text class="section-title">子女相关扣除</text>
        
        <!-- 子女教育扣除 -->
        <view class="deduction-item {{selectedDeductions.childEducation.selected ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="childEducation">
          <view class="deduction-header">
            <text>子女教育</text>
            <text class="deduction-value">每个子女每月2000元</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.childEducation.selected}}" catchtap>
            <view class="control-row">
              <text class="control-label">子女数量</text>
              <view class="counter-control">
                <view class="counter-btn" bindtap="changeChildCount" data-type="childEducation" data-action="minus">-</view>
                <text class="counter-value">{{selectedDeductions.childEducation.count}}</text>
                <view class="counter-btn" bindtap="changeChildCount" data-type="childEducation" data-action="add">+</view>
              </view>
            </view>
            
            <view class="control-row">
              <text class="control-label">扣除方式</text>
              <radio-group class="deduction-radio-group" bindchange="changeDeductionMethod" data-type="childEducation">
                <label class="deduction-radio">
                  <radio value="single" checked="{{selectedDeductions.childEducation.deductionMethod === 'single'}}" />
                  <text>一方扣除</text>
                </label>
                <label class="deduction-radio">
                  <radio value="half" checked="{{selectedDeductions.childEducation.deductionMethod === 'half'}}" />
                  <text>夫妻双方各50%</text>
                </label>
              </radio-group>
            </view>
          </view>
        </view>
        
        <!-- 3岁以下婴幼儿照护 -->
        <view class="deduction-item {{selectedDeductions.infantCare.selected ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="infantCare">
          <view class="deduction-header">
            <text>3岁以下婴幼儿照护</text>
            <text class="deduction-value">每个子女每月2000元</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.infantCare.selected}}" catchtap>
            <view class="control-row">
              <text class="control-label">子女数量</text>
              <view class="counter-control">
                <view class="counter-btn" bindtap="changeChildCount" data-type="infantCare" data-action="minus">-</view>
                <text class="counter-value">{{selectedDeductions.infantCare.count}}</text>
                <view class="counter-btn" bindtap="changeChildCount" data-type="infantCare" data-action="add">+</view>
              </view>
            </view>
            
            <view class="control-row">
              <text class="control-label">扣除方式</text>
              <radio-group class="deduction-radio-group" bindchange="changeDeductionMethod" data-type="infantCare">
                <label class="deduction-radio">
                  <radio value="single" checked="{{selectedDeductions.infantCare.deductionMethod === 'single'}}" />
                  <text>一方扣除</text>
                </label>
                <label class="deduction-radio">
                  <radio value="half" checked="{{selectedDeductions.infantCare.deductionMethod === 'half'}}" />
                  <text>夫妻双方各50%</text>
                </label>
              </radio-group>
            </view>
          </view>
        </view>
      </view>
      
      <view class="deduction-section">
        <text class="section-title">住房相关扣除</text>
        
        <view class="housing-note">
          <text class="note-text text-orange">注：住房贷款利息和住房租金只能选择其中一项</text>
        </view>
        
        <!-- 住房贷款利息 -->
        <view class="deduction-item {{selectedDeductions.housingLoan.selected ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="housingLoan">
          <view class="deduction-header">
            <text>住房贷款利息</text>
            <text class="deduction-value">首套住房贷款每月1000元</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.housingLoan.selected}}" catchtap>
            <view class="control-row">
              <text class="control-label">扣除方式</text>
              <radio-group class="deduction-radio-group" bindchange="changeDeductionMethod" data-type="housingLoan">
                <label class="deduction-radio">
                  <radio value="single" checked="{{selectedDeductions.housingLoan.deductionMethod === 'single'}}" />
                  <text>一方扣除</text>
                </label>
                <label class="deduction-radio">
                  <radio value="half" checked="{{selectedDeductions.housingLoan.deductionMethod === 'half'}}" />
                  <text>夫妻双方各50%</text>
                </label>
              </radio-group>
            </view>
          </view>
        </view>
        
        <!-- 住房租金 -->
        <view class="deduction-item {{selectedDeductions.housingRent ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="housingRent">
          <view class="deduction-header">
            <text>住房租金</text>
            <text class="deduction-value">根据城市等级</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.housingRent}}" catchtap>
            <text class="control-label">城市等级</text>
            <radio-group class="city-radio-group" bindchange="changeCityLevel">
              <label class="city-radio">
                <radio value="firstTier" checked="{{cityLevel === 'firstTier'}}" />
                <text>直辖市、省会城市(1500元/月)</text>
              </label>
              <label class="city-radio">
                <radio value="secondTier" checked="{{cityLevel === 'secondTier'}}" />
                <text>人口超过100万城市(1100元/月)</text>
              </label>
              <label class="city-radio">
                <radio value="thirdTier" checked="{{cityLevel === 'thirdTier'}}" />
                <text>人口不超过100万城市(800元/月)</text>
              </label>
            </radio-group>
          </view>
        </view>
      </view>
      
      <view class="deduction-section">
        <text class="section-title">其他扣除</text>
        
        <!-- 赡养老人 -->
        <view class="deduction-item {{selectedDeductions.elderlySupport.selected ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="elderlySupport">
          <view class="deduction-header">
            <text>赡养老人</text>
            <text class="deduction-value">独生子女3000元/月，非独生子女1500元/月</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.elderlySupport.selected}}" catchtap>
            <view class="control-row">
              <text class="control-label">是否独生子女</text>
              <switch checked="{{selectedDeductions.elderlySupport.isOnlyChild}}" bindchange="toggleOnlyChild" />
            </view>
          </view>
        </view>
        
        <!-- 继续教育 -->
        <view class="deduction-item {{selectedDeductions.continueEducation.selected ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="continueEducation">
          <view class="deduction-header">
            <text>继续教育</text>
            <text class="deduction-value">学历教育400元/月，职业资格3600元/年</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.continueEducation.selected}}" catchtap>
            <view class="control-row">
              <text class="control-label">教育类型</text>
              <radio-group class="deduction-radio-group" bindchange="changeEducationType">
                <label class="deduction-radio">
                  <radio value="degree" checked="{{selectedDeductions.continueEducation.type === 'degree'}}" />
                  <text>学历继续教育</text>
                </label>
                <label class="deduction-radio">
                  <radio value="skill" checked="{{selectedDeductions.continueEducation.type === 'skill'}}" />
                  <text>职业资格继续教育</text>
                </label>
              </radio-group>
            </view>
            
            <view class="control-row" wx:if="{{selectedDeductions.continueEducation.type === 'degree'}}">
              <text class="control-label">扣除主体</text>
              <radio-group class="deduction-radio-group" bindchange="changeDeductionMethod" data-type="continueEducation">
                <label class="deduction-radio">
                  <radio value="self" checked="{{selectedDeductions.continueEducation.deductionMethod === 'self'}}" />
                  <text>本人扣除</text>
                </label>
                <label class="deduction-radio">
                  <radio value="parent" checked="{{selectedDeductions.continueEducation.deductionMethod === 'parent'}}" />
                  <text>父母扣除</text>
                </label>
              </radio-group>
            </view>
          </view>
        </view>
        
        <!-- 大病医疗 -->
        <view class="deduction-item {{selectedDeductions.medicalFee.selected ? 'selected' : ''}}" 
              bindtap="toggleDeduction" data-type="medicalFee">
          <view class="deduction-header">
            <text>大病医疗</text>
            <text class="deduction-value">超过15000元部分，每年最高80000元</text>
          </view>
          
          <view class="deduction-controls" wx:if="{{selectedDeductions.medicalFee.selected}}" catchtap>
            <view class="control-row">
              <text class="control-label">扣除主体</text>
              <radio-group class="deduction-radio-group" bindchange="changeDeductionMethod" data-type="medicalFee">
                <label class="deduction-radio">
                  <radio value="self" checked="{{selectedDeductions.medicalFee.deductionMethod === 'self'}}" />
                  <text>本人扣除</text>
                </label>
                <label class="deduction-radio">
                  <radio value="spouse" checked="{{selectedDeductions.medicalFee.deductionMethod === 'spouse'}}" />
                  <text>配偶扣除</text>
                </label>
              </radio-group>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="pension-info">
      <view class="pension-label">个人养老金缴纳金额</view>
      <view class="pension-input-container">
        <input type="digit" model:value="{{pensionAmount}}" placeholder="请输入金额" value="12000" />
        <text class="input-unit">元/年</text>
      </view>
    </view>
    
    <button class="btn-calculate" bindtap="calculateTax">
      <text class="btn-icon">📊</text>
      <text>计算节税金额</text>
    </button>
  </view>
  
  <view class="result-card" wx:if="{{showResult}}">
    <view class="result-header">
      <text class="result-title">您每年可节省税费</text>
      <text class="highlight">{{savingAmount}} 元</text>
      <text class="saving-desc">通过缴纳个人养老金，您可享受税收优惠政策，相当于获得{{taxRate}}的额外收益</text>
      <text class="net-saving">考虑领取时3%税费后，实际节省 <text class="highlight-net">{{netSaving}} 元</text></text>
    </view>
    
    <view class="chart-wrapper">
      <!-- 添加图表数据表格作为显示方式 -->
      <view class="chart-data-table">
        <view class="table-row table-header">
          <view class="table-cell">项目</view>
          <view class="table-cell">金额</view>
        </view>
        <view class="table-row">
          <view class="table-cell">年收入(已扣五险一金)</view>
          <view class="table-cell">{{netIncome}} 元</view>
        </view>
        <view class="table-row" wx:if="{{annualBonusDisplay !== '0'}}">
          <view class="table-cell">其中：全年一次性奖金</view>
          <view class="table-cell">{{annualBonusDisplay}} 元</view>
        </view>
        <view class="table-row">
          <view class="table-cell">专项附加扣除金额</view>
          <view class="table-cell">{{specialDeductions}} 元</view>
        </view>
        <view class="table-row">
          <view class="table-cell">缴纳的个人养老金金额</view>
          <view class="table-cell">{{pensionAmountDisplay}} 元</view>
        </view>
        <view class="table-row">
          <view class="table-cell">缴纳个人养老金前税费</view>
          <view class="table-cell">{{taxBefore}} 元</view>
        </view>
        <view class="table-row" wx:if="{{bonusTax !== '0'}}">
          <view class="table-cell">其中：一次性奖金税费</view>
          <view class="table-cell">{{bonusTax}} 元</view>
        </view>
        <view class="table-row">
          <view class="table-cell">缴纳个人养老金后税费</view>
          <view class="table-cell">{{taxAfter}} 元</view>
        </view>
        <view class="table-row">
          <view class="table-cell">节省税费</view>
          <view class="table-cell">{{savingAmount}} 元</view>
        </view>
        <view class="table-row highlight-row">
          <view class="table-cell">净节省(扣除3%)</view>
          <view class="table-cell">{{netSaving}} 元</view>
        </view>
      </view>
    </view>
  </view>
  
  <view class="explanation">
    <view class="explanation-title">
      <text class="icon">ℹ️</text>
      <text>个人养老金节税说明</text>
    </view>
    <view class="note-list">
      <view class="note-item">
        <text class="note-text"><text class="bold">税收优惠：</text>个人养老金每年缴纳上限为 <text class="bold">12,000元</text>，缴纳金额可在税前扣除</text>
      </view>
      <view class="note-item">
        <text class="note-text"><text class="bold">节税原理：</text>通过减少应纳税所得额，降低个人所得税税率档次</text>
      </view>
      <view class="note-item">
        <text class="note-text"><text class="bold">计算公式：</text>应纳税所得额 = 年收入 - 60,000元(基本减除) - 专项附加扣除 - 个人养老金缴纳额（本程序所填入的年收入为扣除五险一金后的年收入，实际计算应纳税所得额还需减去五险一金等）</text>
      </view>
      <view class="note-item">
        <text class="note-text"><text class="bold">领取税率：</text>个人养老金在退休领取时需按<text class="bold">3%</text>的税率缴纳个人所得税</text>
      </view>
      <view class="note-item">
        <text class="note-text"><text class="bold">适用人群：</text>年收入超过9.6万元（月收入8000元以上）的人群效果更明显</text>
      </view>
        <view class="note-item">
        <text class="note-text"><text class="bold">全年一次性奖金：</text>根据财政部、税务总局2023年第30号公告，全年一次性奖金单独计税政策延续至2027年12月31日</text>
      </view>
      <view class="note-item">
        <text class="note-text"><text class="bold">注意事项：</text>本计算器依据2025年最新政策，实际税务情况请咨询当地税务部门</text>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <text>© {{currentYear}} 个人养老金节税计算器</text>
  </view>
</view> 