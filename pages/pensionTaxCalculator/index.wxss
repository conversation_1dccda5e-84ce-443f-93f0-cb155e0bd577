.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
  box-sizing: border-box;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 40rpx;
  background: linear-gradient(135deg, #1a3a5f 0%, #4a90e2 100%);
  border-radius: 20rpx;
  box-shadow: 0 10rpx 25rpx rgba(26, 58, 95, 0.3);
  color: white;
}

.title {
  font-size: 36rpx;
  margin-bottom: 10rpx;
  font-weight: 600;
  display: block;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.calculator-card, .result-card, .explanation {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 100, 0.1);
  padding: 30rpx;
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.card-title {
  display: flex;
  align-items: center;
  color: #1a3a5f;
  font-size: 32rpx;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f5ff;
  font-weight: 500;
}

.card-title .icon {
  margin-right: 15rpx;
  background: #f0f7ff;
  padding: 10rpx;
  border-radius: 50%;
  font-size: 36rpx;
}

.input-group {
  margin-bottom: 30rpx;
  width: 100%;
}

.label {
  display: block;
  margin-bottom: 15rpx;
  color: #5a7ba7;
  font-weight: 500;
  font-size: 28rpx;
}

.input-container {
  position: relative;
  width: 100%;
}

input {
  width: 100%;
  padding: 20rpx 60rpx 20rpx 30rpx;
  border: 2rpx solid #dce5f0;
  border-radius: 12rpx;
  font-size: 32rpx;
  background: #f9fbfd;
  color: #1a3a5f;
  box-sizing: border-box;
  text-align: left;
  min-height: 80rpx;
}

.input-unit {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #7d9cbf;
  font-size: 28rpx;
  z-index: 1;
}

.deduction-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
  width: 100%;
}

.deduction-item {
  background: #f0f7ff;
  border: 2rpx solid #d1e5ff;
  border-radius: 12rpx;
  padding: 20rpx;
  color: #4a7ab0;
  position: relative;
  box-sizing: border-box;
}

.deduction-item.selected {
  background: #e1f0ff;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2rpx rgba(74, 144, 226, 0.2);
}

.deduction-item.selected::after {
  content: "✓";
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  color: #4a90e2;
  font-weight: bold;
  font-size: 32rpx;
}

.deduction-value {
  font-weight: 600;
  color: #1a3a5f;
  display: block;
  margin-top: 10rpx;
  font-size: 24rpx;
}

.pension-info {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin: 30rpx 0;
  font-size: 28rpx;
  color: #4a7ab0;
}

.pension-label {
  white-space: nowrap;
  margin-right: 20rpx;
  font-weight: 500;
}

.pension-input-container {
  position: relative;
  flex: 1;
  max-width: 220rpx;
}

.pension-input-container input {
  width: 100%;
  padding: 16rpx 70rpx 16rpx 20rpx;
  border: 2rpx solid #dce5f0;
  border-radius: 12rpx;
  font-size: 30rpx;
  background: #f9fbfd;
  color: #1a3a5f;
  box-sizing: border-box;
  text-align: left;
  min-height: 70rpx;
}

.pension-value {
  font-weight: 700;
  color: #1a3a5f;
  font-size: 32rpx;
}

.btn-calculate {
  background: linear-gradient(135deg, #4a90e2 0%, #1a3a5f 100%);
  color: white;
  border: none;
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10rpx 20rpx rgba(74, 144, 226, 0.3);
  width: 100%;
}

.btn-icon {
  margin-right: 15rpx;
}

.result-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.result-title {
  color: #1a3a5f;
  font-size: 32rpx;
  margin-bottom: 15rpx;
  font-weight: 600;
  display: block;
}

.highlight {
  color: #e74c3c;
  font-size: 60rpx;
  font-weight: 700;
  display: block;
  margin: 20rpx 0;
  text-shadow: 0 2rpx 5rpx rgba(231, 76, 60, 0.2);
}

.saving-desc {
  color: #5a7ba7;
  font-size: 24rpx;
  display: block;
  line-height: 1.5;
}

.comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: 30rpx 0;
}

.comparison-item {
  background: #f9fbfd;
  border-radius: 16rpx;
  padding: 25rpx 20rpx;
  text-align: center;
  border: 2rpx solid #eaeff5;
}

.comparison-title {
  font-size: 24rpx;
  color: #5a7ba7;
  margin-bottom: 15rpx;
  font-weight: 500;
  display: block;
}

.comparison-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a3a5f;
  display: block;
}

.chart-wrapper {
  padding: 0;
  width: 100%;
  margin-top: 30rpx;
}

.chart-data-table {
  width: 100%;
  margin: 20rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eaeff5;
  background-color: #fff;
}

.table-header {
  background-color: #f0f7ff;
  font-weight: bold;
  color: #1a3a5f;
}

.table-cell {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #5a7ba7;
}

.highlight-row {
  background-color: rgba(255, 179, 102, 0.1);
}

.highlight-row .table-cell {
  font-weight: bold;
  color: #ff7e00;
}

.deduction-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.deduction-header {
  display: flex;
  flex-direction: column;
}

.deduction-controls {
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1px dashed #eaeff5;
}

.control-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.control-label {
  font-size: 24rpx;
  color: #5a7ba7;
}

.counter-control {
  display: flex;
  align-items: center;
}

.counter-btn {
  width: 50rpx;
  height: 50rpx;
  background: #f0f7ff;
  border: 1px solid #d1e5ff;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #4a90e2;
  font-weight: bold;
}

.counter-value {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #1a3a5f;
  font-weight: 600;
}

.city-radio-group {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}

.city-radio {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #5a7ba7;
}

.city-radio radio {
  margin-right: 10rpx;
  transform: scale(0.8);
}

.net-saving {
  color: #5a7ba7;
  font-size: 26rpx;
  display: block;
  margin-top: 15rpx;
  line-height: 1.5;
}

.highlight-net {
  color: #ff9f43;
  font-weight: 600;
}

.orange-dot {
  background: linear-gradient(135deg, #FFB366, #FFD9B3);
}

.deduction-radio-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.deduction-radio {
  display: flex;
  align-items: center;
  margin-right: 20px;
  font-size: 14px;
  color: #333;
}

.deduction-radio radio {
  transform: scale(0.8);
}

.highlight-row {
  background-color: rgba(255, 179, 102, 0.1);
}

.highlight-row .table-cell {
  font-weight: bold;
  color: #ff7e00;
}

.deduction-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.view-details {
  font-size: 12px;
  color: #3478f6;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(52, 120, 246, 0.1);
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .deduction-grid {
    grid-template-columns: 1fr;
  }
  
  .comparison {
    grid-template-columns: 1fr;
  }
  
  .highlight {
    font-size: 50rpx;
  }
}

/* 恢复说明内容样式 */
.explanation-title {
  color: #1a3a5f;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  font-weight: 600;
}

.explanation-title .icon {
  margin-right: 15rpx;
  color: #27ae60;
  background: #eafaf1;
  padding: 10rpx;
  border-radius: 50%;
}

.note-list {
  padding-left: 10rpx;
}

.note-item {
  margin-bottom: 15rpx;
  padding-left: 20rpx;
  position: relative;
}

.note-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #4a90e2;
}

.note-text {
  color: #5a7ba7;
  line-height: 1.6;
  font-size: 26rpx;
}

.bold {
  color: #1a3a5f;
  font-weight: 600;
}

.footer {
  text-align: center;
  color: #7d9cbf;
  font-size: 22rpx;
  margin-top: 40rpx;
  padding: 20rpx;
}

/* 全年一次性奖金样式 */
.tax-policy-tag {
  font-size: 22rpx;
  color: #ff7e00;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  background-color: rgba(255, 126, 0, 0.1);
  white-space: nowrap;
}

.bonus-tax-option {
  margin-top: 10rpx;
}

.bonus-radio-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 6rpx;
}

.bonus-radio {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  font-size: 26rpx;
  color: #1a3a5f;
}

.bonus-radio radio {
  transform: scale(0.8);
  margin-right: 4rpx;
}

.bonus-tax-note {
  font-size: 22rpx;
  color: #7d9cbf;
  display: block;
  margin-top: 6rpx;
}

.input-hint {
  display: block;
  margin-top: -10rpx;
  margin-bottom: 10rpx;
  color: #7d9cbf;
  font-size: 22rpx;
  font-weight: normal;
}

.label-wrapper {
  display: flex;
  align-items: center;
}

.nowrap {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 添加住房相关扣除互斥提示的样式 */
.housing-note {
  margin-bottom: 16rpx;
  padding: 10rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.housing-note .note-text {
  font-size: 24rpx;
  color: #666;
}

/* 添加文字颜色样式 */
.text-blue {
  color: #4a90e2;
}

.text-green {
  color: #2ecc71;
}

.text-orange {
  color: #f39c12;
}

.text-red {
  color: #e74c3c;
}

/* 注意文本样式 */
.input-hint {
  display: block;
  font-size: 24rpx;
  margin-bottom: 10rpx;
  color: #7d9cbf;
}

.housing-note {
  background-color: #fff8e1;
  border-left: 4rpx solid #ffb74d;
  padding: 16rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.note-text {
  font-size: 24rpx;
  line-height: 1.5;
  color: #5a7ba7;
} 