<view class="container">
  <view class="ios-header">
    <view class="status-bar"></view>
    <view class="nav-bar">
      <view class="back-btn" bindtap="goBack">
        <view class="back-content">
          <text class="back-arrow">‹</text>
          <text class="back-text">返回</text>
        </view>
      </view>
      <text class="nav-title">专项附加扣除</text>
    </view>
  </view>
  
  <!-- 选项卡区域 -->
  <view class="tabs-container">
    <scroll-view scroll-x="true" class="tabs-scroll" enhanced="true" show-scrollbar="false">
      <view class="tabs">
        <view 
          wx:for="{{tabs}}" 
          wx:key="id" 
          class="tab-item {{item.selected ? 'active' : ''}}"
          bindtap="switchTab"
          data-index="{{item.id}}">
          <text>{{item.name}}</text>
          <view class="tab-line" wx:if="{{item.selected}}"></view>
        </view>
      </view>
    </scroll-view>
  </view>
  
  <!-- 详情内容 -->
  <view class="detail-content">
    <block wx:if="{{activeTab === 0}}">
      <!-- 住房租金 -->
      <view class="detail-section light-purple">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view class="content-text">
              在<text class="highlight-text">主要工作城市</text>没有自有住房而发生的住房租金支出。
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view class="standard-row">
              <view class="standard-cell left-col">
                直辖市、省会（首府）城市、计划单列市以及国务院确定的其他城市
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>1500元/月</view>
                  <view class="note-text">（定额扣除）</view>
                </view>
              </view>
            </view>
            <view class="standard-row">
              <view class="standard-cell left-col">
                除第一项所列城市以外，市辖区户籍人口超过100万的城市
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>1100元/月</view>
                  <view class="note-text">（定额扣除）</view>
                </view>
              </view>
            </view>
            <view class="standard-row">
              <view class="standard-cell left-col">
                除第一项所列城市以外，市辖区户籍人口不超过100万（含）的城市
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>800元/月</view>
                  <view class="note-text">（定额扣除）</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view class="subject-item">
              <text class="bullet">▶</text> 纳税人未婚：由<text class="highlight-text">本人</text>扣除
            </view>
            <view class="subject-item">
              <text class="bullet">▶</text> 夫妻双方主要工作城市相同：只能由<text class="highlight-text">一方</text>扣除
            </view>
            <view class="subject-item">
              <text class="bullet">▶</text> 夫妻双方主要工作城市不相同：可以<text class="highlight-text">分别</text>扣除
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[0].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[0].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <block wx:elif="{{activeTab === 1}}">
      <!-- 住房贷款利息 -->
      <view class="detail-section light-blue">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view class="content-text">
              本人或配偶单独或共同使用商业银行或住房公积金个人住房贷款为本人或其配偶购买<text class="highlight-text">中国境内住房</text>，发生的<text class="highlight-text">首套</text>住房贷款利息支出。
            </view>
            <view class="content-text">
              实际发生贷款利息的年度（扣除期限最长不超过<text class="highlight-text">240</text>个月）。
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view class="standard-row">
              <view class="standard-cell left-col">首套住房贷款利息</view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>1000元/月</view>
                  <view class="note-text">（定额扣除）</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view class="subject-item">
              <text class="bullet">▶</text> 纳税人未婚：由<text class="highlight-text">本人</text>扣除。
            </view>
            <view class="subject-item">
              <text class="bullet">▶</text> 纳税人已婚：经夫妻双方约定，可以选择由其中<text class="highlight-text">一方</text>扣除。
            </view>
            <view class="subject-item">
              <text class="bullet">▶</text> 夫妻双方婚前分别购买住房发生的首套住房贷款利息支出：婚后可以选择其中一套购买的住房，由购买方按扣除标准的<text class="highlight-text">100%</text>扣除；也可以由夫妻双方对各自购买的住房分别按扣除标准的<text class="highlight-text">50%</text>扣除，具体扣除方式在一个纳税年度内不能变更。
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[1].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[1].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <block wx:elif="{{activeTab === 2}}">
      <!-- 继续教育 -->
      <view class="detail-section light-orange">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view class="content-text">
              {{deductionDetails[2].extraInfo.range}}
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view class="standard-row">
              <view class="standard-cell left-col">
                学历（学位）继续教育
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>{{deductionDetails[2].extraInfo.standards[0].amount}}</view>
                  <view class="note-text">{{deductionDetails[2].extraInfo.standards[0].amountType}}</view>
                </view>
              </view>
            </view>
            <view class="standard-row">
              <view class="standard-cell left-col">
                学历（学位）教育期间（扣除期限不超过48个月）
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>取得相关证书的当年</view>
                </view>
              </view>
            </view>
            <view class="standard-row">
              <view class="standard-cell left-col">
                职业资格继续教育
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>{{deductionDetails[2].extraInfo.standards[1].amount}}</view>
                  <view class="note-text">{{deductionDetails[2].extraInfo.standards[1].amountType}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[2].extraInfo.subjects}}" wx:key="*this" class="subject-item">
              <text class="bullet">▶</text> {{item}}
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[2].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[2].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <block wx:elif="{{activeTab === 3}}">
      <!-- 赡养老人 -->
      <view class="detail-section light-red">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view class="content-text">
              {{deductionDetails[3].extraInfo.range}}
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view wx:for="{{deductionDetails[3].extraInfo.standards}}" wx:key="title" class="standard-row">
              <view class="standard-cell left-col">
                {{item.title}}
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>{{item.amount}}</view>
                  <view class="note-text">{{item.amountType}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[3].extraInfo.subjects}}" wx:key="*this" class="subject-item">
              <text class="bullet">▶</text> {{item}}
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[3].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[3].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <block wx:elif="{{activeTab === 4}}">
      <!-- 3岁以下婴幼儿照护 -->
      <view class="detail-section light-green">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view class="content-text">
              {{deductionDetails[4].extraInfo.range}}
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view wx:for="{{deductionDetails[4].extraInfo.standards}}" wx:key="title" class="standard-row">
              <view class="standard-cell left-col">
                {{item.title || '3岁以下婴幼儿照护费用'}}
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>{{item.amount}}</view>
                  <view class="note-text">{{item.amountType}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[4].extraInfo.subjects}}" wx:key="*this" class="subject-item">
              <text class="bullet">▶</text> {{item}}
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[4].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[4].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <block wx:elif="{{activeTab === 5}}">
      <!-- 子女教育 -->
      <view class="detail-section light-blue-alt">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[5].extraInfo.range}}" wx:key="title" class="range-grid">
              <view class="range-title">{{item.title}}</view>
              <view class="range-desc">{{item.desc}}</view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view wx:for="{{deductionDetails[5].extraInfo.standards}}" wx:key="title" class="standard-row">
              <view class="standard-cell left-col">
                {{item.title || '子女教育费用'}}
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>{{item.amount}}</view>
                  <view class="note-text">{{item.amountType}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[5].extraInfo.subjects}}" wx:key="*this" class="subject-item">
              <text class="bullet">▶</text> {{item}}
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[5].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[5].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <block wx:elif="{{activeTab === 6}}">
      <!-- 大病医疗 -->
      <view class="detail-section light-purple-alt">
        <view class="section-block">
          <view class="section-title-row">扣除范围</view>
          <view class="section-content">
            <view class="content-text">
              {{deductionDetails[6].extraInfo.range}}
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除标准</view>
          <view class="standard-grid">
            <view wx:for="{{deductionDetails[6].extraInfo.standards}}" wx:key="title" class="standard-row">
              <view class="standard-cell left-col">
                {{item.title || '大病医疗费用'}}
              </view>
              <view class="standard-cell right-col highlight-amount">
                <view class="amount-container">
                  <view>{{item.amount}}</view>
                  <view class="note-text">{{item.amountType}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-block">
          <view class="section-title-row">扣除主体</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[6].extraInfo.subjects}}" wx:key="*this" class="subject-item">
              <text class="bullet">▶</text> {{item}}
            </view>
          </view>
        </view>
        
        <view class="section-block" wx:if="{{deductionDetails[6].extraInfo.notes.length > 0}}">
          <view class="section-title-row">注意事项</view>
          <view class="section-content">
            <view wx:for="{{deductionDetails[6].extraInfo.notes}}" wx:key="*this" class="note-item">
              <text class="note-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>
  
  <view class="footer">
    <text>数据依据中国现行个人所得税政策，实际税务情况请咨询当地税务主管部门</text>
  </view>
</view> 