.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  padding-bottom: 30rpx;
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.back-btn {
  position: absolute;
  left: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  height: 64rpx;
  padding: 0 10rpx;
  z-index: 2;
  border-radius: 32rpx;
  transition: background-color 0.2s;
}

.back-btn:active {
  background-color: rgba(0, 122, 255, 0.1);
}

.back-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.back-arrow {
  font-size: 56rpx;
  color: #007aff;
  line-height: 1;
  font-weight: 300;
  margin-right: 2rpx;
}

.back-text {
  font-size: 34rpx;
  color: #007aff;
  font-weight: 400;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
  margin-right: 80rpx;
}

/* Apple风格导航栏 */
.ios-header {
  width: 100%;
  background: rgba(248, 248, 250, 0.9);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.08);
}

.status-bar {
  height: 44rpx;
}

.nav-bar {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 20rpx;
}

.nav-title {
  font-size: 34rpx;
  color: #000;
  font-weight: 600;
  text-align: center;
  width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 选项卡区域 */
.tabs-container {
  padding: 10rpx 0;
  background: rgba(248, 248, 250, 0.8);
  margin-bottom: 20rpx;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.08);
}

.tabs-scroll {
  white-space: nowrap;
  width: 100%;
}

.tabs {
  display: inline-flex;
  padding: 0 20rpx;
}

.tab-item {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  position: relative;
  color: rgba(60, 60, 67, 0.6);
  font-weight: 400;
  transition: all 0.25s ease;
}

.tab-item.active {
  color: #007aff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 20rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}

/* 详情内容 */
.detail-content {
  padding: 0 24rpx 40rpx;
}

.detail-section {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24rpx;
  border: none;
}

.section-block {
  padding: 30rpx;
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.08);
}

.section-block:last-child {
  border-bottom: none;
}

.section-title-row {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1c1e;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-content {
  color: rgba(60, 60, 67, 0.85);
  font-size: 28rpx;
  line-height: 1.5;
}

.highlight-text {
  color: #007aff;
  font-weight: 500;
}

/* 表格网格样式 */
.standard-grid {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.standard-row {
  display: flex;
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.08);
}

.standard-row:last-child {
  border-bottom: none;
}

.standard-cell {
  padding: 24rpx 20rpx;
  font-size: 26rpx;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.left-col {
  flex: 3;
  color: rgba(60, 60, 67, 0.85);
  text-align: left;
  padding-right: 10rpx;
}

.right-col {
  flex: 2;
  text-align: center;
  font-weight: 600;
  justify-content: center;
}

.highlight-amount {
  color: #007aff;
}

.amount-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.note-text {
  font-size: 22rpx;
  color: rgba(60, 60, 67, 0.6);
  font-weight: normal;
  margin-top: 4rpx;
}

/* 主题和注意事项样式 */
.subject-item, .note-item {
  margin-bottom: 16rpx;
  font-size: 26rpx;
  line-height: 1.6;
  padding-left: 30rpx;
  position: relative;
}

.subject-item:last-child, .note-item:last-child {
  margin-bottom: 0;
}

.bullet {
  position: absolute;
  left: 0;
  color: #007aff;
  margin-right: 10rpx;
  font-size: 20rpx;
}

/* 范围项目样式 */
.range-grid {
  margin-bottom: 16rpx;
}

.range-grid:last-child {
  margin-bottom: 0;
}

.range-title {
  font-weight: bold;
  margin-bottom: 6rpx;
  font-size: 28rpx;
  color: #333;
}

.range-desc {
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 颜色区分不同板块 */
.light-purple .section-title-row,
.light-blue .section-title-row,
.light-orange .section-title-row,
.light-red .section-title-row, 
.light-green .section-title-row,
.light-blue-alt .section-title-row,
.light-purple-alt .section-title-row {
  position: relative;
  padding-left: 30rpx;
}

.light-purple {
  border-left: none;
}

.light-blue {
  border-left: none;
}

.light-orange {
  border-left: none;
}

.light-red {
  border-left: none;
}

.light-green {
  border-left: none;
}

.light-blue-alt {
  border-left: none;
}

.light-purple-alt {
  border-left: none;
}

/* 底部版权信息 */
.footer {
  text-align: center;
  color: #8e8e93;
  font-size: 24rpx;
  padding: 30rpx 0 50rpx;
} 