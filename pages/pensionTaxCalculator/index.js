// 删除引入微信小程序图表组件的注释
// const wxCharts = require('../../utils/wxcharts.js');
// var taxChart = null;

Page({
  data: {
    income: '100000',
    deductions: '0',
    annualBonus: '0', // 全年一次性奖金
    bonusTaxMethod: 'separate', // 奖金计税方式: separate-单独计税, combined-并入综合所得
    showResult: false,
    savingAmount: '0',
    taxBefore: '0',
    taxAfter: '0',
    bonusTax: '0', // 奖金税额
    taxRate: '0%',
    netSaving: '0', // 考虑3%领取税后的净节省
    currentYear: new Date().getFullYear(),
    pensionAmount: '12000', // 添加个人养老金金额字段，默认为12000
    // 增加结果展示需要的数据
    netIncome: '0', // 扣除五险一金后的年收入
    specialDeductions: '0', // 专项附加扣除金额
    manualDeductionInput: false, // 是否手动输入了专项附加扣除总额
    selectedDeductions: {
      childEducation: {
        selected: false,
        count: 1,
        deductionMethod: 'single',
        monthlyAmount: 2000
      },
      infantCare: {
        selected: false,
        count: 1,
        deductionMethod: 'single',
        monthlyAmount: 2000
      },
      continueEducation: {
        selected: false,
        type: 'degree',
        deductionMethod: 'self',
        monthlyAmount: 400,
        yearlyAmount: 3600
      },
      housingLoan: {
        selected: false,
        deductionMethod: 'single',
        monthlyAmount: 1000
      },
      housingRent: false,
      elderlySupport: {
        selected: false,
        isOnlyChild: true,
        monthlyAmount: 1500
      },
      medicalFee: {
        selected: false,
        deductionMethod: 'self',
        amount: 0
      }
    },
    cityLevel: 'firstTier' // 默认为一线城市
  },

  onLoad: function() {
    // 页面加载时执行
  },
  
  // 查看专项附加扣除详情
  viewDeductionDetails: function() {
    wx.navigateTo({
      url: '/pages/pensionTaxCalculator/deductionDetails'
    });
  },
  
  // 切换扣除项目选择状态
  toggleDeduction: function(e) {
    const type = e.currentTarget.dataset.type;
    const selectedDeductions = this.data.selectedDeductions;
    
    // 重置手动输入标记
    this.setData({
      manualDeductionInput: false
    });
    
    // 处理住房贷款利息和住房租金互斥的情况
    if (type === 'housingLoan' || type === 'housingRent') {
      if (type === 'housingLoan') {
        // 如果选择住房贷款利息，则取消住房租金
        if (!selectedDeductions.housingLoan.selected) {
          selectedDeductions.housingRent = false;
        }
        selectedDeductions.housingLoan.selected = !selectedDeductions.housingLoan.selected;
      } else if (type === 'housingRent') {
        // 如果选择住房租金，则取消住房贷款利息
        if (!selectedDeductions.housingRent) {
          selectedDeductions.housingLoan.selected = false;
        }
        selectedDeductions.housingRent = !selectedDeductions.housingRent;
      }
    } else {
      // 其他扣除项目正常切换
      if (typeof selectedDeductions[type] === 'object') {
        selectedDeductions[type].selected = !selectedDeductions[type].selected;
      } else {
        selectedDeductions[type] = !selectedDeductions[type];
      }
    }
    
    this.setData({
      selectedDeductions: selectedDeductions
    });
    
    this.calculateTotalDeduction();
  },
  
  // 更改子女数量
  changeChildCount: function(e) {
    const type = e.currentTarget.dataset.type;
    const action = e.currentTarget.dataset.action;
    const selectedDeductions = this.data.selectedDeductions;
    
    // 重置手动输入标记
    this.setData({
      manualDeductionInput: false
    });
    
    if (action === 'add') {
      selectedDeductions[type].count += 1;
    } else if (action === 'minus' && selectedDeductions[type].count > 1) {
      selectedDeductions[type].count -= 1;
    }
    
    this.setData({
      selectedDeductions: selectedDeductions
    });
    
    this.calculateTotalDeduction();
  },
  
  // 切换是否独生子女
  toggleOnlyChild: function(e) {
    const selectedDeductions = this.data.selectedDeductions;
    
    // 重置手动输入标记
    this.setData({
      manualDeductionInput: false
    });
    
    selectedDeductions.elderlySupport.isOnlyChild = !selectedDeductions.elderlySupport.isOnlyChild;
    
    this.setData({
      selectedDeductions: selectedDeductions
    });
    
    this.calculateTotalDeduction();
  },
  
  // 切换城市等级
  changeCityLevel: function(e) {
    // 重置手动输入标记
    this.setData({
      manualDeductionInput: false,
      cityLevel: e.detail.value
    });
    
    this.calculateTotalDeduction();
  },
  
  // 切换扣除方式
  changeDeductionMethod: function(e) {
    const type = e.currentTarget.dataset.type;
    const method = e.detail.value;
    const selectedDeductions = this.data.selectedDeductions;
    
    // 重置手动输入标记
    this.setData({
      manualDeductionInput: false
    });
    
    selectedDeductions[type].deductionMethod = method;
    
    this.setData({
      selectedDeductions: selectedDeductions
    });
    
    this.calculateTotalDeduction();
  },
  
  // 切换继续教育类型
  changeEducationType: function(e) {
    const type = e.detail.value;
    const selectedDeductions = this.data.selectedDeductions;
    
    // 重置手动输入标记
    this.setData({
      manualDeductionInput: false
    });
    
    selectedDeductions.continueEducation.type = type;
    
    this.setData({
      selectedDeductions: selectedDeductions
    });
    
    this.calculateTotalDeduction();
  },
  
  // 切换奖金计税方式
  changeBonusTaxMethod: function(e) {
    this.setData({
      bonusTaxMethod: e.detail.value
    });
  },
  
  // 处理个人养老金金额输入
  onPensionAmountInput: function(e) {
    this.setData({
      pensionAmount: e.detail.value
    });
  },
  
  // 计算总扣除额
  calculateTotalDeduction: function() {
    // 如果用户已手动输入了专项附加扣除总额，则不再自动计算
    if (this.data.manualDeductionInput) {
      return;
    }
    
    const selectedDeductions = this.data.selectedDeductions;
    let totalDeduction = 0;
    
    // 子女教育: 每个子女每月2000元
    if (selectedDeductions.childEducation.selected) {
      // 如果选择父母扣除，则本人不再计算该项扣除
      if (selectedDeductions.childEducation.deductionMethod !== 'parent') {
        const monthlyAmount = selectedDeductions.childEducation.count * selectedDeductions.childEducation.monthlyAmount;
        // 如果选择双方平分，则只计算一半
        const ratio = selectedDeductions.childEducation.deductionMethod === 'single' ? 1 : 0.5;
        totalDeduction += monthlyAmount * 12 * ratio;
      }
    }
    
    // 3岁以下婴幼儿照护: 每个子女每月2000元
    if (selectedDeductions.infantCare.selected) {
      const monthlyAmount = selectedDeductions.infantCare.count * selectedDeductions.infantCare.monthlyAmount;
      // 如果选择双方平分，则只计算一半
      const ratio = selectedDeductions.infantCare.deductionMethod === 'single' ? 1 : 0.5;
      totalDeduction += monthlyAmount * 12 * ratio;
    }
    
    // 继续教育
    if (selectedDeductions.continueEducation.selected) {
      // 如果是学历继续教育且选择父母扣除，则本人不再计算该项扣除
      if (!(selectedDeductions.continueEducation.type === 'degree' && selectedDeductions.continueEducation.deductionMethod === 'parent')) {
        if (selectedDeductions.continueEducation.type === 'degree') {
          // 学历继续教育: 每月400元
          totalDeduction += selectedDeductions.continueEducation.monthlyAmount * 12;
        } else {
          // 技能继续教育: 每年3600元
          totalDeduction += selectedDeductions.continueEducation.yearlyAmount;
        }
      }
    }
    
    // 住房贷款利息: 每月1000元
    if (selectedDeductions.housingLoan.selected) {
      // 如果选择双方平分，则只计算一半
      const ratio = selectedDeductions.housingLoan.deductionMethod === 'single' ? 1 : 0.5;
      totalDeduction += selectedDeductions.housingLoan.monthlyAmount * 12 * ratio;
    }
    
    // 住房租金: 根据城市等级
    if (selectedDeductions.housingRent) {
      switch (this.data.cityLevel) {
        case 'firstTier':
          totalDeduction += 1500 * 12;
          break;
        case 'secondTier':
          totalDeduction += 1100 * 12;
          break;
        case 'thirdTier':
          totalDeduction += 800 * 12;
          break;
      }
    }
    
    // 赡养老人: 独生子女3000元/月，非独生子女1500元/月
    if (selectedDeductions.elderlySupport.selected) {
      const monthlyAmount = selectedDeductions.elderlySupport.isOnlyChild ? 3000 : selectedDeductions.elderlySupport.monthlyAmount;
      totalDeduction += monthlyAmount * 12;
    }
    
    // 大病医疗: 个人自付超过15000元部分，每年最高80000元
    if (selectedDeductions.medicalFee.selected && selectedDeductions.medicalFee.deductionMethod === 'self') {
      totalDeduction += 80000;
    }
    
    this.setData({
      deductions: totalDeduction.toString(),
      specialDeductions: totalDeduction.toString() // 保存专项附加扣除金额用于结果显示
    });
  },
  
  // 监听专项附加扣除总额输入变化
  onDeductionsInput: function(e) {
    // 标记为手动输入
    this.setData({
      manualDeductionInput: true,
      deductions: e.detail.value,
      specialDeductions: e.detail.value // 保存专项附加扣除金额用于结果显示
    });
  },
  
  calculateTax: function() {
    // 获取输入值
    const income = parseFloat(this.data.income) || 100000;
    const deductions = parseFloat(this.data.deductions) || 0;
    const annualBonus = parseFloat(this.data.annualBonus) || 0;
    const bonusTaxMethod = this.data.bonusTaxMethod;
    const pensionAmount = parseFloat(this.data.pensionAmount) || 0; // 使用实际输入的个人养老金金额
    
    if (income <= 0) {
      wx.showToast({
        title: '请输入有效的年收入金额',
        icon: 'none'
      });
      return;
    }
    
    if (annualBonus > income) {
      wx.showToast({
        title: '奖金不能大于总收入',
        icon: 'none'
      });
      return;
    }
    
    // 基本减除费用
    const basicDeduction = 60000;
    
    // 计算应纳税所得额
    let taxableIncomeBefore, taxableIncomeAfter;
    let bonusTaxBefore = 0, bonusTaxAfter = 0;
    
    if (bonusTaxMethod === 'separate') {
      // 单独计税
      // 总收入减去奖金部分
      const regularIncome = income - annualBonus;
      
      // 计算普通收入的应纳税所得额
      taxableIncomeBefore = Math.max(0, regularIncome - basicDeduction - deductions);
      taxableIncomeAfter = Math.max(0, regularIncome - basicDeduction - deductions - pensionAmount);
      
      // 计算奖金税款
      bonusTaxBefore = this.calculateBonusTax(annualBonus);
      bonusTaxAfter = bonusTaxBefore; // 奖金单独计税不受个人养老金影响
    } else {
      // 并入综合所得
      taxableIncomeBefore = Math.max(0, income - basicDeduction - deductions);
      taxableIncomeAfter = Math.max(0, income - basicDeduction - deductions - pensionAmount);
    }
    
    // 计算税款
    const taxBefore = this.calculateIncomeTax(taxableIncomeBefore) + (bonusTaxMethod === 'separate' ? bonusTaxBefore : 0);
    const taxAfter = this.calculateIncomeTax(taxableIncomeAfter) + (bonusTaxMethod === 'separate' ? bonusTaxAfter : 0);
    const taxSaving = taxBefore - taxAfter;
    
    // 计算实际税率
    const effectiveTaxRate = Math.round((taxSaving / pensionAmount) * 100);
    
    // 计算考虑3%领取税后的净节省
    const withdrawalTax = pensionAmount * 0.03;
    const netSaving = taxSaving - withdrawalTax;
    
    // 格式化数字为整数
    const formatNumber = (num) => {
      return Math.round(num).toLocaleString('zh-CN');
    };
    
    this.setData({
      showResult: true,
      savingAmount: formatNumber(taxSaving),
      taxBefore: formatNumber(taxBefore),
      taxAfter: formatNumber(taxAfter),
      bonusTax: formatNumber(bonusTaxMethod === 'separate' ? bonusTaxBefore : 0),
      taxRate: effectiveTaxRate + '%',
      netSaving: formatNumber(netSaving),
      // 添加结果展示需要的数据
      netIncome: formatNumber(income),
      annualBonusDisplay: formatNumber(annualBonus),
      pensionAmountDisplay: formatNumber(pensionAmount) // 添加个人养老金金额显示
    });
    
    // 滚动到结果区域
    setTimeout(() => {
      wx.createSelectorQuery()
        .select('.result-card')
        .boundingClientRect(function(rect) {
          if (rect) {
            // 先获取当前页面滚动位置
            wx.createSelectorQuery()
              .selectViewport()
              .scrollOffset(function(res) {
                const currentScrollTop = res.scrollTop;
                // 计算结果卡片相对于页面顶部的绝对位置
                const absoluteTop = currentScrollTop + rect.top;
                // 滚动到结果卡片位置，减去一点偏移量让其显示在屏幕上方位置
                wx.pageScrollTo({
                  scrollTop: absoluteTop - 50,
                  duration: 300
                });
              })
              .exec();
          }
        })
        .exec();
    }, 300);
  },
  
  // 计算全年一次性奖金的税款
  calculateBonusTax: function(bonus) {
    if (bonus <= 0) return 0;
    
    // 计算月平均值
    const monthlyAverage = bonus / 12;
    
    // 使用月度税率表（全年一次性奖金单独计税适用）
    let taxRate = 0;
    let quickDeduction = 0;
    
    if (monthlyAverage <= 3000) {
      taxRate = 0.03;
      quickDeduction = 0;
    } else if (monthlyAverage <= 12000) {
      taxRate = 0.1;
      quickDeduction = 210;
    } else if (monthlyAverage <= 25000) {
      taxRate = 0.2;
      quickDeduction = 1410;
    } else if (monthlyAverage <= 35000) {
      taxRate = 0.25;
      quickDeduction = 2660;
    } else if (monthlyAverage <= 55000) {
      taxRate = 0.3;
      quickDeduction = 4410;
    } else if (monthlyAverage <= 80000) {
      taxRate = 0.35;
      quickDeduction = 7160;
    } else {
      taxRate = 0.45;
      quickDeduction = 15160;
    }
    
    // 计算税款
    return bonus * taxRate - quickDeduction;
  },
  
  // 个人所得税计算函数
  calculateIncomeTax: function(income) {
    if (income <= 0) return 0;
    
    let tax = 0;
    
    // 2023年个人所得税税率表（综合所得适用）
    if (income <= 36000) {
      tax = income * 0.03;
    } else if (income <= 144000) {
      tax = income * 0.1 - 2520;
    } else if (income <= 300000) {
      tax = income * 0.2 - 16920;
    } else if (income <= 420000) {
      tax = income * 0.25 - 31920;
    } else if (income <= 660000) {
      tax = income * 0.3 - 52920;
    } else if (income <= 960000) {
      tax = income * 0.35 - 85920;
    } else {
      tax = income * 0.45 - 181920;
    }
    
    return Math.max(0, tax);
  },

      // 转发给朋友
      onShareAppMessage() {
        return {
          title: '平台常用计算工具-养老金税费计算器',
          path: '/pages/pensionTaxCalculator/index'
        };
      },
    
      // 分享到朋友圈（需基础库 2.11.3+）
      onShareTimeline() {
        return {
          title: '平台常用计算工具-养老金税费计算器',
          query: 'from=timeline'
        };
      }
}); 