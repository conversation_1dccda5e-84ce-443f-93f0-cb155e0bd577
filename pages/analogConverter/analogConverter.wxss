.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f9fafc;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.title-underline {
  height: 6rpx;
  width: 60rpx;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 3rpx;
  margin: 0 auto;
}

/* 选项卡样式 */
.tab-container {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab.active {
  color: #3498db;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #3498db;
  border-radius: 2rpx;
}

/* 计算器部分样式 */
.calculator-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.input-group {
  margin-bottom: 25rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input-field {
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #333;
  border: 1rpx solid #e6e9ed;
  width: 100%;
  box-sizing: border-box;
}

.calculation-result {
  margin-top: 30rpx;
  background-color: #f0f8ff;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #3498db;
  position: relative;
}

.calculation-note {
  margin: 10rpx 0 20rpx;
  padding: 10rpx;
  background-color: #fffde7;
  border-radius: 6rpx;
  border-left: 4rpx solid #ffc107;
}

.note-text {
  font-size: 24rpx;
  color: #795548;
  line-height: 1.4;
}

.result-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.result-value {
  font-size: 40rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 10rpx;
}

/* 计算详情按钮和内容样式 */
.calc-details-btn {
  display: inline-block;
  font-size: 24rpx;
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  margin-top: 10rpx;
  cursor: pointer;
}

.calc-details-btn:active {
  opacity: 0.8;
}

.calc-details-content {
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #fff;
  border-radius: 8rpx;
  border: 1rpx dashed #ddd;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 双法兰液位计计算模式选择器 */
.calc-mode-selector {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  margin-bottom: 25rpx;
  overflow: hidden;
  border: 1rpx solid #e6e9ed;
}

.calc-mode {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.calc-mode.active {
  background-color: #3498db;
  color: #fff;
  font-weight: 500;
}

/* 安装方式选项卡样式 */
.installation-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 25rpx;
  overflow: hidden;
  border: 1rpx solid #e6e9ed;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.installation-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
  border-right: 1rpx solid #e6e9ed;
}

.installation-tab:last-child {
  border-right: none;
}

.installation-tab.active {
  background-color: #3498db;
  color: #fff;
  font-weight: 500;
}

/* 安装方式选择器样式 */
.installation-mode-selector {
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  margin-bottom: 25rpx;
  overflow: hidden;
  border: 1rpx solid #e6e9ed;
}

.installation-mode {
  text-align: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
  border-bottom: 1rpx solid #e6e9ed;
}

.installation-mode:last-child {
  border-bottom: none;
}

.installation-mode.active {
  background-color: #3498db;
  color: #fff;
  font-weight: 500;
}

/* 安装示意图样式 */
.installation-diagram {
  margin-bottom: 25rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 多列布局样式 */
.multi-column-layout {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.input-column, .result-column {
  flex: 1;
  min-width: 300rpx;
}

/* 紧凑型结果显示样式 */
.calculation-result.compact {
  margin-top: 10rpx;
  padding: 15rpx;
  margin-bottom: 15rpx;
}

.calculation-result.compact .result-label {
  font-size: 26rpx;
  margin-bottom: 5rpx;
}

.calculation-result.compact .result-value {
  font-size: 32rpx;
  margin-bottom: 5rpx;
}

/* 量程范围特殊样式，字体更小 */
.range-value {
  font-size: 26rpx !important;
  line-height: 1.2;
  height: 32rpx;
  display: flex;
  align-items: center;
}

/* 底部注释样式 */
.bottom-note {
  margin-top: 10rpx;
  margin-bottom: 20rpx;
}

/* 分隔线样式 */
.section-divider {
  display: flex;
  align-items: center;
  margin: 30rpx 0 20rpx;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background-color: #e0e0e0;
}

.divider-text {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
}

/* 图表内容容器 */
.installation-diagram-content {
  background-color: #fff;
  border-radius: 8rpx;
  border: 1rpx solid #e6e9ed;
  overflow: hidden;
  padding: 20rpx;
}

.diagram-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 15rpx;
  text-align: center;
}

.diagram-container {
  display: flex;
  flex-direction: column;
}

.diagram-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 15rpx;
}

.diagram-image {
  width: 100%;
  max-width: 500rpx;
  height: 400rpx;
}

.diagram-formula {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
  border: 1rpx dashed #ddd;
}

.diagram-formula text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

/* 安装描述样式（旧版保留） */
.installation-description {
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx dashed #ddd;
}

.description-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 15rpx;
  text-align: center;
}

.description-content {
  display: flex;
  flex-direction: column;
}

.description-content text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 8rpx;
  text-align: left;
}

/* 说明部分样式 */
.note-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.note-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

.note-content {
  display: flex;
  flex-direction: column;
}

.note-content text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
} 