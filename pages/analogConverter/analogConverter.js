Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'transmitter', // 默认激活变送器选项卡
    doubleFlangeMode: 'level', // 双法兰液位计默认模式：液位计算
    singleFlangeMode: 'level', // 单法兰液位计默认模式：液位计算
    installationMode: 'above', // 双法兰液位计安装方式：变送器在上
    showCalcDetails: false, // 是否显示计算详情
    
    // 变送器数据
    transmitter: {
      upperLimit: '', // 量程上限
      lowerLimit: '', // 量程下限
      currentValue: '', // 当前电流值
      result: '- -', // 计算结果
      calcDetails: '' // 计算详情
    },
    
    // 单法兰液位计数据
    singleFlange: {
      height: '', // 液位计高度
      density: '', // 介质密度
      currentValue: '', // 当前电流值
      result: '- -', // 计算结果
      calcDetails: '', // 计算详情
      
      // 量程计算模式数据
      range: {
        density: '', // 介质密度
        height: '', // 液位高度
        pressureRange: '- -', // 压力量程
        calcDetails: '' // 计算详情
      },
      
      // 压力转液位模式数据
      pressure: {
        pressureDiff: '', // 测量压力差
        result: '- -', // 计算结果 - 液位高度
      }
    },
    
    // 双法兰液位计数据
    doubleFlange: {
      // 液位计算模式数据
      distance: '', // 法兰间距
      density: '', // 介质密度
      currentValue: '', // 当前电流值
      result: '- -', // 计算结果
      calcDetails: '', // 计算详情
      
      // 量程计算模式数据
      range: {
        density: '', // 介质密度
        h0: '', // 法兰间距（量程高度）
        h1: '', // 高侧引压点到零液位距离
        h2: '', // 低侧引压点到零液位距离
        result: '- -', // 计算结果 - 压力量程
        highLevelPressure: '- -', // 高液位处液柱压力
        lowLevelPressure: '- -', // 低液位处液柱压力
        emptyPressure: '- -', // 空液位时压力差
        fullPressure: '- -', // 满液位时压力差
        actualRange: '- -', // 实际量程范围
        calcDetails: '' // 计算详情
      },
      
      // 压力转液位模式数据
      pressure: {
        density: '', // 介质密度
        distance: '', // 法兰间距
        pressureDiff: '', // 测量压力差
        result: '- -', // 计算结果 - 液位高度
        calcDetails: '' // 计算详情
      }
    }
  },

  /**
   * 切换选项卡
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },
  
  /**
   * 切换双法兰液位计计算模式
   */
  switchDoubleFlangeMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      doubleFlangeMode: mode
    });
  },
  
  /**
   * 切换双法兰液位计安装方式
   */
  switchInstallationMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      installationMode: mode
    });
    
    // 切换安装方式后重新计算
    this.calculateDoubleFlangeRange();
  },
  
  /**
   * 切换单法兰液位计计算模式
   */
  switchSingleFlangeMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      singleFlangeMode: mode
    });
  },
  
  /**
   * 显示/隐藏计算详情
   */
  toggleCalcDetails() {
    this.setData({
      showCalcDetails: !this.data.showCalcDetails
    });
  },

  /**
   * 处理输入值变化
   */
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    let value = e.detail.value;
    
    // 处理输入值，限制只能输入数字和一个小数点，最多一位小数
    value = this.formatInputValue(value);
    
    // 使用解构的方式更新对应字段
    const fieldParts = field.split('.');
    const category = fieldParts[0]; // 变送器/单法兰/双法兰
    
    // 更新数据
    this.setData({
      [field]: value
    });
    
    // 进行相应计算
    if (category === 'transmitter') {
      this.calculateTransmitter();
    } else if (category === 'singleFlange') {
      // 根据单法兰子类型进行计算
      const subCategory = fieldParts[1];
      if (subCategory === 'range') {
        this.calculateSingleFlangeRange();
      } else if (subCategory === 'pressure') {
        this.calculateSingleFlangeByPressure();
      } else {
        this.calculateSingleFlange();
      }
    } else if (category === 'doubleFlange') {
      // 根据双法兰子类型进行计算
      const subCategory = fieldParts[1];
      if (subCategory === 'range') {
        this.calculateDoubleFlangeRange();
      } else if (subCategory === 'pressure') {
        // 使用量程计算中的数据进行压力转液位计算
        this.calculateDoubleFlangeByPressure();
      } else {
        this.calculateDoubleFlange();
      }
    }
  },

  /**
   * 格式化输入值，限制只能输入数字和一个小数点，最多两位小数
   */
  formatInputValue(value) {
    if (!value) return value;
    
    // 允许负号，负号只能在开头
    value = value.replace(/[^\d.-]/g, '');
    // 只保留第一个负号且只能在开头
    value = value.replace(/(?!^)-/g, '');
    if (value.indexOf('-') > 0) {
      value = value.replace(/-/g, '');
    }
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    // 限制小数点后最多两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    return value;
  },

  /**
   * 执行计算
   */
  calculate(category) {
    if (category === 'transmitter') {
      this.calculateTransmitter();
    } else if (category === 'singleFlange') {
      // 根据当前模式进行相应计算
      if (this.data.singleFlangeMode === 'level') {
        this.calculateSingleFlange();
      } else if (this.data.singleFlangeMode === 'range') {
        this.calculateSingleFlangeRange();
      }
    } else if (category === 'doubleFlange') {
      // 根据当前模式进行相应计算
      if (this.data.doubleFlangeMode === 'level') {
        this.calculateDoubleFlange();
      } else if (this.data.doubleFlangeMode === 'range') {
        this.calculateDoubleFlangeRange();
      }
    }
  },

  /**
   * 计算变送器输出值
   * 公式：测量值 = 量程下限 + (测量电流 - 4) × (量程上限 - 量程下限) ÷ 16
   */
  calculateTransmitter() {
    const { upperLimit, lowerLimit, currentValue } = this.data.transmitter;
    
    // 检查是否所有必要数据都已输入
    if (!upperLimit || !lowerLimit || !currentValue) {
      this.setData({
        'transmitter.result': '- -',
        'transmitter.calcDetails': ''
      });
      return;
    }
    
    // 转换为数字进行计算
    const upper = parseFloat(upperLimit);
    const lower = parseFloat(lowerLimit);
    const current = parseFloat(currentValue);
    
    // 验证输入值
    if (isNaN(upper) || isNaN(lower) || isNaN(current)) {
      this.setData({
        'transmitter.result': '输入有误',
        'transmitter.calcDetails': ''
      });
      return;
    }
    
    if (current < 4 || current > 20) {
      this.setData({
        'transmitter.result': '电流值应在4-20mA范围内',
        'transmitter.calcDetails': ''
      });
      return;
    }
    
    // 执行计算
    const result = lower + (current - 4) * (upper - lower) / 16;
    
    // 计算详情
    const calcDetails = `计算过程：\n` +
      `测量值 = 量程下限 + (测量电流 - 4) × (量程上限 - 量程下限) ÷ 16\n` +
      `= ${lower} + (${current} - 4) × (${upper} - ${lower}) ÷ 16\n` +
      `= ${lower} + ${current - 4} × ${upper - lower} ÷ 16\n` +
      `= ${lower} + ${(current - 4) * (upper - lower) / 16}\n` +
      `= ${result.toFixed(2)}`;
    
    // 更新结果，保留两位小数
    this.setData({
      'transmitter.result': result.toFixed(2),
      'transmitter.calcDetails': calcDetails
    });
  },

  /**
   * 计算单法兰液位计液位高度
   */
  calculateSingleFlange() {
    const { height, density, currentValue } = this.data.singleFlange;
    
    // 检查是否所有必要数据都已输入
    if (!height || !density || !currentValue) {
      this.setData({
        'singleFlange.result': '- -',
        'singleFlange.calcDetails': ''
      });
      return;
    }
    
    // 转换为数字进行计算
    const h = parseFloat(height);
    const d = parseFloat(density);
    const current = parseFloat(currentValue);
    
    // 验证输入值
    if (isNaN(h) || isNaN(d) || isNaN(current)) {
      this.setData({
        'singleFlange.result': '输入有误',
        'singleFlange.calcDetails': ''
      });
      return;
    }
    
    if (current < 4 || current > 20) {
      this.setData({
        'singleFlange.result': '电流值应在4-20mA范围内',
        'singleFlange.calcDetails': ''
      });
      return;
    }
    
    // 执行计算 - 单法兰液位计计算公式
    // 液位高度 = 测量电流在量程内的百分比 × 液位计高度
    const percentage = (current - 4) / 16;
    const result = percentage * h;
    
    // 计算详情
    const calcDetails = `计算过程：\n` +
      `百分比 = (测量电流 - 4) ÷ 16\n` +
      `= (${current} - 4) ÷ 16\n` +
      `= ${percentage.toFixed(4)}\n\n` +
      `液位高度 = 百分比 × 液位计高度\n` +
      `= ${percentage.toFixed(4)} × ${h.toFixed(3)}m\n` +
      `= ${result.toFixed(3)} m`;
    
    // 更新结果，保留三位小数
    this.setData({
      'singleFlange.result': result.toFixed(3),
      'singleFlange.calcDetails': calcDetails
    });
  },

  /**
   * 计算双法兰液位计液位高度 - 液位计算模式
   */
  calculateDoubleFlange() {
    const { distance, density, currentValue } = this.data.doubleFlange;
    
    // 检查是否所有必要数据都已输入
    if (!distance || !density || !currentValue) {
      this.setData({
        'doubleFlange.result': '- -',
        'doubleFlange.calcDetails': ''
      });
      return;
    }
    
    // 转换为数字进行计算
    const dist = parseFloat(distance);
    const d = parseFloat(density);
    const current = parseFloat(currentValue);
    
    // 验证输入值
    if (isNaN(dist) || isNaN(d) || isNaN(current)) {
      this.setData({
        'doubleFlange.result': '输入有误',
        'doubleFlange.calcDetails': ''
      });
      return;
    }
    
    if (current < 4 || current > 20) {
      this.setData({
        'doubleFlange.result': '电流值应在4-20mA范围内',
        'doubleFlange.calcDetails': ''
      });
      return;
    }
    
    // 执行计算 - 双法兰液位计计算公式
    // 液位高度 = 测量电流在量程内的百分比 × 法兰间距
    const percentage = (current - 4) / 16;
    const result = percentage * dist;
    
    // 计算详情
    const calcDetails = `计算过程：\n` +
      `百分比 = (测量电流 - 4) ÷ 16\n` +
      `= (${current} - 4) ÷ 16\n` +
      `= ${percentage.toFixed(4)}\n\n` +
      `液位高度 = 百分比 × 法兰间距\n` +
      `= ${percentage.toFixed(4)} × ${dist.toFixed(3)}m\n` +
      `= ${result.toFixed(3)} m`;
    
    // 更新结果，保留三位小数
    this.setData({
      'doubleFlange.result': result.toFixed(3),
      'doubleFlange.calcDetails': calcDetails
    });
  },
  
  /**
   * 计算双法兰液位计量程 - 量程计算模式
   * 根据不同安装方式有不同的计算公式
   */
  calculateDoubleFlangeRange() {
    const { density, h0, h1, h2 } = this.data.doubleFlange.range;
    const mode = this.data.installationMode;
    
    // 检查是否所有必要数据都已输入
    if (!density || !h0 || !h1 || !h2) {
      this.setData({
        'doubleFlange.range.result': '- -',
        'doubleFlange.range.highLevelPressure': '- -',
        'doubleFlange.range.lowLevelPressure': '- -',
        'doubleFlange.range.emptyPressure': '- -',
        'doubleFlange.range.fullPressure': '- -',
        'doubleFlange.range.actualRange': '- -',
        'doubleFlange.range.calcDetails': ''
      });
      return;
    }
    
    // 转换为数字进行计算
    const d = parseFloat(density);
    const h0Value = parseFloat(h0);
    const h1Value = parseFloat(h1);
    const h2Value = parseFloat(h2);
    
    // 硅油密度默认为0.93 kg/m³
    const oilDensity = 0.93;
    
    // 验证输入值
    if (isNaN(d) || isNaN(h0Value) || isNaN(h1Value) || isNaN(h2Value)) {
      this.setData({
        'doubleFlange.range.result': '输入有误',
        'doubleFlange.range.highLevelPressure': '- -',
        'doubleFlange.range.lowLevelPressure': '- -',
        'doubleFlange.range.emptyPressure': '- -',
        'doubleFlange.range.fullPressure': '- -',
        'doubleFlange.range.actualRange': '- -',
        'doubleFlange.range.calcDetails': ''
      });
      return;
    }
    
    // 重力加速度
    const g = 9.8;
    
    // 基本压力量程计算
    const pressureRange = d * g * h0Value;
    
    let highLevelPressure, lowLevelPressure, emptyPressure, fullPressure, actualRange;
    let calcDetails = '';
    
    // 根据不同的安装方式进行计算
    if (mode === 'above') { 
      // 1. 变送器在上、法兰在下
      // 高侧液柱压力 (使用负号)
      highLevelPressure = oilDensity * g * (-h1Value);
      // 低侧液柱压力 (使用负号)
      lowLevelPressure = oilDensity * g * (-h2Value);
      // 空液位时压力差
      emptyPressure = highLevelPressure - lowLevelPressure;
      // 满液位时压力差
      fullPressure = emptyPressure + pressureRange;
      // 实际量程
      actualRange = Math.abs(emptyPressure - fullPressure);
      
      // 计算详情
      calcDetails = `计算过程：\n` +
        `量程：ΔP = ρ × g × H0 = ${d} × ${g} × ${h0Value.toFixed(3)} = ${pressureRange.toFixed(2)}kPa\n\n` +
        `高侧液柱压力：P(+) = ρ0 × g × (-H1) = ${oilDensity} × ${g} × ${-h1Value.toFixed(3)} = ${highLevelPressure.toFixed(2)}kPa\n\n` +
        `低侧液柱压力：P(-) = ρ0 × g × (-H2) = ${oilDensity} × ${g} × ${-h2Value.toFixed(3)} = ${lowLevelPressure.toFixed(2)}kPa\n\n` +
        `空液位时压力差：ΔP = P(+) - P(-) = ${highLevelPressure.toFixed(2)} - ${lowLevelPressure.toFixed(2)} = ${emptyPressure.toFixed(2)}kPa\n\n` +
        `满液位时压力差：ΔP = 空液位压力差 + 量程 = ${emptyPressure.toFixed(2)} + ${pressureRange.toFixed(2)} = ${fullPressure.toFixed(2)}kPa\n\n` +
        `实际量程范围：${emptyPressure.toFixed(2)}至${fullPressure.toFixed(2)}kPa`;
    } 
    else if (mode === 'middle') { 
      // 2. 变送器在上下法兰之间
      // 高侧液柱压力
      highLevelPressure = oilDensity * g * (-h1Value);
      // 低侧液柱压力
      lowLevelPressure = oilDensity * g * h2Value;
      // 空液位时压力差
      emptyPressure = highLevelPressure - lowLevelPressure;
      // 满液位时压力差
      fullPressure = emptyPressure + pressureRange;
      // 实际量程
      actualRange = Math.abs(emptyPressure - fullPressure);
      
      // 计算详情
      calcDetails = `计算过程：\n` +
        `量程：ΔP = ρ × g × H0 = ${d} × ${g} × ${h0Value.toFixed(3)} = ${pressureRange.toFixed(2)}kPa\n\n` +
        `高侧液柱压力：P(+) = ρ0 × g × (-H1) = ${oilDensity} × ${g} × ${-h1Value.toFixed(3)} = ${highLevelPressure.toFixed(2)}kPa\n\n` +
        `低侧液柱压力：P(-) = ρ0 × g × H2 = ${oilDensity} × ${g} × ${h2Value.toFixed(3)} = ${lowLevelPressure.toFixed(2)}kPa\n\n` +
        `空液位时压力差：ΔP = P(+) - P(-) = ${highLevelPressure.toFixed(2)} - ${lowLevelPressure.toFixed(2)} = ${emptyPressure.toFixed(2)}kPa\n\n` +
        `满液位时压力差：ΔP = 空液位压力差 + 量程 = ${emptyPressure.toFixed(2)} + ${pressureRange.toFixed(2)} = ${fullPressure.toFixed(2)}kPa\n\n` +
        `实际量程范围：${emptyPressure.toFixed(2)}至${fullPressure.toFixed(2)}kPa`;
    } 
    else if (mode === 'below') { 
      // 3. 变送器在下、法兰在上
      // 高侧液柱压力 (不再使用负号)
      highLevelPressure = oilDensity * g * h1Value;
      // 低侧液柱压力 (不再使用负号)
      lowLevelPressure = oilDensity * g * h2Value;
      // 空液位时压力差
      emptyPressure = highLevelPressure - lowLevelPressure;
      // 满液位时压力差
      fullPressure = emptyPressure + pressureRange;
      // 实际量程
      actualRange = Math.abs(emptyPressure - fullPressure);
      
      // 计算详情
      calcDetails = `计算过程：\n` +
        `量程：ΔP = ρ × g × H0 = ${d} × ${g} × ${h0Value.toFixed(3)} = ${pressureRange.toFixed(2)}kPa\n\n` +
        `高侧液柱压力：P(+) = ρ0 × g × H1 = ${oilDensity} × ${g} × ${h1Value.toFixed(3)} = ${highLevelPressure.toFixed(2)}kPa\n\n` +
        `低侧液柱压力：P(-) = ρ0 × g × H2 = ${oilDensity} × ${g} × ${h2Value.toFixed(3)} = ${lowLevelPressure.toFixed(2)}kPa\n\n` +
        `空液位时压力差：ΔP = P(+) - P(-) = ${highLevelPressure.toFixed(2)} - ${lowLevelPressure.toFixed(2)} = ${emptyPressure.toFixed(2)}kPa\n\n` +
        `满液位时压力差：ΔP = 空液位压力差 + 量程 = ${emptyPressure.toFixed(2)} + ${pressureRange.toFixed(2)} = ${fullPressure.toFixed(2)}kPa\n\n` +
        `实际量程范围：${emptyPressure.toFixed(2)}至${fullPressure.toFixed(2)}kPa`;
    }
    
    // 更新结果，保留两位小数
    this.setData({
      'doubleFlange.range.result': pressureRange.toFixed(2),
      'doubleFlange.range.highLevelPressure': highLevelPressure.toFixed(2),
      'doubleFlange.range.lowLevelPressure': lowLevelPressure.toFixed(2),
      'doubleFlange.range.emptyPressure': emptyPressure.toFixed(2),
      'doubleFlange.range.fullPressure': fullPressure.toFixed(2),
      'doubleFlange.range.actualRange': `${emptyPressure.toFixed(2)}至${fullPressure.toFixed(2)}`,
      'doubleFlange.range.calcDetails': calcDetails
    });
  },
  
  /**
   * 根据压力差计算双法兰液位计的液位高度
   */
  calculateDoubleFlangeByPressure() {
    // 使用量程计算中的数据
    const density = parseFloat(this.data.doubleFlange.range.density || 0);
    const h0Value = parseFloat(this.data.doubleFlange.range.h0 || 0);
    const pressureDiff = this.data.doubleFlange.pressure.pressureDiff;
    
    // 获取计算出的压力范围值
    const emptyPressure = parseFloat(this.data.doubleFlange.range.emptyPressure || 0);
    const fullPressure = parseFloat(this.data.doubleFlange.range.fullPressure || 0);
    
    // 检查量程范围是否已计算
    if (this.data.doubleFlange.range.actualRange === '- -') {
      this.setData({
        'doubleFlange.pressure.result': '请先计算量程范围'
      });
      return;
    }
    
    // 检查是否所有必要数据都已输入和计算
    if (!density || !h0Value || pressureDiff === '' || !emptyPressure || !fullPressure) {
      this.setData({
        'doubleFlange.pressure.result': '- -'
      });
      return;
    }
    
    // 转换为数值
    const pressureDiffValue = parseFloat(pressureDiff);
    
    // 验证输入值
    if (isNaN(density) || isNaN(h0Value) || isNaN(pressureDiffValue) || 
        isNaN(emptyPressure) || isNaN(fullPressure)) {
      this.setData({
        'doubleFlange.pressure.result': '输入有误'
      });
      return;
    }
    
    // 检查是否超出量程范围
    const minPressure = Math.min(emptyPressure, fullPressure);
    const maxPressure = Math.max(emptyPressure, fullPressure);
    
    if (pressureDiffValue < minPressure || pressureDiffValue > maxPressure) {
      this.setData({
        'doubleFlange.pressure.result': '超出量程范围'
      });
      return;
    }
    
    // 执行计算：使用线性比例关系计算出液位高度
    // 液位百分比 = (当前压力差 - 空液位压力差) / (满液位压力差 - 空液位压力差)
    // 液位高度 = 液位百分比 × 量程高度
    
    const pressureRange = fullPressure - emptyPressure;
    const percentage = (pressureDiffValue - emptyPressure) / pressureRange;
    const level = percentage * h0Value;
    
    // 限制结果在0-H0范围内
    const finalLevel = Math.max(0, Math.min(level, h0Value));
    
    // 更新结果，保留三位小数
    this.setData({
      'doubleFlange.pressure.result': finalLevel.toFixed(3)
    });
  },

  /**
   * 计算单法兰液位计的压力量程
   * 公式：P = ρ × g × h，其中P为压力，ρ为密度，g为重力加速度，h为液位高度
   */
  calculateSingleFlangeRange() {
    const density = parseFloat(this.data.singleFlange.range.density || 0);
    const height = parseFloat(this.data.singleFlange.range.height || 0);
    
    // 检查是否所有必要数据都已输入
    if (!density || !height) {
      this.setData({
        'singleFlange.range.pressureRange': '- -',
        'singleFlange.range.calcDetails': ''
      });
      return;
    }
    
    // 验证输入值
    if (isNaN(density) || isNaN(height)) {
      this.setData({
        'singleFlange.range.pressureRange': '输入有误',
        'singleFlange.range.calcDetails': ''
      });
      return;
    }
    
    // 重力加速度
    const g = 9.8;
    
    // 执行计算 - 压力量程计算公式：P = ρ × g × h
    const pressureRange = density * g * height;
    
    // 计算详情
    const calcDetails = `计算过程：\n` +
      `量程：ΔP = ρ × g × h = ${density} × ${g} × ${height.toFixed(3)} = ${pressureRange.toFixed(2)}kPa\n\n` +
      `其中：\n` +
      `ρ 为介质密度 (${density} kg/m³)\n` +
      `g 为重力加速度 (9.8 m/s²)\n` +
      `h 为液位高度 (${height.toFixed(3)} m)`;
    
    // 更新结果，保留两位小数
    this.setData({
      'singleFlange.range.pressureRange': pressureRange.toFixed(2),
      'singleFlange.range.calcDetails': calcDetails
    });
  },
  
  /**
   * 根据压力差计算单法兰液位计的液位高度
   */
  calculateSingleFlangeByPressure() {
    // 使用量程计算中的密度
    const density = parseFloat(this.data.singleFlange.range.density || 0);
    const pressureDiff = this.data.singleFlange.pressure.pressureDiff;
    
    // 检查量程是否已计算
    if (this.data.singleFlange.range.pressureRange === '- -') {
      this.setData({
        'singleFlange.pressure.result': '请先计算量程范围'
      });
      return;
    }
    
    // 检查是否所有必要数据都已输入
    if (!density || pressureDiff === '') {
      this.setData({
        'singleFlange.pressure.result': '- -'
      });
      return;
    }
    
    // 转换为数值
    const pressureDiffValue = parseFloat(pressureDiff);
    
    // 验证输入值
    if (isNaN(density) || isNaN(pressureDiffValue)) {
      this.setData({
        'singleFlange.pressure.result': '输入有误'
      });
      return;
    }
    
    // 获取计算出的压力量程
    const pressureRange = parseFloat(this.data.singleFlange.range.pressureRange);
    
    // 检查是否超出量程范围
    if (pressureDiffValue < 0 || pressureDiffValue > pressureRange) {
      this.setData({
        'singleFlange.pressure.result': '超出量程范围'
      });
      return;
    }
    
    // 重力加速度
    const g = 9.8;
    
    // 执行计算：h = P / (ρ × g)
    const level = pressureDiffValue / (density * g);
    
    // 更新结果，保留三位小数
    this.setData({
      'singleFlange.pressure.result': level.toFixed(3)
    });
  },

  /**
   * 转发给朋友
   */
  onShareAppMessage() {
    const titles = {
      'transmitter': '4-20mA模拟量转换-变送器计算工具',
      'singleFlange': '4-20mA模拟量转换-单法兰液位计计算工具',
      'doubleFlange': '4-20mA模拟量转换-双法兰液位计计算工具'
    };
    
    return {
      title: titles[this.data.activeTab],
      path: '/pages/analogConverter/analogConverter'
    };
  },

  /**
   * 分享到朋友圈（需基础库 2.11.3+）
   */
  onShareTimeline() {
    const titles = {
      'transmitter': '4-20mA模拟量转换-变送器计算工具',
      'singleFlange': '4-20mA模拟量转换-单法兰液位计计算工具',
      'doubleFlange': '4-20mA模拟量转换-双法兰液位计计算工具'
    };
    
    return {
      title: titles[this.data.activeTab],
      query: 'tab=' + this.data.activeTab
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  }
}) 