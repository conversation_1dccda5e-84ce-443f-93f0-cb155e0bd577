<view class="container">

  <view class="tab-container">
    <view class="tab {{activeTab == 'transmitter' ? 'active' : ''}}" bindtap="switchTab" data-tab="transmitter">变送器</view>
    <view class="tab {{activeTab == 'singleFlange' ? 'active' : ''}}" bindtap="switchTab" data-tab="singleFlange">单法兰液位计</view>
    <view class="tab {{activeTab == 'doubleFlange' ? 'active' : ''}}" bindtap="switchTab" data-tab="doubleFlange">双法兰液位计</view>
  </view>

  <!-- 变送器计算器 -->
  <view class="calculator-section" wx:if="{{activeTab == 'transmitter'}}">
    <view class="multi-column-layout">
      <view class="input-column">
        <view class="input-group">
          <view class="input-label">量程上限 (对应20mA):</view>
          <input type="digit" class="input-field" placeholder="请输入量程上限值" bindinput="onInputChange" data-field="transmitter.upperLimit" value="{{transmitter.upperLimit}}"/>
        </view>
      </view>

      <view class="input-column">
        <view class="input-group">
          <view class="input-label">量程下限 (对应4mA):</view>
          <input type="digit" class="input-field" placeholder="请输入量程下限值" bindinput="onInputChange" data-field="transmitter.lowerLimit" value="{{transmitter.lowerLimit}}"/>
        </view>
      </view>
    </view>
    
    <view class="input-group">
      <view class="input-label">测量电流 (mA):</view>
      <input type="digit" class="input-field" placeholder="请输入测量电流值" bindinput="onInputChange" data-field="transmitter.currentValue" value="{{transmitter.currentValue}}"/>
    </view>

    <view class="calculation-result">
      <view class="result-label">计算结果:</view>
      <view class="result-value">{{transmitter.result}}</view>
      <view class="calc-details-btn" bindtap="toggleCalcDetails">计算详情</view>
      <view class="calc-details-content" wx:if="{{showCalcDetails}}">{{transmitter.calcDetails}}</view>
    </view>
  </view>

  <!-- 单法兰液位计计算器 -->
  <view class="calculator-section" wx:if="{{activeTab == 'singleFlange'}}">
    <!-- 计算模式选择 -->
    <view class="calc-mode-selector">
      <view class="calc-mode {{singleFlangeMode === 'level' ? 'active' : ''}}" bindtap="switchSingleFlangeMode" data-mode="level">液位计算</view>
      <view class="calc-mode {{singleFlangeMode === 'range' ? 'active' : ''}}" bindtap="switchSingleFlangeMode" data-mode="range">量程计算</view>
    </view>

    <!-- 液位计算模式 -->
    <view wx:if="{{singleFlangeMode === 'level'}}">
      <view class="multi-column-layout">
        <view class="input-column">
          <view class="input-group">
            <view class="input-label">液位计高度 (m):</view>
            <input type="digit" class="input-field" placeholder="请输入液位计高度" bindinput="onInputChange" data-field="singleFlange.height" value="{{singleFlange.height}}"/>
          </view>
        </view>

        <view class="input-column">
          <view class="input-group">
            <view class="input-label">介质密度 (kg/m³):</view>
            <input type="digit" class="input-field" placeholder="请输入介质密度" bindinput="onInputChange" data-field="singleFlange.density" value="{{singleFlange.density}}"/>
          </view>
        </view>
      </view>

      <view class="input-group">
        <view class="input-label">测量电流 (mA):</view>
        <input type="digit" class="input-field" placeholder="请输入测量电流值" bindinput="onInputChange" data-field="singleFlange.currentValue" value="{{singleFlange.currentValue}}"/>
      </view>

      <view class="calculation-result">
        <view class="result-label">液位高度:</view>
        <view class="result-value">{{singleFlange.result}} m</view>
        <view class="calc-details-btn" bindtap="toggleCalcDetails">计算详情</view>
        <view class="calc-details-content" wx:if="{{showCalcDetails}}">{{singleFlange.calcDetails}}</view>
      </view>
    </view>

    <!-- 量程计算模式 -->
    <view wx:if="{{singleFlangeMode === 'range'}}">
      <!-- 安装示意图 -->
      <view class="installation-diagram">
        <view class="installation-diagram-content">
          <view class="diagram-title">【单法兰液位计安装图】</view>
          <view class="diagram-container">
            <view class="diagram-image-container">
              <image class="diagram-image" src="/images/single_flange_diagram.jpg" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 输入区域 -->
      <view class="multi-column-layout">
        <view class="input-column">
          <view class="input-group">
            <view class="input-label">介质密度 (kg/m³):</view>
            <input type="digit" class="input-field" placeholder="请输入介质密度" bindinput="onInputChange" data-field="singleFlange.range.density" value="{{singleFlange.range.density}}"/>
          </view>
        </view>

        <view class="input-column">
          <view class="input-group">
            <view class="input-label">液位高度 (m):</view>
            <input type="digit" class="input-field" placeholder="请输入液位高度" bindinput="onInputChange" data-field="singleFlange.range.height" value="{{singleFlange.range.height}}"/>
          </view>
        </view>
      </view>

      <!-- 结果显示区域 -->
      <view class="calculation-result">
        <view class="result-label">压力量程:</view>
        <view class="result-value">{{singleFlange.range.pressureRange}} kPa</view>
        <view class="calc-details-btn" bindtap="toggleCalcDetails">计算详情</view>
        <view class="calc-details-content" wx:if="{{showCalcDetails}}">{{singleFlange.range.calcDetails}}</view>
      </view>
      
      <!-- 压力转液位计算部分 -->
      <view class="section-divider">
        <view class="divider-line"></view>
        <view class="divider-text">压力转液位计算</view>
        <view class="divider-line"></view>
      </view>
      
      <view class="input-group">
        <view class="input-label">测量压力差 (kPa):</view>
        <input type="text" class="input-field" placeholder="请输入测量压力差（可输入负数）" bindinput="onInputChange" data-field="singleFlange.pressure.pressureDiff" value="{{singleFlange.pressure.pressureDiff}}"/>
      </view>

      <view class="calculation-result compact">
        <view class="result-label">液位高度:</view>
        <view class="result-value">
          <block wx:if="{{singleFlange.pressure.result == '请先计算量程范围' || singleFlange.pressure.result == '输入有误' || singleFlange.pressure.result == '超出量程范围' || singleFlange.pressure.result == '- -'}}">
            {{singleFlange.pressure.result}}
          </block>
          <block wx:else>
            {{singleFlange.pressure.result}} m
          </block>
        </view>
      </view>
      
      <view class="calculation-note bottom-note">
        <text class="note-text">注：根据计算公式 h = P / (ρ × g) 进行液位计算，其中P为压力，ρ为密度，g为重力加速度(9.8)</text>
      </view>
    </view>
  </view>

  <!-- 双法兰液位计计算器 -->
  <view class="calculator-section" wx:if="{{activeTab == 'doubleFlange'}}">
    <!-- 计算模式选择 -->
    <view class="calc-mode-selector">
      <view class="calc-mode {{doubleFlangeMode === 'level' ? 'active' : ''}}" bindtap="switchDoubleFlangeMode" data-mode="level">液位计算</view>
      <view class="calc-mode {{doubleFlangeMode === 'range' ? 'active' : ''}}" bindtap="switchDoubleFlangeMode" data-mode="range">量程计算</view>
    </view>

    <!-- 液位计算模式 -->
    <view wx:if="{{doubleFlangeMode === 'level'}}">
      <view class="multi-column-layout">
        <view class="input-column">
          <view class="input-group">
            <view class="input-label">法兰间距 (m):</view>
            <input type="digit" class="input-field" placeholder="请输入法兰间距" bindinput="onInputChange" data-field="doubleFlange.distance" value="{{doubleFlange.distance}}"/>
          </view>
        </view>
        
        <view class="input-column">
          <view class="input-group">
            <view class="input-label">介质密度 (kg/m³):</view>
            <input type="digit" class="input-field" placeholder="请输入介质密度" bindinput="onInputChange" data-field="doubleFlange.density" value="{{doubleFlange.density}}"/>
          </view>
        </view>
      </view>

      <view class="input-group">
        <view class="input-label">测量电流 (mA):</view>
        <input type="digit" class="input-field" placeholder="请输入测量电流值" bindinput="onInputChange" data-field="doubleFlange.currentValue" value="{{doubleFlange.currentValue}}"/>
      </view>

      <view class="calculation-result">
        <view class="result-label">液位高度:</view>
        <view class="result-value">{{doubleFlange.result}} m</view>
        <view class="calc-details-btn" bindtap="toggleCalcDetails">计算详情</view>
        <view class="calc-details-content" wx:if="{{showCalcDetails}}">{{doubleFlange.calcDetails}}</view>
      </view>
    </view>

    <!-- 量程计算模式 -->
    <view wx:if="{{doubleFlangeMode === 'range'}}">
      <!-- 安装方式选择 -->
      <view class="installation-tabs">
        <view class="installation-tab {{installationMode === 'above' ? 'active' : ''}}" bindtap="switchInstallationMode" data-mode="above">变送器在上</view>
        <view class="installation-tab {{installationMode === 'middle' ? 'active' : ''}}" bindtap="switchInstallationMode" data-mode="middle">变送器在中间</view>
        <view class="installation-tab {{installationMode === 'below' ? 'active' : ''}}" bindtap="switchInstallationMode" data-mode="below">变送器在下</view>
      </view>

      <!-- 安装图示 -->
      <view class="installation-diagram">
        <!-- 变送器在上方安装图 -->
        <view wx:if="{{installationMode === 'above'}}">
          <view class="installation-diagram-content">
            <view class="diagram-title">【变送器在上方安装图】</view>
            <view class="diagram-container">
              <view class="diagram-image-container">
                <image class="diagram-image" src="/images/above_diagram.jpg" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>

        <!-- 变送器在中间安装图 -->
        <view wx:if="{{installationMode === 'middle'}}">
          <view class="installation-diagram-content">
            <view class="diagram-title">【变送器在中间安装图】</view>
            <view class="diagram-container">
              <view class="diagram-image-container">
                <image class="diagram-image" src="/images/middle_diagram.jpg" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>

        <!-- 变送器在下方安装图 -->
        <view wx:if="{{installationMode === 'below'}}">
          <view class="installation-diagram-content">
            <view class="diagram-title">【变送器在下方安装图】</view>
            <view class="diagram-container">
              <view class="diagram-image-container">
                <image class="diagram-image" src="/images/below_diagram.jpg" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 输入区域优化为两列布局 -->
      <view class="multi-column-layout">
        <view class="input-column">
          <view class="input-group">
            <view class="input-label">介质密度 (kg/m³):</view>
            <input type="digit" class="input-field" placeholder="请输入介质密度" bindinput="onInputChange" data-field="doubleFlange.range.density" value="{{doubleFlange.range.density}}"/>
          </view>

          <view class="input-group">
            <view class="input-label">H0值 (m):</view>
            <input type="digit" class="input-field" placeholder="法兰间距/量程高度" bindinput="onInputChange" data-field="doubleFlange.range.h0" value="{{doubleFlange.range.h0}}"/>
          </view>
        </view>

        <view class="input-column">
          <view class="input-group">
            <view class="input-label">H1值 (m):</view>
            <input type="digit" class="input-field" placeholder="低侧引压点到零液位距离" bindinput="onInputChange" data-field="doubleFlange.range.h1" value="{{doubleFlange.range.h1}}"/>
          </view>

          <view class="input-group">
            <view class="input-label">H2值 (m):</view>
            <input type="digit" class="input-field" placeholder="高侧引压点到零液位距离" bindinput="onInputChange" data-field="doubleFlange.range.h2" value="{{doubleFlange.range.h2}}"/>
          </view>
        </view>
      </view>

      <view class="calculation-note">
        <text class="note-text">注：计算中ρ0(硅油密度)默认值为0.93 kg/m³</text>
      </view>

      <!-- 结果显示区域改为两列布局 -->
      <view class="multi-column-layout">
        <view class="result-column">
          <view class="calculation-result compact">
            <view class="result-label">压力量程:</view>
            <view class="result-value">{{doubleFlange.range.result}} kPa</view>
          </view>

          <view class="calculation-result compact">
            <view class="result-label">高液位处液柱压力:</view>
            <view class="result-value">{{doubleFlange.range.highLevelPressure}} kPa</view>
          </view>

          <view class="calculation-result compact">
            <view class="result-label">低液位处液柱压力:</view>
            <view class="result-value">{{doubleFlange.range.lowLevelPressure}} kPa</view>
          </view>
        </view>

        <view class="result-column">
          <view class="calculation-result compact">
            <view class="result-label">空液位时差压:</view>
            <view class="result-value">{{doubleFlange.range.emptyPressure}} kPa</view>
          </view>

          <view class="calculation-result compact">
            <view class="result-label">满液位时差压:</view>
            <view class="result-value">{{doubleFlange.range.fullPressure}} kPa</view>
          </view>

          <view class="calculation-result compact">
            <view class="result-label">实际量程范围:</view>
            <view class="result-value range-value">{{doubleFlange.range.actualRange}} kPa</view>
          </view>
        </view>
      </view>

      <view class="calc-details-btn" bindtap="toggleCalcDetails">计算详情</view>
      <view class="calc-details-content" wx:if="{{showCalcDetails}}">{{doubleFlange.range.calcDetails}}</view>
      
      <!-- 压力转液位计算部分 -->
      <view class="section-divider">
        <view class="divider-line"></view>
        <view class="divider-text">压力转液位计算</view>
        <view class="divider-line"></view>
      </view>
      
      <view class="input-group">
        <view class="input-label">测量压力差 (kPa):</view>
        <input type="text" class="input-field" placeholder="请输入测量压力差（可输入负数）" bindinput="onInputChange" data-field="doubleFlange.pressure.pressureDiff" value="{{doubleFlange.pressure.pressureDiff}}"/>
      </view>

      <view class="calculation-result compact">
        <view class="result-label">液位高度:</view>
        <view class="result-value">
          <block wx:if="{{doubleFlange.pressure.result == '请先计算量程范围' || doubleFlange.pressure.result == '输入有误' || doubleFlange.pressure.result == '超出量程范围' || doubleFlange.pressure.result == '- -'}}">
            {{doubleFlange.pressure.result}}
          </block>
          <block wx:else>
            {{doubleFlange.pressure.result}} m
          </block>
        </view>
      </view>
      
      <view class="calculation-note bottom-note">
        <text class="note-text">注：根据实际量程范围{{doubleFlange.range.actualRange}} kPa与H0值({{doubleFlange.range.h0 || '?'}}m)的对应关系计算液位</text>
      </view>
    </view>
  </view>

  <view class="note-section">
    <view class="note-title">说明：</view>
    <view class="note-content">
      <text>• 变送器计算公式：测量值 = 量程下限 + (测量电流 - 4) × (量程上限 - 量程下限) ÷ 16</text>
      <text>• 单法兰液位计计算依据介质密度和液位计高度进行</text>
      <text>• 双法兰液位计适用于密闭容器液位测量，原理基于P=ρ×g×h</text>
      <text>• 双法兰液位计量程计算根据不同安装方式有不同计算方法</text>
      <text>• 支持三种安装方式：变送器在上/变送器在中间/变送器在下</text>
      <text>• 计算结果仅供参考，请以实际设备标定为准</text>
      <text>• 输入单位为m，计算结果输出单位为m</text>
    </view>
  </view>
</view> 