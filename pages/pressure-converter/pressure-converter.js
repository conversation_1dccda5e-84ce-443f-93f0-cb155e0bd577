Page({
  data: {
    // 压力换算数据
    psiValue: '',
    mpaValue: '',
    barValue: '',
    kgfValue: ''
  },

  onShow: function() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: 'PSI压力换算'
    });
  },

  // 格式化输入值，限制只能输入数字和一个小数点，最多两位小数
  formatInputValue: function(value) {
    if (!value) return value;
    
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    
    return value;
  },

  // 压力换算相关函数
  // PSI输入
  onPsiInput: function(e) {
    const value = this.formatInputValue(e.detail.value);
    if (value === '') {
      this.setData({
        psiValue: '',
        mpaValue: '',
        barValue: '',
        kgfValue: ''
      });
      return;
    }
    
    const psi = parseFloat(value);
    if (!isNaN(psi)) {
      const mpa = this.formatNumber(psi * 0.006895);
      const bar = this.formatNumber(psi * 0.06895);
      const kgf = this.formatNumber(psi * 0.070307);
      
      this.setData({
        psiValue: value,
        mpaValue: mpa,
        barValue: bar,
        kgfValue: kgf
      });
    }
  },

  // MPa输入
  onMpaInput: function(e) {
    const value = this.formatInputValue(e.detail.value);
    if (value === '') {
      this.setData({
        psiValue: '',
        mpaValue: '',
        barValue: '',
        kgfValue: ''
      });
      return;
    }
    
    const mpa = parseFloat(value);
    if (!isNaN(mpa)) {
      const psi = this.formatNumber(mpa * 145.038);
      const bar = this.formatNumber(mpa * 10);
      const kgf = this.formatNumber(mpa * 10.1972);
      
      this.setData({
        psiValue: psi,
        mpaValue: value,
        barValue: bar,
        kgfValue: kgf
      });
    }
  },

  // Bar输入
  onBarInput: function(e) {
    const value = this.formatInputValue(e.detail.value);
    if (value === '') {
      this.setData({
        psiValue: '',
        mpaValue: '',
        barValue: '',
        kgfValue: ''
      });
      return;
    }
    
    const bar = parseFloat(value);
    if (!isNaN(bar)) {
      const psi = this.formatNumber(bar * 14.5038);
      const mpa = this.formatNumber(bar * 0.1);
      const kgf = this.formatNumber(bar * 1.01972);
      
      this.setData({
        psiValue: psi,
        mpaValue: mpa,
        barValue: value,
        kgfValue: kgf
      });
    }
  },

  // Kgf/cm²输入
  onKgfInput: function(e) {
    const value = this.formatInputValue(e.detail.value);
    if (value === '') {
      this.setData({
        psiValue: '',
        mpaValue: '',
        barValue: '',
        kgfValue: ''
      });
      return;
    }
    
    const kgf = parseFloat(value);
    if (!isNaN(kgf)) {
      const psi = this.formatNumber(kgf * 14.2233);
      const mpa = this.formatNumber(kgf * 0.0980665);
      const bar = this.formatNumber(kgf * 0.980665);
      
      this.setData({
        psiValue: psi,
        mpaValue: mpa,
        barValue: bar,
        kgfValue: value
      });
    }
  },

  // 一键清除压力换算输入
  clearPressureInputs: function() {
    this.setData({
      psiValue: '',
      mpaValue: '',
      barValue: '',
      kgfValue: ''
    });
  },

  // 数字格式化，保留4位小数
  formatNumber: function(num) {
    return parseFloat(num.toFixed(4)).toString();
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-PSI换算',
      path: '/pages/pressure-converter/pressure-converter'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-PSI换算',
      query: 'from=timeline'
    };
  }
});
