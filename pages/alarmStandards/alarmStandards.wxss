/* pages/alarmStandards/alarmStandards.wxss */

.container {
  padding: 20rpx;
  background-color: #f7f7f7;
}

/* 选择区域样式 */
.selection-area {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.section:last-child {
  margin-bottom: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 8rpx;
}

.picker-container {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.picker-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.picker-wrapper {
  flex: 1;
  position: relative;
}

.picker {
  width: 100%;
}

.picker-content {
  height: 70rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding-left: 15rpx;
  padding-right: 40rpx;
  display: flex;
  align-items: center;
  border: 1px solid #e5e5e5;
  position: relative;
  overflow: visible;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.picker-arrow {
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
  pointer-events: none;
}

.input-container {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 85rpx;
  border: 1px solid #e0e0e0;
}

.input {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  padding: 0;
  margin: 0;
  border: none;
  background: transparent;
}

.unit {
  font-size: 30rpx;
  color: #333;
  margin-left: 10rpx;
  font-weight: bold;
  min-width: 60rpx;
  text-align: center;
}

/* 结果区域样式 */
.results-area {
  padding: 10rpx 0;
}

.result-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  align-items: center;
}

.result-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #444;
}

.result-value {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  text-align: right;
  padding-right: 10rpx;
}

/* 设置标准区域样式 */
.standard-area {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.standard-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 按钮区域样式 */
.button-area {
  display: flex;
  justify-content: space-between;
  margin-top: 18rpx;
  padding: 0 2rpx;
}

.calculate-btn, .reset-btn {
  width: 48%;
  font-size: 28rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
}

.calculate-btn {
  background-color: #1aad19;
  color: #ffffff;
}

.calculate-btn:active {
  background-color: #159115;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #333;
}

.reset-btn:active {
  background-color: #e0e0e0;
}

/* 添加报警阈值颜色区分 */
.level1-lower {
  background-color: #e6f7ff;
  border-left: 6rpx solid #1890ff;
}

.level1-upper {
  background-color: #fff7e6;
  border-left: 6rpx solid #faad14;
}

.level2-lower {
  background-color: #f6e6ff;
  border-left: 6rpx solid #722ed1;
}

.level2-upper {
  background-color: #ffe6e6;
  border-left: 6rpx solid #f5222d;
}

/* 执行标准底部样式 */
.standard-footer {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  margin: 30rpx 0 20rpx;
  padding: 10rpx;
  border-top: 1px solid #eee;
}

/* 特殊处理按钮区域后的标准说明区域 */
.section.standard-section {
  margin-top: 0rpx;
} 