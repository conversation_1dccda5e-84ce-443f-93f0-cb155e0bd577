Page({
  data: {
    // 选择相关
    platforms: ['采油平台', '中心平台', '火气系统'],
    platformIndex: "0",
    deviceTypes: [],
    deviceTypeIndex: "0",
    parameters: [],
    parameterIndex: "0",
    currentValue: '',
    currentUnit: '',
    
    // 结果相关
    showResults: false,
    level1LowerLimit: '',
    level1UpperLimit: '',
    level2LowerLimit: '',
    level2UpperLimit: '',
    
    // 标准说明
    standardDescription: '',
    
    // 状态控制
    isCalculating: false,
    
    // 是否需要输入当前值
    needsCurrentValue: true
  },

  onLoad: function (options) {
    // 初始化页面数据
    this.updateDeviceTypesByPlatform();
    this.updateParametersByDeviceType();
    this.updateUnitByParameter();
    this.updateStandardDescription();
  },
  
  // 平台类型切换
  bindPickerPlatformChange: function(e) {
    this.setData({
      platformIndex: e.detail.value,
      deviceTypeIndex: "0",
      parameterIndex: "0",
      currentValue: '',
      showResults: false
    });
    this.updateDeviceTypesByPlatform();
    this.updateParametersByDeviceType();
    this.updateUnitByParameter();
    this.updateStandardDescription();
    this.checkIfNeedsCurrentValue();
  },
  
  // 设备类型切换
  bindPickerDeviceTypeChange: function(e) {
    this.setData({
      deviceTypeIndex: e.detail.value,
      parameterIndex: "0",
      currentValue: '',
      showResults: false
    });
    this.updateParametersByDeviceType();
    this.updateUnitByParameter();
    this.updateStandardDescription();
    this.checkIfNeedsCurrentValue();
  },
  
  // 参数类型切换
  bindPickerParameterChange: function(e) {
    this.setData({
      parameterIndex: e.detail.value,
      currentValue: '',
      showResults: false
    });
    this.updateUnitByParameter();
    this.updateStandardDescription();
    this.checkIfNeedsCurrentValue();
  },
  
  // 检查是否需要输入当前值
  checkIfNeedsCurrentValue: function() {
    const platformIndex = parseInt(this.data.platformIndex);
    const deviceTypeIndex = parseInt(this.data.deviceTypeIndex);
    const parameterIndex = parseInt(this.data.parameterIndex);
    
    let needsCurrentValue = true;
    
    // 火气系统的所有参数直接显示设定值
    if (platformIndex === 2) {
      needsCurrentValue = false;
    }
    // 安全阀控制柜压力
    else if (platformIndex === 0 && deviceTypeIndex === 0 && parameterIndex === 8) {
      needsCurrentValue = false;
    }
    // 紧急切断阀 - 采油平台
    else if (platformIndex === 0 && deviceTypeIndex === 2 && parameterIndex === 3) {
      needsCurrentValue = false;
    }
    // 紧急切断阀 - 中心平台
    else if (platformIndex === 1 && deviceTypeIndex === 2 && parameterIndex === 2) {
      needsCurrentValue = false;
    }
    
    this.setData({
      needsCurrentValue: needsCurrentValue
    });
    
    // 如果不需要输入当前值，直接计算报警值
    if (!needsCurrentValue) {
      this.calculateAlarmValues();
    }
  },
  
  // 输入值改变
  onValueInput: function(e) {
    // 格式化输入值，限制只能输入一个小数点和两位小数
    const formattedValue = this.formatInputValue(e.detail.value);
    
    this.setData({
      currentValue: formattedValue,
      showResults: false
    });
  },
  
  // 格式化输入值，限制只能输入数字和一个小数点，最多两位小数
  formatInputValue: function(value) {
    if (!value) return value;
    
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    
    return value;
  },
  
  // 重置表单
  resetForm: function() {
    this.setData({
      currentValue: '',
      showResults: false
    });
  },
  
  // 计算报警值
  calculateAlarmValues: function() {
    if (this.data.needsCurrentValue && (!this.data.currentValue || isNaN(parseFloat(this.data.currentValue)))) {
      wx.showToast({
        title: '请输入有效的参数值',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    this.setData({
      isCalculating: true
    });
    
    const platformIndex = parseInt(this.data.platformIndex);
    const deviceTypeIndex = parseInt(this.data.deviceTypeIndex);
    const parameterIndex = parseInt(this.data.parameterIndex);
    const currentValue = this.data.needsCurrentValue ? parseFloat(this.data.currentValue) : 0;
    
    // 计算对应分类索引
    let categoryIndex = 0;
    
    if (platformIndex === 0) { // 采油平台
      if (deviceTypeIndex === 0) categoryIndex = 0; // 采油井
      else if (deviceTypeIndex === 1) categoryIndex = 1; // 水井
      else if (deviceTypeIndex === 2) categoryIndex = 2; // 外输工艺流程
    } 
    else if (platformIndex === 1) { // 中心平台
      if (deviceTypeIndex === 0) categoryIndex = 3; // 罐类
      else if (deviceTypeIndex === 1) categoryIndex = 4; // 泵类
      else if (deviceTypeIndex === 2) categoryIndex = 5; // 外输工艺流程
    }
    else if (platformIndex === 2) { // 火气系统
      categoryIndex = 6;
    }
    
    switch(categoryIndex) {
      case 0: // 采油平台-采油井
        this.calculateOilWellAlarm(parameterIndex, currentValue);
        break;
      case 1: // 采油平台-水井
        this.calculateWaterWellAlarm(parameterIndex, currentValue);
        break;
      case 2: // 采油平台-外输工艺流程
        this.calculateExportProcessAlarm(parameterIndex, currentValue);
        break;
      case 3: // 中心平台-罐类
        this.calculateTankAlarm(parameterIndex, currentValue);
        break;
      case 4: // 中心平台-泵类
        this.calculatePumpAlarm(parameterIndex, currentValue);
        break;
      case 5: // 中心平台-外输工艺流程
        this.calculateCentralExportAlarm(parameterIndex, currentValue);
        break;
      case 6: // 火气系统
        this.calculateFireGasAlarm(parameterIndex, currentValue);
        break;
    }
    
    this.setData({
      isCalculating: false,
      showResults: true
    });
  },
  
  // 根据平台类型更新设备类型列表
  updateDeviceTypesByPlatform: function() {
    let deviceTypes = [];
    
    switch (parseInt(this.data.platformIndex)) {
      case 0: // 采油平台
        deviceTypes = ['采油井', '水井', '外输工艺流程'];
        break;
      case 1: // 中心平台
        deviceTypes = ['罐类', '泵类', '外输工艺流程'];
        break;
      case 2: // 火气系统
        deviceTypes = ['火气系统'];
        break;
    }
    
    this.setData({
      deviceTypes,
      deviceTypeIndex: "0"
    });
  },
  
  // 根据设备类型更新参数列表
  updateParametersByDeviceType: function() {
    let parameters = [];
    const platformIndex = parseInt(this.data.platformIndex);
    const deviceTypeIndex = parseInt(this.data.deviceTypeIndex);
    
    // 计算对应分类索引
    let categoryIndex = 0;
    
    if (platformIndex === 0) { // 采油平台
      if (deviceTypeIndex === 0) categoryIndex = 0; // 采油井
      else if (deviceTypeIndex === 1) categoryIndex = 1; // 水井
      else if (deviceTypeIndex === 2) categoryIndex = 2; // 外输工艺流程
    } 
    else if (platformIndex === 1) { // 中心平台
      if (deviceTypeIndex === 0) categoryIndex = 3; // 罐类
      else if (deviceTypeIndex === 1) categoryIndex = 4; // 泵类
      else if (deviceTypeIndex === 2) categoryIndex = 5; // 外输工艺流程
    }
    else if (platformIndex === 2) { // 火气系统
      categoryIndex = 6;
    }
    
    switch (categoryIndex) {
      case 0: // 采油平台-采油井
        parameters = ['油压', '套压', '回压', '温度', '电流', '电压', '毛管压力', '井下传感器', '安全阀控制柜压力'];
        break;
      case 1: // 采油平台-水井
        parameters = ['注水压力', '注水瞬时流量'];
        break;
      case 2: // 采油平台-外输工艺流程
        parameters = ['干压', '干温', '分离器压力', '紧急切断阀'];
        break;
      case 3: // 中心平台-罐类
        parameters = ['液位', '压力', '温度', '流量'];
        break;
      case 4: // 中心平台-泵类
        parameters = ['压力', '温度', '位移', '电机电流', '电机电压'];
        break;
      case 5: // 中心平台-外输工艺流程
        parameters = ['干压', '干温', '紧急切断阀'];
        break;
      case 6: // 火气系统
        parameters = ['可燃气体浓度', '硫化氢浓度', '火焰探测器'];
        break;
    }
    
    this.setData({
      parameters,
      parameterIndex: "0"
    });
  },
  
  // 根据参数类型更新单位
  updateUnitByParameter: function() {
    const platformIndex = parseInt(this.data.platformIndex);
    const deviceTypeIndex = parseInt(this.data.deviceTypeIndex);
    const parameterIndex = parseInt(this.data.parameterIndex);
    
    // 计算对应分类索引
    let categoryIndex = 0;
    
    if (platformIndex === 0) { // 采油平台
      if (deviceTypeIndex === 0) categoryIndex = 0; // 采油井
      else if (deviceTypeIndex === 1) categoryIndex = 1; // 水井
      else if (deviceTypeIndex === 2) categoryIndex = 2; // 外输工艺流程
    } 
    else if (platformIndex === 1) { // 中心平台
      if (deviceTypeIndex === 0) categoryIndex = 3; // 罐类
      else if (deviceTypeIndex === 1) categoryIndex = 4; // 泵类
      else if (deviceTypeIndex === 2) categoryIndex = 5; // 外输工艺流程
    }
    else if (platformIndex === 2) { // 火气系统
      categoryIndex = 6;
    }
    
    let unit = '';
    
    // 设置不同参数的单位
    if (categoryIndex == 0) { // 采油平台-采油井
      const params = ['MPa', 'MPa', 'MPa', '℃', 'A', 'V', 'MPa', '', 'MPa'];
      unit = params[parameterIndex] || '';
    } else if (categoryIndex == 1) { // 采油平台-水井
      const params = ['MPa', 'm³/h'];
      unit = params[parameterIndex] || '';
    } else if (categoryIndex == 2) { // 采油平台-外输工艺流程
      const params = ['MPa', '℃', 'MPa', '%'];
      unit = params[parameterIndex] || '';
    } else if (categoryIndex == 3) { // 中心平台-罐类
      const params = ['m', 'MPa', '℃', 'm³/h'];
      unit = params[parameterIndex] || '';
    } else if (categoryIndex == 4) { // 中心平台-泵类
      const params = ['MPa', '℃', 'mm', 'A', 'V'];
      unit = params[parameterIndex] || '';
    } else if (categoryIndex == 5) { // 中心平台-外输工艺流程
      const params = ['MPa', '℃', '%'];
      unit = params[parameterIndex] || '';
    } else if (categoryIndex == 6) { // 火气系统
      const params = ['%LEL', 'ppm', 'mA'];
      unit = params[parameterIndex] || '';
    }
    
    this.setData({
      currentUnit: unit
    });
  },
  
  // 更新标准说明
  updateStandardDescription: function() {
    const platformIndex = parseInt(this.data.platformIndex);
    const deviceTypeIndex = parseInt(this.data.deviceTypeIndex);
    const parameterIndex = parseInt(this.data.parameterIndex);
    
    let description = '';
    
    // 采油平台-采油井
    if (platformIndex === 0 && deviceTypeIndex === 0) {
      switch(parameterIndex) {
        case 0: // 油压
          description = "一级报警阈值：\n根据实际油压值，油压小于5MPa的，±0.5MPa为一级报警值的上下限；\n油压高于5MPa的，±10%为一级报警值的上下限。\n油压波动井，取正常波动的峰谷值±0.5MPa为一级报警值的上下限。\n\n二级报警阈值：\n根据实际油压值，油压小于5MPa的，±1.5MPa为二级报警值的上下限；\n油压高于5MPa的，±30%为二级报警值的上下限。\n油压波动井，取正常波动的峰谷值±1.5MPa为二级报警值的上下限。";
          break;
        case 1: // 套压
          description = "一级报警阈值：\n根据实际套压值，套压小于1MPa的，±0.3MPa为一级报警值的上下限；\n套压高于1MPa的，±0.5MPa为一级报警值的上下限。\n套压波动井，取正常波动的峰谷值±0.5MPa为一级报警值的上下限。\n\n二级报警阈值：\n根据实际套压值，套压小于1MPa的，±0.5MPa为二级报警值的上下限；\n套压高于1MPa的，±1MPa为二级报警值的上下限。\n套压波动井，取正常波动的峰谷值±1MPa为二级报警值的上下限。";
          break;
        case 2: // 回压
          description = "一级报警阈值：\n根据实际回压值，回压小于1MPa的，±0.2MPa为一级报警值的上下限；\n回压高于1MPa的，±0.3MPa为一级报警值的上下限。\n回压波动井，取正常波动的峰谷值±0.3MPa为一级报警值的上下限。\n\n二级报警阈值：\n根据实际回压值，回压小于1MPa的，±0.3MPa为二级报警值的上下限；\n回压高于1MPa的，±0.5MPa为二级报警值的上下限。\n回压波动井，取正常波动的峰谷值±0.5MPa为二级报警值的上下限。\n回压二级报警值上下限不得超出相应联锁关断的触发值。";
          break;
        case 3: // 温度
          description = "一级报警阈值：\n根据实际温度，温度小于50℃的，±5℃为一级报警值的上下限；\n温度高于50℃的，±10%为一级报警值的上下限。\n因季节变化而引起的昼夜温度变化较大的，取波动的峰谷值±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际温度，温度小于50℃的，±10℃为二级报警值的上下限；\n温度高于50℃的，±20%为二级报警值的上下限。\n因季节变化而引起的昼夜温度变化较大的，取波动的峰谷值±20%为二级报警值的上下限。";
          break;
        case 4: // 电流
          description = "一级报警阈值：\n根据实际电流，电流小于40A的，±2A为一级报警值的上下限；\n电流高于40A的，±5%为一级报警值的上下限。\n电流波动井，取正常波动的峰谷值±2A为一级报警值的上下限。\n\n二级报警阈值：\n由于保护中心电流过载欠载保护原则为正常工作电流的120%和80%，根据实际电流，±15%为二级报警值的上下限。\n电流波动井，取正常波动的峰谷值±15%为二级报警值的上下限。";
          break;
        case 5: // 电压
          description = "一级报警阈值：\n根据正常工作电压，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常工作电压，±20%为二级报警值的上下限。";
          break;
        case 6: // 毛管压力
          description = "一级报警阈值：\n根据正常毛管压力值，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常毛管压力值，±20%为二级报警值的上下限。";
          break;
        case 7: // 井下传感器
          description = "一级报警阈值：\n根据正常参数值，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常参数值，±20%为二级报警值的上下限。";
          break;
        case 8: // 安全阀控制柜压力
          description = "一级报警阈值：\n设24.1Mpa（3500psi）为一级报警值下限。\n\n二级报警阈值：\n设20.6Mpa（3000psi）为二级报警值下限。\n设37.9Mpa（5500psi）为二级报警值上限。";
          break;
      }
    } 
    // 采油平台-水井
    else if (platformIndex === 0 && deviceTypeIndex === 1) {
      switch(parameterIndex) {
        case 0: // 注水压力
          description = "一级报警阈值：\n根据正常注水压力，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常注水压力，注水压力低于5MPa的，±1.5MPa为一级报警值的上下限。注水压力高于5MPa的,±30%为二级报警值的上下限。\n注水压力波动井，取正常波动的峰谷值±30%为二级报警值的上下限。";
          break;
        case 1: // 注水瞬时流量
          description = "一级报警阈值：\n根据实际注水流量，配注量小于1m³/h，低报警值下限为0.1m³/h，高报警值上限为1.5m³/h。配注量大于1m³/h，按当前实际注水流量值±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际注水流量，配注量小于1m³/h，低低报警值下限为0.1m³/h，高高报警值上限为2m³/h。配注量大于1m³/h，当前实际注水流量值±30%为二级报警值的上下限。\n注水流量波动井，取正常波动的峰谷值±30%为二级报警值的上下限。";
          break;
      }
    } 
    // 采油平台-外输工艺流程
    else if (platformIndex === 0 && deviceTypeIndex === 2) {
      switch(parameterIndex) {
        case 0: // 干压
          description = "一级报警阈值：\n根据实际干压值，干压小于1MPa的，±0.2MPa为一级报警值的上下限。干压高于1MPa的,±0.3MPa为一级报警值的上下限。\n\n二级报警阈值：\n根据实际干压值，干压小于1MPa的，±0.30MPa为二级报警值的上下限。干压高于1MPa的,±0.5MPa为二级报警值的上下限。\n干压波动较大的，取正常波动的峰谷值±0.5MPa为二级报警值的上下限。\n干压二级报警值上下限不得超出相应联锁关断的触发值。";
          break;
        case 1: // 干温
          description = "一级报警阈值：\n根据实际温度，±5℃为一级报警值的上下限。\n因季节变化而引起的昼夜温度变化较大的，取波动的峰谷值±10%为二级报警值的上下限。\n\n二级报警阈值：\n根据实际温度，±10℃为二级报警值的上下限。\n因季节变化而引起的昼夜温度变化较大的，取波动的峰谷值±20%为二级报警值的上下限。";
          break;
        case 2: // 分离器压力
          description = "一级报警阈值：\n根据实际压力值，分压小于1MPa的，±0.20为一级报警值的上下限。分压高于1MPa的,±20%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际分压值，分压小于1MPa的，±0.40为二级报警值的上下限。分压高于1MPa的,±30%为二级报警值的上下限。\n二级报警上限不得高于分离器安全阀整定压力的80%。";
          break;
        case 3: // 紧急切断阀
          description = "正常情况下紧急切断阀为全开或全关状态，只要阀开度发生不明原因变化都属于严重类问题，所以设置开度90%为二级报警下限。";
          break;
      }
    } 
    // 中心平台-罐类
    else if (platformIndex === 1 && deviceTypeIndex === 0) {
      switch(parameterIndex) {
        case 0: // 液位
          description = "一级报警阈值：\n罐总液位高度的35%为低报警下限，65%为高报警上限。\n\n二级报警阈值：\n罐总液位高度的20%为低低报警下限，80%为高高报警上限。\n液位二级报警值上下限不得超出相应联锁关断的触发值。";
          break;
        case 1: // 压力
          description = "一级报警阈值：\n根据正常工作压力，±20%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常工作压力，加减40%为二级报警值的上下限。（不得高于安全阀整定压力的80%）。\n压力二级报警值上下限不得超出相应联锁关断的触发值。";
          break;
        case 2: // 温度
          description = "一级报警阈值：\n根据正常工作温度，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常工作温度，±30%为二级报警值的上下限。";
          break;
        case 3: // 流量
          description = "一级报警阈值：\n根据正常工作流量，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据正常工作流量，±30%为二级报警值的上下限。";
          break;
      }
    } 
    // 中心平台-泵类
    else if (platformIndex === 1 && deviceTypeIndex === 1) {
      switch(parameterIndex) {
        case 0: // 压力
          description = "一级报警阈值：\n根据实际工作压力，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际工作压力，±30%为二级报警值的上下限。";
          break;
        case 1: // 温度
          description = "一级报警阈值：\n根据实际工作温度，+10%为一级报警值的上限。\n\n二级报警阈值：\n根据实际工作温度，+30%为二级报警值的上限。\n温度二级报警值上下限不得超出相应联锁关断的触发值。";
          break;
        case 2: // 位移
          description = "一级报警阈值：\n根据实际工作位移量，+10%为一级报警值的上限。\n\n二级报警阈值：\n根据实际工作位移量，+30%为二级报警值的上限。";
          break;
        case 3: // 电机电流
          description = "一级报警阈值：\n根据实际工作电流，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际工作电流，±30%为二级报警值的上下限。";
          break;
        case 4: // 电机电压
          description = "一级报警阈值：\n根据实际工作电压，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际工作电压，±30%为二级报警值的上下限。";
          break;
      }
    } 
    // 中心平台-外输工艺流程
    else if (platformIndex === 1 && deviceTypeIndex === 2) {
      switch(parameterIndex) {
        case 0: // 干压
          description = "一级报警阈值：\n根据实际干压值，干压小于1MPa的，±0.10为一级报警值的上下限。干压高于1MPa的，±10%为一级报警值的上下限。\n\n二级报警阈值：\n根据实际干压值，干压小于1MPa的，±0.30为二级报警值的上下限。干压高于1MPa的，±30%为二级报警值的上下限。\n干压二级报警值上下限不得超出相应联锁关断的触发值。";
          break;
        case 1: // 干温
          description = "一级报警阈值：\n根据实际温度，±5℃为一级报警值的上下限。\n\n二级报警阈值：\n根据实际温度，±15℃为二级报警值的上下限。";
          break;
        case 2: // 紧急切断阀
          description = "正常情况下紧急切断阀为全开或全关状态，只要阀开度发生不明原因变化都属于严重类问题，所以设置开度90%为二级报警值的下限。";
          break;
      }
    } 
    // 火气系统
    else if (platformIndex === 2) {
      switch(parameterIndex) {
        case 0: // 可燃气体浓度
          description = "一级报警阈值：\n一级报警设置低报警值为-5%LEL，高报警值为20%LEL。\n\n二级报警阈值：\n二级报警设置高高报警值为40% LEL。";
          break;
        case 1: // 硫化氢浓度
          description = "一级报警阈值：\n一级报警设置高报警为10ppm。\n\n二级报警阈值：\n二级报警设置高高报警为20ppm。";
          break;
        case 2: // 火焰探测器监测
          description = "一级报警阈值：\n一级报警设置低报警为-5mA，高报警值为12mA。\n\n二级报警阈值：\n二级报警设置高高报警为20mA。";
          break;
      }
    }
    
    this.setData({
      standardDescription: description
    });
  },
  
  // 计算采油井报警阈值
  calculateOilWellAlarm: function(parameterIndex, currentValue) {
    let level1Lower = 0;
    let level1Upper = 0;
    let level2Lower = 0;
    let level2Upper = 0;
    
    switch(parameterIndex) {
      case 0: // 油压
        if (currentValue < 5) {
          level1Lower = Math.max(currentValue - 0.5, 0);
          level1Upper = currentValue + 0.5;
          level2Lower = Math.max(currentValue - 1.5, 0);
          level2Upper = currentValue + 1.5;
        } else {
          level1Lower = currentValue * 0.9;
          level1Upper = currentValue * 1.1;
          level2Lower = currentValue * 0.7;
          level2Upper = currentValue * 1.3;
        }
        break;
      case 1: // 套压
        if (currentValue < 1) {
          level1Lower = Math.max(currentValue - 0.3, 0);
          level1Upper = currentValue + 0.3;
          level2Lower = Math.max(currentValue - 0.5, 0);
          level2Upper = currentValue + 0.5;
        } else {
          level1Lower = Math.max(currentValue - 0.5, 0);
          level1Upper = currentValue + 0.5;
          level2Lower = Math.max(currentValue - 1, 0);
          level2Upper = currentValue + 1;
        }
        break;
      case 2: // 回压
        if (currentValue < 1) {
          level1Lower = Math.max(currentValue - 0.2, 0);
          level1Upper = currentValue + 0.2;
          level2Lower = Math.max(currentValue - 0.3, 0);
          level2Upper = currentValue + 0.3;
        } else {
          level1Lower = Math.max(currentValue - 0.3, 0);
          level1Upper = currentValue + 0.3;
          level2Lower = Math.max(currentValue - 0.5, 0);
          level2Upper = currentValue + 0.5;
        }
        break;
      case 3: // 温度
        if (currentValue < 50) {
          level1Lower = Math.max(currentValue - 5, 0);
          level1Upper = currentValue + 5;
          level2Lower = Math.max(currentValue - 10, 0);
          level2Upper = currentValue + 10;
        } else {
          level1Lower = currentValue * 0.9;
          level1Upper = currentValue * 1.1;
          level2Lower = currentValue * 0.8;
          level2Upper = currentValue * 1.2;
        }
        break;
      case 4: // 电流
        if (currentValue < 40) {
          level1Lower = Math.max(currentValue - 2, 0);
          level1Upper = currentValue + 2;
          level2Lower = Math.max(currentValue * 0.85, 0);
          level2Upper = currentValue * 1.15;
        } else {
          level1Lower = currentValue * 0.95;
          level1Upper = currentValue * 1.05;
          level2Lower = currentValue * 0.85;
          level2Upper = currentValue * 1.15;
        }
        break;
      case 5: // 电压
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.8;
        level2Upper = currentValue * 1.2;
        break;
      case 6: // 毛管压力
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.8;
        level2Upper = currentValue * 1.2;
        break;
      case 7: // 井下传感器
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.8;
        level2Upper = currentValue * 1.2;
        break;
      case 8: // 安全阀控制柜压力
        level1Lower = 24.1;
        level1Upper = null; // 一级报警没有上限
        level2Lower = 20.6;
        level2Upper = 37.9;
        break;
    }
    
    // 确保下限不小于0
    if (level1Lower !== null) level1Lower = Math.max(0, level1Lower);
    if (level2Lower !== null) level2Lower = Math.max(0, level2Lower);
    
    this.setData({
      level1LowerLimit: level1Lower !== null ? level1Lower.toFixed(2) : '',
      level1UpperLimit: level1Upper !== null ? level1Upper.toFixed(2) : '',
      level2LowerLimit: level2Lower !== null ? level2Lower.toFixed(2) : '',
      level2UpperLimit: level2Upper !== null ? level2Upper.toFixed(2) : ''
    });
  },
  
  // 水井报警阈值计算
  calculateWaterWellAlarm: function(parameterIndex, currentValue) {
    let level1Lower = 0;
    let level1Upper = 0;
    let level2Lower = 0;
    let level2Upper = 0;
    
    switch(parameterIndex) {
      case 0: // 注水压力
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        
        if (currentValue < 5) {
          level2Lower = Math.max(currentValue - 1.5, 0);
          level2Upper = currentValue + 1.5;
        } else {
          level2Lower = currentValue * 0.7;
          level2Upper = currentValue * 1.3;
        }
        break;
      case 1: // 注水瞬时流量
        if (currentValue < 1) {
          level1Lower = 0.1;
          level1Upper = 1.5;
          level2Lower = 0.1;
          level2Upper = 2;
        } else {
          level1Lower = currentValue * 0.9;
          level1Upper = currentValue * 1.1;
          level2Lower = currentValue * 0.7;
          level2Upper = currentValue * 1.3;
        }
        break;
    }
    
    // 确保下限不小于0
    if (level1Lower !== null) level1Lower = Math.max(0, level1Lower);
    if (level2Lower !== null) level2Lower = Math.max(0, level2Lower);
    
    this.setData({
      level1LowerLimit: level1Lower !== null ? level1Lower.toFixed(2) : '',
      level1UpperLimit: level1Upper !== null ? level1Upper.toFixed(2) : '',
      level2LowerLimit: level2Lower !== null ? level2Lower.toFixed(2) : '',
      level2UpperLimit: level2Upper !== null ? level2Upper.toFixed(2) : ''
    });
  },
  
  // 计算外输工艺流程报警值
  calculateExportProcessAlarm: function(parameterIndex, currentValue) {
    let level1Lower = 0;
    let level1Upper = 0;
    let level2Lower = 0;
    let level2Upper = 0;
    
    switch(parameterIndex) {
      case 0: // 干压
        if (currentValue < 1) {
          level1Lower = Math.max(currentValue - 0.2, 0);
          level1Upper = currentValue + 0.2;
          level2Lower = Math.max(currentValue - 0.3, 0);
          level2Upper = currentValue + 0.3;
        } else {
          level1Lower = Math.max(currentValue - 0.3, 0);
          level1Upper = currentValue + 0.3;
          level2Lower = Math.max(currentValue - 0.5, 0);
          level2Upper = currentValue + 0.5;
        }
        break;
      case 1: // 干温
        level1Lower = Math.max(currentValue - 5, 0);
        level1Upper = currentValue + 5;
        level2Lower = Math.max(currentValue - 10, 0);
        level2Upper = currentValue + 10;
        break;
      case 2: // 分离器压力
        if (currentValue < 1) {
          level1Lower = Math.max(currentValue - 0.2, 0);
          level1Upper = currentValue + 0.2;
          level2Lower = Math.max(currentValue - 0.4, 0);
          level2Upper = currentValue + 0.4;
        } else {
          level1Lower = currentValue * 0.8;
          level1Upper = currentValue * 1.2;
          level2Lower = currentValue * 0.7;
          level2Upper = currentValue * 1.3;
        }
        break;
      case 3: // 紧急切断阀
        level1Lower = null; // 一级报警没有下限
        level1Upper = null; // 一级报警没有上限
        level2Lower = 90; // 只有二级下限
        level2Upper = null; // 二级报警没有上限
        break;
    }
    
    // 确保下限不小于0
    if (level1Lower !== null) level1Lower = Math.max(0, level1Lower);
    if (level2Lower !== null) level2Lower = Math.max(0, level2Lower);
    
    this.setData({
      level1LowerLimit: level1Lower !== null ? level1Lower.toFixed(2) : '',
      level1UpperLimit: level1Upper !== null ? level1Upper.toFixed(2) : '',
      level2LowerLimit: level2Lower !== null ? level2Lower.toFixed(2) : '',
      level2UpperLimit: level2Upper !== null ? level2Upper.toFixed(2) : ''
    });
  },
  
  // 计算中心平台罐类报警值
  calculateTankAlarm: function(parameterIndex, currentValue) {
    let level1Lower = 0;
    let level1Upper = 0;
    let level2Lower = 0;
    let level2Upper = 0;
    
    switch(parameterIndex) {
      case 0: // 液位 - 输入单位为m，但按百分比计算
        // 不需要当前值，直接使用百分比设定
        level1Lower = 35; // 罐总液位高度的35%
        level1Upper = 65; // 65%
        level2Lower = 20; // 20%
        level2Upper = 80; // 80%
        
        // 如果有当前值(m)，转换为对应的m值
        if (this.data.needsCurrentValue && currentValue > 0) {
          // 假设当前值是总高度
          level1Lower = (currentValue * 0.35).toFixed(2);
          level1Upper = (currentValue * 0.65).toFixed(2);
          level2Lower = (currentValue * 0.20).toFixed(2);
          level2Upper = (currentValue * 0.80).toFixed(2);
        }
        break;
      case 1: // 压力
        level1Lower = currentValue * 0.8;
        level1Upper = currentValue * 1.2;
        level2Lower = currentValue * 0.6;
        level2Upper = currentValue * 1.4;
        break;
      case 2: // 温度
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.7;
        level2Upper = currentValue * 1.3;
        break;
      case 3: // 流量
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.7;
        level2Upper = currentValue * 1.3;
        break;
    }
    
    // 确保下限不小于0
    level1Lower = Math.max(0, level1Lower);
    level2Lower = Math.max(0, level2Lower);
    
    this.setData({
      level1LowerLimit: typeof level1Lower === 'string' ? level1Lower : level1Lower.toFixed(2),
      level1UpperLimit: typeof level1Upper === 'string' ? level1Upper : level1Upper.toFixed(2),
      level2LowerLimit: typeof level2Lower === 'string' ? level2Lower : level2Lower.toFixed(2),
      level2UpperLimit: typeof level2Upper === 'string' ? level2Upper : level2Upper.toFixed(2)
    });
  },
  
  // 计算中心平台泵类报警值
  calculatePumpAlarm: function(parameterIndex, currentValue) {
    let level1Lower = 0;
    let level1Upper = 0;
    let level2Lower = 0;
    let level2Upper = 0;
    
    switch(parameterIndex) {
      case 0: // 压力
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.7;
        level2Upper = currentValue * 1.3;
        break;
      case 1: // 温度
        level1Lower = currentValue * 0.9; // 下限同样设置为90%
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.7; // 下限为70%
        level2Upper = currentValue * 1.3;
        break;
      case 2: // 位移
        level1Lower = 0; // 下限为0
        level1Upper = currentValue * 1.1;
        level2Lower = 0; // 下限为0
        level2Upper = currentValue * 1.3;
        break;
      case 3: // 电机电流
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.7;
        level2Upper = currentValue * 1.3;
        break;
      case 4: // 电机电压
        level1Lower = currentValue * 0.9;
        level1Upper = currentValue * 1.1;
        level2Lower = currentValue * 0.7;
        level2Upper = currentValue * 1.3;
        break;
    }
    
    // 确保下限不小于0
    if (level1Lower !== null) level1Lower = Math.max(0, level1Lower);
    if (level2Lower !== null) level2Lower = Math.max(0, level2Lower);
    
    this.setData({
      level1LowerLimit: level1Lower !== null ? level1Lower.toFixed(2) : '',
      level1UpperLimit: level1Upper !== null ? level1Upper.toFixed(2) : '',
      level2LowerLimit: level2Lower !== null ? level2Lower.toFixed(2) : '',
      level2UpperLimit: level2Upper !== null ? level2Upper.toFixed(2) : ''
    });
  },
  
  // 计算中心平台外输工艺流程报警值
  calculateCentralExportAlarm: function(parameterIndex, currentValue) {
    let level1Lower = 0;
    let level1Upper = 0;
    let level2Lower = 0;
    let level2Upper = 0;
    
    switch(parameterIndex) {
      case 0: // 干压
        if (currentValue < 1) {
          level1Lower = Math.max(currentValue - 0.1, 0);
          level1Upper = currentValue + 0.1;
          level2Lower = Math.max(currentValue - 0.3, 0);
          level2Upper = currentValue + 0.3;
        } else {
          level1Lower = currentValue * 0.9;
          level1Upper = currentValue * 1.1;
          level2Lower = currentValue * 0.7;
          level2Upper = currentValue * 1.3;
        }
        break;
      case 1: // 干温
        level1Lower = Math.max(currentValue - 5, 0);
        level1Upper = currentValue + 5;
        level2Lower = Math.max(currentValue - 15, 0);
        level2Upper = currentValue + 15;
        break;
      case 2: // 紧急切断阀
        level1Lower = null; // 一级报警没有下限
        level1Upper = null; // 一级报警没有上限
        level2Lower = 90; // 只有二级下限
        level2Upper = null; // 二级报警没有上限
        break;
    }
    
    // 确保下限不小于0
    if (level1Lower !== null) level1Lower = Math.max(0, level1Lower);
    if (level2Lower !== null) level2Lower = Math.max(0, level2Lower);
    
    this.setData({
      level1LowerLimit: level1Lower !== null ? level1Lower.toFixed(2) : '',
      level1UpperLimit: level1Upper !== null ? level1Upper.toFixed(2) : '',
      level2LowerLimit: level2Lower !== null ? level2Lower.toFixed(2) : '',
      level2UpperLimit: level2Upper !== null ? level2Upper.toFixed(2) : ''
    });
  },
  
  // 计算火气系统报警值
  calculateFireGasAlarm: function(parameterIndex, currentValue) {
    let level1Lower = null;
    let level1Upper = null;
    let level2Lower = null;
    let level2Upper = null;
    
    switch(parameterIndex) {
      case 0: // 可燃气体浓度
        level1Lower = -5; // 低报警为-5%LEL
        level1Upper = 20; // 高报警为20%LEL
        level2Lower = null; // 二级报警没有下限
        level2Upper = 40; // 高高报警为40%LEL
        break;
      case 1: // 硫化氢浓度
        level1Lower = null; // 一级报警没有下限
        level1Upper = 10; // 高报警为10ppm
        level2Lower = null; // 二级报警没有下限
        level2Upper = 20; // 高高报警为20ppm
        break;
      case 2: // 火焰探测器监测
        level1Lower = -5; // 低报警为-5mA
        level1Upper = 12; // 高报警为12mA
        level2Lower = null; // 二级报警没有下限
        level2Upper = 20; // 高高报警为20mA
        break;
    }
    
    this.setData({
      level1LowerLimit: level1Lower !== null ? level1Lower.toFixed(2) : '',
      level1UpperLimit: level1Upper !== null ? level1Upper.toFixed(2) : '',
      level2LowerLimit: level2Lower !== null ? level2Lower.toFixed(2) : '',
      level2UpperLimit: level2Upper !== null ? level2Upper.toFixed(2) : ''
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '报警值设定标准查询工具',
      path: '/pages/alarmStandards/alarmStandards'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '报警值设定标准查询',
      query: 'from=timeline'
    };
  }
}); 