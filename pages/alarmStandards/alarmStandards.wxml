<view class="container">
  <!-- 选择区域 -->
  <view class="section">
    <view class="section-title">平台与设备选择</view>
    <view class="picker-container">
      <view class="picker-label">平台类型</view>
      <view class="picker-wrapper">
        <picker bindchange="bindPickerPlatformChange" value="{{platformIndex}}" range="{{platforms}}" class="picker">
          <view class="picker-content">
            <view class="picker-text">{{platforms[platformIndex]}}</view>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="picker-container">
      <view class="picker-label">设备类型</view>
      <view class="picker-wrapper">
        <picker bindchange="bindPickerDeviceTypeChange" value="{{deviceTypeIndex}}" range="{{deviceTypes}}" class="picker">
          <view class="picker-content">
            <view class="picker-text">{{deviceTypes[deviceTypeIndex]}}</view>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
    <view class="picker-container">
      <view class="picker-label">参数类型</view>
      <view class="picker-wrapper">
        <picker bindchange="bindPickerParameterChange" value="{{parameterIndex}}" range="{{parameters}}" class="picker">
          <view class="picker-content">
            <view class="picker-text">{{parameters[parameterIndex]}}</view>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 输入区域和按钮区域 - 仅在需要输入当前值时显示 -->
  <view class="section" wx:if="{{needsCurrentValue}}">
    <view class="section-title">当前参数值</view>
    <view class="input-container">
      <input type="digit" value="{{currentValue}}" bindinput="onValueInput" placeholder="请输入当前参数值" class="input" />
      <text class="unit">{{currentUnit}}</text>
    </view>
    
    <!-- 按钮区域整合到输入区域内 -->
    <view class="button-area">
      <button class="calculate-btn" bindtap="calculateAlarmValues" loading="{{isCalculating}}">计算报警值</button>
      <button class="reset-btn" bindtap="resetForm">重置</button>
    </view>
  </view>

  <!-- 结果区域 - 有结果或不需要当前值时显示 -->
  <view class="section" wx:if="{{showResults || !needsCurrentValue}}">
    <view class="section-title">报警阈值</view>
    <view class="results-area">
      <view class="result-item level1-lower" wx:if="{{level1LowerLimit !== ''}}">
        <view class="result-label">一级报警下限:</view>
        <view class="result-value">{{level1LowerLimit}}{{currentUnit}}</view>
      </view>
      <view class="result-item level1-upper" wx:if="{{level1UpperLimit !== ''}}">
        <view class="result-label">一级报警上限:</view>
        <view class="result-value">{{level1UpperLimit}}{{currentUnit}}</view>
      </view>
      <view class="result-item level2-lower" wx:if="{{level2LowerLimit !== ''}}">
        <view class="result-label">二级报警下限:</view>
        <view class="result-value">{{level2LowerLimit}}{{currentUnit}}</view>
      </view>
      <view class="result-item level2-upper" wx:if="{{level2UpperLimit !== ''}}">
        <view class="result-label">二级报警上限:</view>
        <view class="result-value">{{level2UpperLimit}}{{currentUnit}}</view>
      </view>
    </view>
  </view>
  
  <!-- 标准说明区域 -->
  <view class="section standard-section">
    <view class="section-title">标准说明</view>
    <view class="standard-area">
      <text class="standard-text" user-select="true">{{standardDescription}}</text>
    </view>
  </view>
  
  <!-- 执行标准 -->
  <view class="standard-footer">
    <text>执行标准：胜海油发〔2021〕35号《海洋采油厂工艺自动化系统管理细则》</text>
  </view>
</view> 