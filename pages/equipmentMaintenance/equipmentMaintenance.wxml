<view class="container">
  <!-- 页面头部 -->
  <view class="module-header">
    <view class="picker-container">
      <view class="picker">
        🔧 设备保养时间计算器
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="module-box">
    <view class="input-section">
      <view class="input-row">
        <view class="input-group">
          <text class="input-label">一保运行时间（h）：</text>
          <input type="digit" class="input-field" bindinput="onFirstInputChange" placeholder="请输入一保运行小时数" value="{{firstRunningHours}}" />
        </view>
        <view class="input-group">
          <text class="input-label">二保运行时间（h）：</text>
          <input type="digit" class="input-field" bindinput="onSecondInputChange" placeholder="请输入二保运行小时数" value="{{secondRunningHours}}" />
        </view>
      </view>
      <button class="calculate-btn" bindtap="calculateMaintenance">🔍 开始计算</button>
    </view>
  </view>

  <!-- 结果显示区域 -->
  <view class="result-section" wx:if="{{showResults}}">
    <!-- 一级保养结果 -->
    <view class="module-box result-box first-maintenance" wx:if="{{showFirstResult}}">
      <view class="result-header">
        <view class="result-title">
          <text class="title-icon">🔧</text>
          <text class="title-text">一级保养（每1000小时）</text>
        </view>
      </view>
      <view class="result-content">
        <view class="result-row">
          <view class="result-item">
            <text class="result-label">距离下次保养还需</text>
            <view class="result-value-container">
              <block wx:if="{{isFirstOverdue}}">
                <text class="result-value overdue">超 {{-remainingHoursForFirst}} 小时（{{daysForFirst}} 天）</text>
              </block>
              <block wx:else>
                <text class="result-value {{remainingHoursForFirst <= 48 ? 'warning' : 'normal'}}">{{remainingHoursForFirst}} 小时（{{daysForFirst}} 天）</text>
              </block>
            </view>
          </view>
        </view>
        <view class="result-row">
          <view class="result-item">
            <text class="result-label">预计保养日期</text>
            <text class="result-value {{isFirstOverdue ? 'overdue' : 'normal'}}">{{estimatedDateFirst}}</text>
            <text class="result-status {{isFirstOverdue ? 'overdue' : ''}}" wx:if="{{isFirstOverdue}}">应立即保养</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 二级保养结果 -->
    <view class="module-box result-box second-maintenance" wx:if="{{showSecondResult}}">
      <view class="result-header">
        <view class="result-title">
          <text class="title-icon">⚙️</text>
          <text class="title-text">二级保养（每3000小时）</text>
        </view>
      </view>
      <view class="result-content">
        <view class="result-row">
          <view class="result-item">
            <text class="result-label">距离下次保养还需</text>
            <view class="result-value-container">
              <block wx:if="{{isSecondOverdue}}">
                <text class="result-value overdue">超 {{-remainingHoursForSecond}} 小时（{{daysForSecond}} 天）</text>
              </block>
              <block wx:else>
                <text class="result-value {{remainingHoursForSecond <= 48 ? 'warning' : 'normal'}}">{{remainingHoursForSecond}} 小时（{{daysForSecond}} 天）</text>
              </block>
            </view>
          </view>
        </view>
        <view class="result-row">
          <view class="result-item">
            <text class="result-label">预计保养日期</text>
            <text class="result-value {{isSecondOverdue ? 'overdue' : 'normal'}}">{{estimatedDateSecond}}</text>
            <text class="result-status {{isSecondOverdue ? 'overdue' : ''}}" wx:if="{{isSecondOverdue}}">应立即保养</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 免责声明 -->
  <view class="disclaimer">
    <text class="disclaimer-icon">ℹ️</text>
    <text class="disclaimer-text">此计算结果仅供参考，实际保养时间请以现场实际情况为准</text>
  </view>
</view>