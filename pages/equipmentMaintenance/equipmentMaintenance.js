Page({
  data: {
    firstRunningHours: '', // 一保运行小时数
    secondRunningHours: '', // 二保运行小时数
    dailyRunningHours: 24, // 每天平均运行小时数，默认为24小时
    showResults: false, // 是否显示结果
    showFirstResult: false, // 是否显示一保结果
    showSecondResult: false, // 是否显示二保结果
    remainingHoursForFirst: 0, // 距离下次一保的剩余小时数
    daysForFirst: 0, // 距离下次一保的剩余天数
    isFirstOverdue: false, // 一级保养是否超时
    remainingHoursForSecond: 0, // 距离下次二保的剩余小时数
    daysForSecond: 0, // 距离下次二保的剩余天数
    isSecondOverdue: false, // 二级保养是否超时
    estimatedDateFirst: '', // 预计一保日期
    estimatedDateSecond: '' // 预计二保日期
  },

  // 监听一保运行小时数输入变化
  onFirstInputChange: function(e) {
    let value = e.detail.value;
    
    // 使用正则表达式限制只能输入数字和最多一个小数点
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多一位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 1) {
        value = parts[0] + '.' + parts[1].substring(0, 1);
      }
    }
    
    // 检查输入是否大于等于2000
    if (value && parseFloat(value) >= 2000) {
      wx.showToast({
        title: '一保运行时间必须小于2000小时',
        icon: 'none',
        duration: 2000
      });
      this.setData({
        firstRunningHours: ''
      });
      return;
    }
    
    this.setData({
      firstRunningHours: value
    });
  },

  // 监听二保运行小时数输入变化
  onSecondInputChange: function(e) {
    let value = e.detail.value;
    
    // 使用正则表达式限制只能输入数字和最多一个小数点
    // 首先移除所有非数字和非小数点字符
    value = value.replace(/[^\d.]/g, '');
    
    // 处理多个小数点的情况，只保留第一个
    if (value.split('.').length > 2) {
      const parts = value.split('.');
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多一位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1].length > 1) {
        value = parts[0] + '.' + parts[1].substring(0, 1);
      }
    }
    
    // 检查输入是否大于等于6000
    if (value && parseFloat(value) >= 6000) {
      wx.showToast({
        title: '二保运行时间必须小于6000小时',
        icon: 'none',
        duration: 2000
      });
      this.setData({
        secondRunningHours: ''
      });
      return;
    }
    
    this.setData({
      secondRunningHours: value
    });
  },

  // 计算保养时间
  calculateMaintenance: function() {
    // 获取输入的运行小时数
    let firstHours = parseFloat(this.data.firstRunningHours);
    let secondHours = parseFloat(this.data.secondRunningHours);
    
    // 检查是否至少有一个有效输入
    if ((isNaN(firstHours) || firstHours < 0) && (isNaN(secondHours) || secondHours < 0)) {
      wx.showToast({
        title: '请至少输入一个有效的运行小时数',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 设置是否显示各个结果部分
    const showFirstResult = !isNaN(firstHours) && firstHours >= 0;
    const showSecondResult = !isNaN(secondHours) && secondHours >= 0;
    
    // 计算一级保养（每1000小时）
    if (showFirstResult) {
      this.calculateFirstMaintenance(firstHours);
    }
    
    // 计算二级保养（每3000小时）
    if (showSecondResult) {
      this.calculateSecondMaintenance(secondHours);
    }
    
    // 显示结果
    this.setData({
      showResults: true,
      showFirstResult,
      showSecondResult
    });

    // 自动计算预计日期
    this.calculateEstimatedDates();
  },

  // 计算一级保养
  calculateFirstMaintenance: function(hours) {
    const firstMaintenanceInterval = 1000; // 一保间隔
    let isFirstOverdue = false;
    let remainingHoursForFirst;
    
    if (hours === 0) {
      // 刚开始使用，距离保养还有整个周期
      remainingHoursForFirst = firstMaintenanceInterval;
      isFirstOverdue = false;
    } else if (hours % firstMaintenanceInterval === 0) {
      // 刚好是保养点
      remainingHoursForFirst = 0;
      isFirstOverdue = false;
    } else if (hours > firstMaintenanceInterval) {
      // 超过了首次保养
      isFirstOverdue = true;
      
      // 计算超出时间
      const excessHours = hours % firstMaintenanceInterval;
      remainingHoursForFirst = -excessHours;
    } else {
      // 首次保养前
      remainingHoursForFirst = firstMaintenanceInterval - hours;
      isFirstOverdue = false;
    }
    
    const daysForFirst = Math.ceil(Math.abs(remainingHoursForFirst) / this.data.dailyRunningHours);

    // 设置一保相关数据
    this.setData({
      remainingHoursForFirst,
      daysForFirst,
      isFirstOverdue
    });
  },

  // 计算二级保养
  calculateSecondMaintenance: function(hours) {
    const secondMaintenanceInterval = 3000; // 二保间隔
    let isSecondOverdue = false;
    let remainingHoursForSecond;
    
    if (hours === 0) {
      // 刚开始使用，距离保养还有整个周期
      remainingHoursForSecond = secondMaintenanceInterval;
      isSecondOverdue = false;
    } else if (hours % secondMaintenanceInterval === 0) {
      // 刚好是保养点
      remainingHoursForSecond = 0;
      isSecondOverdue = false;
    } else if (hours > secondMaintenanceInterval) {
      // 超过了首次保养
      isSecondOverdue = true;
      
      // 计算超出时间
      const excessHours = hours % secondMaintenanceInterval;
      remainingHoursForSecond = -excessHours;
    } else {
      // 首次保养前
      remainingHoursForSecond = secondMaintenanceInterval - hours;
      isSecondOverdue = false;
    }
    
    const daysForSecond = Math.ceil(Math.abs(remainingHoursForSecond) / this.data.dailyRunningHours);

    // 设置二保相关数据
    this.setData({
      remainingHoursForSecond,
      daysForSecond,
      isSecondOverdue
    });
  },

  // 计算预计保养日期
  calculateEstimatedDates: function() {
    // 获取当前日期
    const today = new Date();
    
    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    // 计算并设置日期
    let firstMaintenanceDate, secondMaintenanceDate;
    
    if (this.data.showFirstResult) {
      if (this.data.isFirstOverdue) {
        // 如果已经超时，保养日期设为当天
        firstMaintenanceDate = today;
      } else {
        // 否则计算预计日期
        firstMaintenanceDate = new Date(today);
        firstMaintenanceDate.setDate(today.getDate() + this.data.daysForFirst);
      }
      
      this.setData({
        estimatedDateFirst: formatDate(firstMaintenanceDate)
      });
    }
    
    if (this.data.showSecondResult) {
      if (this.data.isSecondOverdue) {
        // 如果已经超时，保养日期设为当天
        secondMaintenanceDate = today;
      } else {
        // 否则计算预计日期
        secondMaintenanceDate = new Date(today);
        secondMaintenanceDate.setDate(today.getDate() + this.data.daysForSecond);
      }
      
      this.setData({
        estimatedDateSecond: formatDate(secondMaintenanceDate)
      });
    }
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-设备保养时间计算',
      path: '/pages/equipmentMaintenance/equipmentMaintenance'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '平台常用计算工具-设备保养时间计算',
      query: 'from=timeline'
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 页面加载时执行的逻辑
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
}) 