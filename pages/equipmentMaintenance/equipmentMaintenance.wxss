/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0 0 80rpx 0;
}

/* 表单头部 */
.module-header {
  padding: 24rpx 24rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.picker-container {
  display: inline-block;
  text-align: center;
  width: 100%;
}

.picker {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  letter-spacing: 0.3rpx;
  justify-content: center;
}

/* 模块卡片样式 */
.module-box {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  margin: 16rpx 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 表单内容 */
.input-section {
  padding: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.input-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.input-group {
  flex: 1;
}

.input-label {
  display: block;
  margin-bottom: 8rpx;
  color: #475569;
  font-size: 24rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.input-field {
  width: 100%;
  height: 72rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-sizing: border-box;
  font-size: 26rpx;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
}

.input-field:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.08);
}

/* 计算按钮 */
.calculate-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 16rpx;
  margin-top: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.25);
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.3rpx;
}

.calculate-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 结果区域样式 */
.result-section {
  margin-bottom: 20rpx;
}

/* 一级保养样式 - 蓝色主题 */
.result-box.first-maintenance {
  border-left: 6rpx solid #3b82f6;
}

.result-box.first-maintenance::before {
  content: '🔧';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

/* 二级保养样式 - 浅红色主题 */
.result-box.second-maintenance {
  border-left: 6rpx solid #dc2626;
}

.result-box.second-maintenance::before {
  content: '⚙️';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

/* 结果头部 */
.result-header {
  padding: 20rpx 24rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
}

.result-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

/* 一级保养头部 - 蓝色主题 */
.first-maintenance .result-header {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.first-maintenance .result-header::after {
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.2) 50%, transparent 100%);
}

.first-maintenance .title-text {
  color: #1e40af;
}

/* 二级保养头部 - 浅红色主题 */
.second-maintenance .result-header {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.second-maintenance .result-header::after {
  background: linear-gradient(90deg, transparent 0%, rgba(220, 38, 38, 0.2) 50%, transparent 100%);
}

.second-maintenance .title-text {
  color: #991b1b;
}

.result-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.title-icon {
  font-size: 24rpx;
}

.title-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

/* 结果内容 */
.result-content {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.result-row {
  margin-bottom: 16rpx;
}

.result-row:last-child {
  margin-bottom: 0;
}

.result-item {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 一级保养内容区域 */
.first-maintenance .result-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.first-maintenance .result-item {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1rpx solid #bae6fd;
}

/* 二级保养内容区域 */
.second-maintenance .result-content {
  background: linear-gradient(135deg, #ffffff 0%, #fef7f7 100%);
}

.second-maintenance .result-item {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1rpx solid #fca5a5;
}

.result-label {
  display: block;
  color: #64748b;
  font-size: 22rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  margin-bottom: 8rpx;
}

.result-value-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.result-value {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
  line-height: 1.3;
}

.result-status {
  display: inline-block;
  font-size: 20rpx;
  font-weight: 600;
  margin-left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  text-align: center;
}

/* 状态颜色 */
.normal {
  color: #059669;
}

.warning {
  color: #d97706;
}

.overdue {
  color: #dc2626;
}

.result-status.overdue {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

/* 免责声明 */
.disclaimer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin: 20rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow: inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.disclaimer-icon {
  font-size: 20rpx;
  opacity: 0.7;
}

.disclaimer-text {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  line-height: 1.4;
  text-align: center;
}