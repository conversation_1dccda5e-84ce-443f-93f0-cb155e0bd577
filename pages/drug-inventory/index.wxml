<!-- index.wxml -->
<view class="container">
  <!-- 药剂库存汇总模块（移至顶部） -->
  <view class="module-box summary">
  <view class="module-header summary-header">
    <text class="picker title-center">📊 药剂库存汇总</text>
  </view>
  <view class="summary-section">
    <view class="summary-container">
      <!-- 修改布局结构为三行，每行3个项目 -->
      <view class="summary-row">
        <view class="summary-item" wx:for="{{summaryList}}" wx:key="name"
              wx:if="{{index < 3}}">  <!-- 前3项显示在第一行 -->
          <text class="summary-name">{{item.name}} ：</text>
          <text class="summary-value">{{item.value}}t<text wx:if="{{item.remainingDays}}">({{item.remainingDays}}天)</text></text>
        </view>
      </view>
      <view class="summary-row">
        <view class="summary-item" wx:for="{{summaryList}}" wx:key="name"
              wx:if="{{index >= 3 && index < 5}}">  <!-- 第4-5项显示在第二行 -->
          <text class="summary-name">{{item.name}} ：</text>
          <text class="summary-value">{{item.value}}t<text wx:if="{{item.remainingDays}}">({{item.remainingDays}}天)</text></text>
        </view>
        <!-- 第二行最后一个位置放置按钮 -->
        <view class="summary-item btn-container">
          <view class="btn-group">
            <button class="share-btn" bindtap="shareSummaryImage">📤 分享</button>
            <button class="copy-btn" bindtap="copySummary">📋 复制</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

  <!-- 四个药剂计算模块 -->
  <view wx:for="{{modules}}" wx:key="index" class="module-box">
    <view class="module-header">
      <picker mode="selector" range="{{pickerList}}" value="{{pickerList.indexOf(item.type)}}" 
              bindchange="bindPickerChange" data-index="{{index}}" class="picker-container">
        <view class="picker">{{item.type}} ▼</view>
      </picker>
    </view>

    <view class="input-section">
      <!-- 吨桶输入 -->
      <view class="input-row">
        <view class="input-group with-result">
          <text class="input-label">1t吨桶数量：</text>
          <view class="input-wrapper">
            <input type="number" placeholder="0" value="{{item.input1}}" 
                   data-field="input1" data-index="{{index}}" 
                   bindinput="handleInput" class="input-field"/>
            <text class="inline-result">{{item.result.tank1}}t</text>
          </view>
        </view>
        <view class="input-group with-result">
          <text class="input-label">2t吨桶数量：</text>
          <view class="input-wrapper">
            <input type="number" placeholder="0" value="{{item.input2}}" 
                   data-field="input2" data-index="{{index}}" 
                   bindinput="handleInput" class="input-field"/>
            <text class="inline-result">{{item.result.tank2}}t</text>
          </view>
        </view>
      </view>

      <!-- 修改后的液位输入 -->
      <view class="input-row">
        <view class="input-group with-result">
          <text class="input-label">旧药剂罐液位(m)：</text>
          <view class="input-wrapper">
            <input type="digit" placeholder="0.00" value="{{item.oldLevel}}" 
                   data-field="oldLevel" data-index="{{index}}" 
                   bindinput="handleInput" class="input-field"/>
            <text class="inline-result">{{item.result.oldTank}}t</text>
          </view>
        </view>
        <view class="input-group with-result">
          <text class="input-label">新药剂罐液位(m)：</text>
          <view class="input-wrapper">
            <input type="digit" placeholder="0.00" value="{{item.newLevel}}" 
                   data-field="newLevel" data-index="{{index}}" 
                   bindinput="handleInput" class="input-field"/>
            <text class="inline-result">{{item.result.newTank}}t</text>
          </view>
        </view>
      </view>

      <!-- 新增日加药量输入与减去储罐底部选项放在同一行 -->
      <view class="input-row">
        <view class="input-group">
          <text class="input-label">日加药量(L/D)：</text>
          <view class="input-wrapper">
            <input type="digit" placeholder="0.00" value="{{item.dailyDosage}}" 
                   data-field="dailyDosage" data-index="{{index}}" 
                   bindinput="handleInput" class="input-field"/>
          </view>
        </view>
        <view class="input-group checkbox-container">
          <checkbox-group bindchange="handleSubtractBottomChange">
            <label class="bottom-checkbox">
              <checkbox value="1" checked="{{item.subtractBottom}}"/>
              <text>减去储罐底部0.1m</text>
            </label>
          </checkbox-group>
        </view>
      </view>
    </view>

    <!-- 总库存显示 -->
    <view class="total-row">
      <text class="total-label">总库存：</text>
      <text class="total-value">{{item.result.total}}t<text wx:if="{{item.result.remainingDays}}">({{item.result.remainingDays}}天)</text></text>
    </view>
  </view>

  <!-- 消泡剂模块 -->
  <view class="module-box">
  <view class="module-header">
    <text class="picker">🧪 消泡剂计算（原吨桶）</text>
  </view>
    <view class="input-section">
      <view class="input-row">
        <view class="input-group">
          <text class="input-label">下降高度(cm)：</text>
          <input type="digit" placeholder="0.00" value="{{dropHeight}}" 
                 data-field="dropHeight" bindinput="handleInput" class="input-field"/>
        </view>
        <view class="input-group">
          <text class="input-label">加药量(L/d)：</text>
          <input type="digit" placeholder="0.00" value="{{antifoamDosage}}" 
                 data-field="antifoamDosage" bindinput="handleInput" class="input-field"/>
        </view>
      </view>
    </view>
    <view class="result-row">
      <view class="result-item blue-bg">
        <text class="result-label">消泡剂用量</text>
        <text class="result-value">{{antifoamResult.antifoamVolume}}L</text>
      </view>
      <view class="result-item blue-bg">
        <text class="result-label">下降高度</text>
        <text class="result-value">{{antifoamResult.newDropHeight}}cm</text>
      </view>
    </view>
  </view>
</view>

<canvas canvas-id="shareCanvas" id="shareCanvas" type="2d" style="width: 700px; height: 800px; position: fixed; left: -9999px; top: -9999px;" hidden></canvas>