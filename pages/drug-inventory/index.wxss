/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0 0 80rpx 0;
}

/* 表单头部 */
.module-header {
  padding: 24rpx 24rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

.picker-container {
  display: inline-block;
  text-align: center;
  width: 100%;
}

.picker {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  letter-spacing: 0.3rpx;
  justify-content: center;
}

/* 表单内容 */
.input-section {
  padding: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.input-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.input-row:last-child {
  margin-bottom: 0;
}

.input-group {
  flex: 1;
}

.input-label {
  display: block;
  margin-bottom: 8rpx;
  color: #475569;
  font-size: 24rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.input-field {
  width: 100%;
  height: 72rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-sizing: border-box;
  font-size: 26rpx;
  color: #1e293b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    inset 0 1rpx 2rpx rgba(0, 0, 0, 0.03),
    0 1rpx 2rpx rgba(0, 0, 0, 0.03);
}

.input-field:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  outline: none;
  box-shadow:
    0 0 0 4rpx rgba(59, 130, 246, 0.08),
    inset 0 1rpx 2rpx rgba(0, 0, 0, 0.03),
    0 2rpx 8rpx rgba(59, 130, 246, 0.12);
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.input-wrapper .input-field {
  flex: 1;
}

.inline-result {
  color: #3b82f6 !important;
  font-size: 26rpx;
  font-weight: 600;
  min-width: 80rpx;
  text-align: right;
  letter-spacing: 0.3rpx;
}

/* 总库存结果样式 */
.total-row {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin: 0 20rpx 16rpx;
  box-shadow:
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #e2e8f0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
}

.total-label {
  color: #64748b;
  font-size: 24rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.total-value {
  color: #1e293b;
  font-size: 28rpx;
  font-weight: 700;
  letter-spacing: 0.3rpx;
}

/* 消泡剂结果样式 */
.result-row {
  display: flex;
  gap: 12rpx;
  margin: 0 20rpx 16rpx;
}

.result-item.blue-bg {
  flex: 1;
  padding: 16rpx 12rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  text-align: center;
  border: 1rpx solid #e2e8f0;
  box-shadow:
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8),
    0 1rpx 2rpx rgba(0, 0, 0, 0.04);
}

.result-label {
  color: #64748b;
  font-size: 20rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  text-transform: uppercase;
  opacity: 0.8;
  display: block;
  margin-bottom: 6rpx;
}

.result-value {
  color: #1e293b;
  font-size: 26rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
  display: block;
}

/* 药剂卡片样式 */
.module-box {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.06),
    0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  margin: 16rpx 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 根据药剂类型添加左边框颜色和状态指示 */
.module-box:nth-child(2) {
  border-left: 6rpx solid #007AFF;
}

.module-box:nth-child(2)::before {
  content: '🧪';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

.module-box:nth-child(3) {
  border-left: 6rpx solid #34C759;
}

.module-box:nth-child(3)::before {
  content: '⚗️';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

.module-box:nth-child(4) {
  border-left: 6rpx solid #FF9500;
}

.module-box:nth-child(4)::before {
  content: '🔬';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

.module-box:nth-child(5) {
  border-left: 6rpx solid #FF3B30;
}

.module-box:nth-child(5)::before {
  content: '🛡️';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

.module-box:nth-child(6) {
  border-left: 6rpx solid #8E44AD;
}

.module-box:nth-child(6)::before {
  content: '💧';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

/* 汇总区域样式 */
.summary-section {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}



.summary-row {
  display: flex;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-item {
  flex: 1;
  width: calc(33.333% - 8rpx);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  border: 1rpx solid #e2e8f0;
  box-shadow:
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8),
    0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 68rpx;
  min-height: 68rpx;
  max-height: 68rpx;
}

/* 按钮容器样式 */
.summary-item.btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  box-shadow: none;
  height: 68rpx;
  min-height: 68rpx;
  max-height: 68rpx;
  padding: 16rpx 12rpx;
  flex-direction: column;
}

.summary-name {
  color: #64748b;
  font-size: 20rpx;
  font-weight: 500;
  letter-spacing: 0.2rpx;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.summary-value {
  color: #1e293b;
  font-size: 24rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
  display: block;
  margin-top: 6rpx;
}

/* 汇总头部样式 */
.summary-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 24rpx 16rpx !important;
}

.title-center {
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3rpx;
}

.btn-group {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2rpx 0;
}

/* 重置微信小程序button默认样式 */
.btn-group button {
  all: unset !important;
}

.btn-group button::after {
  border: none !important;
}

.btn-group .copy-btn,
.btn-group .share-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  padding: 0 !important;
  border-radius: 10rpx !important;
  font-size: 16rpx !important;
  font-weight: 500 !important;
  line-height: 40rpx !important;
  white-space: nowrap !important;
  box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.2) !important;
  transition: all 0.3s ease !important;
  border: none !important;
  width: 90rpx !important;
  height: 40rpx !important;
  text-align: center !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

.btn-group .share-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  box-shadow: 0 2rpx 6rpx rgba(16, 185, 129, 0.2) !important;
}

.btn-group .copy-btn:active,
.btn-group .share-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 复选框样式 */
.checkbox-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: 12rpx;
}

.bottom-checkbox {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.bottom-checkbox text {
  margin-left: 6rpx;
}

checkbox {
  transform: scale(0.85);
  accent-color: #3b82f6;
}


