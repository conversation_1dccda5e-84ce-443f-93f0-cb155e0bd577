// index.js
Page({
  data: {
    modules: [
      { 
        type: '破乳剂',
        input1: '',
        input2: '',
        oldLevel: '',
        newLevel: '',
        result: { total: '0.00', tank1: '0.00', tank2: '0.00', oldTank: '0.00', newTank: '0.00' },
        subtractBottom: true,
        dailyDosage: ''
      },
      { 
        type: '反相破乳剂',
        input1: '',
        input2: '',
        oldLevel: '',
        newLevel: '',
        result: { total: '0.00', tank1: '0.00', tank2: '0.00', oldTank: '0.00', newTank: '0.00' },
        subtractBottom: true,
        dailyDosage: ''
      },
      { 
        type: '杀菌剂',
        input1: '',
        input2: '',
        oldLevel: '',
        newLevel: '',
        result: { total: '0.00', tank1: '0.00', tank2: '0.00', oldTank: '0.00', newTank: '0.00' },
        subtractBottom: true,
        dailyDosage: ''
      },
      { 
        type: '缓蚀剂',
        input1: '',
        input2: '',
        oldLevel: '',
        newLevel: '',
        result: { total: '0.00', tank1: '0.00', tank2: '0.00', oldTank: '0.00', newTank: '0.00' },
        subtractBottom: true,
        dailyDosage: ''
      },
      // 新增第五个模块
      { 
        type: '消泡剂',
        input1: '',
        input2: '',
        oldLevel: '',
        newLevel: '',
        result: { total: '0.00', tank1: '0.00', tank2: '0.00', oldTank: '0.00', newTank: '0.00' },
        subtractBottom: true,
        dailyDosage: ''
      }
    ],
    dropHeight: '',
    antifoamDosage: '',
    antifoamResult: {
      antifoamVolume: '0.00',
      newDropHeight: '0.00'
    },
    pickerList: ['破乳剂', '反相破乳剂', '杀菌剂', '缓蚀剂', '消泡剂', '阻垢剂'],
    summaryList: []
  },

  onLoad() {
    this.setData({
      modules: this.data.modules.map((m, i) => ({
        ...m,
        type: this.data.pickerList[i]
      }))
    }, () => {
      this.updateSummary();
    });
  },

  handleInput(e) {
    const { field, index } = e.currentTarget.dataset;
    let value = e.detail.value;
    
    // 消泡剂字段处理
    if (field === 'dropHeight' || field === 'antifoamDosage') {
      value = value.replace(/[^0-9.]/g, '');
      this.setData({ [field]: value }, () => {
        this.calculateAntifoam();
      });
      return;
    }
    
    const modulePath = `modules[${index}]`;

    // 吨桶处理
    if (field.includes('input')) {
      value = value.replace(/[^0-9]/g, '');
      this.setData({
        [`${modulePath}.${field}`]: value
      }, () => this.calculateModule(index));
      return;
    }

    // 日加药量处理
    if (field === 'dailyDosage') {
      value = value.replace(/[^0-9.]/g, '');
      this.setData({
        [`${modulePath}.${field}`]: value
      }, () => this.calculateModule(index));
      return;
    }

    // 液位处理
    if (field.includes('Level')) {
      this.setData({
        [`${modulePath}.${field}`]: value
      }, () => {
        this.validateLevel(index, field, value);
        this.calculateModule(index);
      });
    }
  },

  bindPickerChange(e) {
    const { index } = e.currentTarget.dataset;
    const value = this.data.pickerList[e.detail.value];
    this.setData({
      [`modules[${index}].type`]: value
    }, () => {
      this.updateSummary();
    });
  },

  validateLevel(index, field, value) {
    if (value === '') return;

    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      wx.showToast({ title: '请输入有效数字', icon: 'none' });
      this.setData({ [`modules[${index}].${field}`]: '' });
      return;
    }

    if (field === 'oldLevel' && numValue > 2) {
      wx.showToast({ title: '旧加药撬最大液位2m', icon: 'none' });
      this.setData({ [`modules[${index}].${field}`]: '2' });
    }
    if (field === 'newLevel' && numValue > 2.7) {
      wx.showToast({ title: '新加药撬最大液位2.7m', icon: 'none' });
      this.setData({ [`modules[${index}].${field}`]: '2.7' });
    }
  },

  calculateModule(index) {
    const module = this.data.modules[index]; 
    // 关键修改点：拆分新旧截面积
    const oldCrossArea = 2.2 * 2.2;  // 旧加药撬保持原值
    const newCrossArea = 2.2 * 3;    // 新加药撬改为2.2*3

    const tank1 = (parseInt(module.input1)  || 0) * 1;
    const tank2 = (parseInt(module.input2)  || 0) * 2;
    
    let oldLv = Math.min(parseFloat(module.oldLevel)  || 0, 2);
    let newLv = Math.min(parseFloat(module.newLevel)  || 0, 2.7);
    if (module.subtractBottom) {
      oldLv = Math.max(0, oldLv - 0.1);
      newLv = Math.max(0, newLv - 0.1);
    }
    
    // 关键修改点：分别使用新旧截面积计算
    const oldTank = (oldCrossArea * oldLv).toFixed(2);
    const newTank = (newCrossArea * newLv).toFixed(2);
    
    const total = (tank1 + tank2 + parseFloat(oldTank) + parseFloat(newTank)).toFixed(2);
    
    // 计算剩余天数
    let remainingDays = '';
    const dailyDosage = parseFloat(module.dailyDosage) || 0;
    if (dailyDosage > 0) {
      // 将总库存(t)转换为L，假设密度为1kg/L
      const totalLiters = parseFloat(total) * 1000;
      remainingDays = (totalLiters / dailyDosage).toFixed(1);
    }
    
    this.setData({ 
      [`modules[${index}].result`]: {
        tank1: tank1.toFixed(2), 
        tank2: tank2.toFixed(2), 
        oldTank,
        newTank,
        total,
        remainingDays
      }
    }, () => {
      this.updateSummary(); 
    });
  },

  calculateAntifoam() {
    const { dropHeight, antifoamDosage } = this.data;
    
    // 消泡剂用量计算
    const heightMeter = (parseFloat(dropHeight) || 0) / 100;
    const antifoamVolume = (1.2 * 1 * heightMeter * 1000).toFixed(2);
    
    // 下降高度计算
    const newDropHeight = (parseFloat(antifoamDosage) || 0) / (1000 / 1.2) * 100;
    
    this.setData({
      antifoamResult: {
        antifoamVolume,
        newDropHeight: newDropHeight.toFixed(2)
      }
    });
  },

  updateSummary() {
    const summary = this.data.modules.map(module => {
      const dailyDosage = parseFloat(module.dailyDosage) || 0;
      return {
        name: module.type,
        value: module.result.total || '0.00',
        remainingDays: module.result.remainingDays || '',
        dailyDosage: dailyDosage > 0 ? module.dailyDosage : '',
        tank1Count: parseInt(module.input1) || 0,
        tank2Count: parseInt(module.input2) || 0
      };
    });
    this.setData({ summaryList: summary.slice(0,5) }); // 改为保留前5项
  },

// index.js 新增方法
copySummary() {
  const items = this.data.summaryList
    .slice(0, 5)
    .map((item, idx) => {
      // 获取吨桶信息
      const m = this.data.modules[idx];
      const t1Count = parseInt(m.input1) || 0;
      const t2Count = parseInt(m.input2) || 0;
      const totalCount = t1Count + t2Count;
      
      // 药剂罐信息
      const oldTank = parseFloat(m.result.oldTank) || 0;
      const newTank = parseFloat(m.result.newTank) || 0;
      const tankLevelSum = (oldTank + newTank).toFixed(2);
      
      // 检查是否有数据
      const totalValue = parseFloat(item.value) || 0;
      if (totalValue === 0) {
        return null; // 没有数据，返回null以便后面过滤
      }
      
      let tankInfo = '';
      if (totalCount > 0) {
        if (t1Count > 0 && t2Count > 0) {
          tankInfo = `${totalCount}桶(${t1Count}个1t桶,${t2Count}个2t桶)`;
        } else if (t1Count > 0) {
          tankInfo = `${t1Count}桶(1t桶)`;
        } else if (t2Count > 0) {
          tankInfo = `${t2Count}桶(2t桶)`;
        }
      }
      
      // 构建显示文本
      let text = `${item.name}`;
      
      // 添加加药量信息
      if (item.dailyDosage) {
        text += `(${item.dailyDosage}L/D)`;
      }
      
      text += `：`;
      
      // 添加吨桶和药剂罐信息
      if (tankInfo) {
        text += `${tankInfo}+药剂罐${tankLevelSum}t=${item.value}t`;
      } else {
        text += `药剂罐${tankLevelSum}t=${item.value}t`;
      }
      
      // 添加剩余天数
      if (item.remainingDays) {
        text += `(${item.remainingDays}天)`;
      }
      
      return text;
    })
    .filter(item => item !== null) // 过滤掉没有数据的项
    .join('、');

  // 如果没有任何药剂数据，则提示
  if (!items) {
    wx.showToast({ title: '没有药剂数据可复制', icon: 'none' });
    return;
  }

  // 添加备注信息
  const hasSubtractBottom = this.data.modules.some(m => m.subtractBottom);
  let text = `药剂库存统计：${items}`;
  if (hasSubtractBottom) {
    text += '。备注：液位计10cm以下的药剂都没有统计到库存内';
  }
  
  wx.setClipboardData({
    data: text,
    success: () => {
      wx.showToast({ title: '复制成功', icon: 'none' });
    }
  });
},
  
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-药剂库存计算器',
      path: '/pages/drug-inventory/index'
    };
  },

  onShareTimeline() {
    return {
      title: '平台常用计算工具-药剂库存计算器',
      query: 'from=timeline'
    };
  },

  // 勾选减去储罐底部库存事件
  handleSubtractBottomChange(e) {
    const checked = e.detail.value.length > 0;
    
    // 所有模块同步勾选状态
    const newModules = this.data.modules.map(module => ({
      ...module,
      subtractBottom: checked
    }));
    
    this.setData({
      modules: newModules
    }, () => {
      // 重新计算所有模块
      newModules.forEach((_, index) => {
        this.calculateModule(index);
      });
    });
  },

  // 汇总分享截图
  shareSummaryImage() {
    wx.showLoading({ title: '生成截图中...' });
    // 创建canvas节点
    const query = wx.createSelectorQuery();
    query.select('#shareCanvas').fields({ node: true, size: true }).exec((res) => {
      const canvas = res[0].node;
      const ctx = canvas.getContext('2d');
      // 过滤掉所有合计和总计都为0的药剂
      const filteredList = this.data.summaryList.map((item, idx) => {
        return {
          ...item,
          tankSum: this.getTankSum(idx),
          tankLevelSum: this.getTankLevelSum(idx),
          tankSumDisplay: this.getTankSumDisplay(idx)
        };
      }).filter(item => {
        return !(parseFloat(item.tankSum) === 0 && parseFloat(item.tankLevelSum) === 0 && parseFloat(item.value) === 0);
      });
      
      // 判断是否有加药量数据
      const hasDosageData = filteredList.some(item => item.dailyDosage);
      
      // 画布尺寸
      const width = 700;
      const rowHeight = 44;
      const headerHeight = 38;
      const baseY = 100;
      const tableRows = filteredList.length;
      // 根据是否有加药量调整表格列宽和表头
      let colWidths = [];
      let headers = [];
      
      if (hasDosageData) {
        // 调整列宽，减小部分列宽以确保表格不超出画布宽度
        colWidths = [40, 120, 90, 90, 90, 90, 90];
        headers = ['序号', '药剂名称', '加药量(L/D)', '吨桶合计', '药剂罐合计', '总计(t)', '剩余天数'];
      } else {
        colWidths = [60, 180, 120, 120, 120];
        headers = ['序号', '药剂名称', '吨桶合计', '药剂罐合计', '总计(t)'];
      }
      
      const height = baseY + headerHeight + tableRows * rowHeight + 100; // 增加高度以适应底部备注
      canvas.width = width;
      canvas.height = height;
      
      // 背景
      ctx.fillStyle = '#fff';
      ctx.fillRect(0, 0, width, height);
      // 标题
      ctx.fillStyle = '#222';
      ctx.font = 'bold 30px sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('药剂库存汇总', width / 2, 50);
      // 统计时间
      ctx.font = '16px sans-serif';
      ctx.fillStyle = '#444';
      const now = new Date();
      const dateStr = `${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')} ${String(now.getHours()).padStart(2,'0')}:${String(now.getMinutes()).padStart(2,'0')}`;
      ctx.textAlign = 'left';
      ctx.fillText(`统计时间：${dateStr}`, 40, 85);
      // 表头底色
      ctx.fillStyle = '#f3f6fa';
      ctx.fillRect(40, baseY, colWidths.reduce((a,b)=>a+b,0), headerHeight);
      // 表头文字
      let x = 40;
      ctx.font = 'bold 18px sans-serif';
      ctx.fillStyle = '#222';
      ctx.textAlign = 'center';
      for (let i = 0; i < headers.length; i++) {
        ctx.fillText(headers[i], x + colWidths[i]/2, baseY + headerHeight/2 + 1);
        x += colWidths[i];
      }
      // 表头下分隔线
      ctx.strokeStyle = '#bbb';
      ctx.beginPath();
      ctx.moveTo(40, baseY + headerHeight);
      ctx.lineTo(40 + colWidths.reduce((a,b)=>a+b,0), baseY + headerHeight);
      ctx.stroke();
      // 表格数据
      ctx.font = '16px sans-serif';
      for (let rowIdx = 0; rowIdx < tableRows; rowIdx++) {
        const item = filteredList[rowIdx];
        const y = baseY + headerHeight + rowIdx * rowHeight;
        ctx.fillStyle = rowIdx % 2 === 0 ? '#fff' : '#f7f9fc';
        ctx.fillRect(40, y, colWidths.reduce((a,b)=>a+b,0), rowHeight);
        ctx.fillStyle = '#222';
        
        if (hasDosageData) {
          // 有加药量的表格布局
          let x = 40;
          // 序号
          ctx.fillText(String(rowIdx+1), x + colWidths[0]/2, y + rowHeight/2 + 2);
          x += colWidths[0];
          // 药剂名称
          ctx.fillText(item.name, x + colWidths[1]/2, y + rowHeight/2 + 2);
          x += colWidths[1];
          // 加药量
          ctx.fillText(item.dailyDosage || '-', x + colWidths[2]/2, y + rowHeight/2 + 2);
          x += colWidths[2];
          // 吨桶合计
          ctx.fillText(item.tankSumDisplay || item.tankSum, x + colWidths[3]/2, y + rowHeight/2 + 2);
          x += colWidths[3];
          // 药剂罐合计
          ctx.fillText(item.tankLevelSum + 't', x + colWidths[4]/2, y + rowHeight/2 + 2);
          x += colWidths[4];
          // 总计
          ctx.fillText(item.value + 't', x + colWidths[5]/2, y + rowHeight/2 + 2);
          x += colWidths[5];
          // 剩余天数
          ctx.fillText(item.remainingDays ? item.remainingDays + '天' : '-', x + colWidths[6]/2, y + rowHeight/2 + 2);
        } else {
          // 无加药量的表格布局
          let x = 40;
          // 序号
          ctx.fillText(String(rowIdx+1), x + colWidths[0]/2, y + rowHeight/2 + 2);
          x += colWidths[0];
          // 药剂名称
          ctx.fillText(item.name, x + colWidths[1]/2, y + rowHeight/2 + 2);
          x += colWidths[1];
          // 吨桶合计
          ctx.fillText(item.tankSumDisplay || item.tankSum, x + colWidths[2]/2, y + rowHeight/2 + 2);
          x += colWidths[2];
          // 药剂罐合计
          ctx.fillText(item.tankLevelSum + 't', x + colWidths[3]/2, y + rowHeight/2 + 2);
          x += colWidths[3];
          // 总计
          ctx.fillText(item.value + 't', x + colWidths[4]/2, y + rowHeight/2 + 2);
        }
      }
      // 边框
      ctx.strokeStyle = '#bbb';
      ctx.lineWidth = 1.2;
      ctx.strokeRect(40, baseY, colWidths.reduce((a,b)=>a+b,0), headerHeight + tableRows * rowHeight);
      
      // 检查是否需要添加底部备注
      const hasSubtractBottom = this.data.modules.some(m => m.subtractBottom);
      if (hasSubtractBottom) {
        ctx.fillStyle = '#555';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('备注：液位计10cm以下的药剂都没有统计到库存内', width / 2, baseY + headerHeight + tableRows * rowHeight + 40);
      }
      
      // 导出图片
      wx.canvasToTempFilePath({
        canvas,
        success: (res) => {
          wx.hideLoading();
          wx.showShareImageMenu({ path: res.tempFilePath });
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({ title: '生成图片失败', icon: 'none' });
        }
      });
    });
  },
  // 获取吨桶合计
  getTankSum(idx) {
    const m = this.data.modules[idx];
    const t1 = parseFloat(m.result.tank1) || 0;
    const t2 = parseFloat(m.result.tank2) || 0;
    return (t1 + t2).toFixed(2);
  },
  // 获取药剂罐合计
  getTankLevelSum(idx) {
    const m = this.data.modules[idx];
    const oldTank = parseFloat(m.result.oldTank) || 0;
    const newTank = parseFloat(m.result.newTank) || 0;
    return (oldTank + newTank).toFixed(2);
  },
  // 获取吨桶合计显示文本（包含桶数和类型）
  getTankSumDisplay(idx) {
    const m = this.data.modules[idx];
    const t1Count = parseInt(m.input1) || 0;
    const t2Count = parseInt(m.input2) || 0;
    const totalCount = t1Count + t2Count;
    
    if (totalCount === 0) {
      return '0';
    }
    
    // 显示桶的类型而不是总吨数
    if (t1Count > 0 && t2Count > 0) {
      return `${totalCount}桶(${t1Count}个1t桶,${t2Count}个2t桶)`;
    } else if (t1Count > 0) {
      return `${t1Count}桶(1t桶)`;
    } else if (t2Count > 0) {
      return `${t2Count}桶(2t桶)`;
    } else {
      return '0';
    }
  }
});