page {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  -webkit-tap-highlight-color: transparent;
}

/* 重置所有view的默认点击效果 */
view {
  -webkit-tap-highlight-color: transparent;
}

.container {
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 顶部滚动提示 */
.notice-container {
  width: calc(100% - 30rpx);
  height: 70rpx;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  border-radius: 16rpx;
  margin: 15rpx 15rpx 0 15rpx;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  z-index: 1;
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.08),
    0 1rpx 4rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.notice-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
  position: relative;
  padding: 0 20rpx;
  padding-right: 60rpx; /* 为关闭按钮留出空间 */
  height: 100%;
  animation: scrollNotice 15s linear infinite;
  animation-delay: 0s; /* 无延迟立即开始 */
  transform: translateX(0%); /* 从开始位置开始，不是屏幕外 */
}

.notice-text {
  font-size: 24rpx;
  color: #2980b9;
}

.notice-close {
  position: absolute;
  right: 0;
  top: 0;
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(71, 85, 105, 0.7);
  font-size: 32rpx;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  z-index: 2;
}

@keyframes scrollNotice {
  0%, 10% {
    transform: translateX(0%);
  }
  90%, 100% {
    transform: translateX(-100%);
  }
}

/* 分类容器 - Apple风格毛玻璃效果 */
.category-container {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  margin: 32rpx;
  padding: 32rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 8rpx 24rpx rgba(71, 85, 105, 0.08),
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.category-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(248, 250, 252, 0.1) 50%,
    rgba(255, 255, 255, 0.15) 100%);
  border-radius: 28rpx;
  pointer-events: none;
  z-index: -1;
}

/* 分类头部 */
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  margin: 0;
  position: relative;
}

/* 设置按钮 */
.settings-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(59, 130, 246, 0.1);
  border: 1rpx solid rgba(59, 130, 246, 0.2);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.settings-btn:active {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(0.95);
}

.settings-icon {
  font-size: 24rpx;
  margin-right: 6rpx;
}

.settings-text {
  font-size: 24rpx;
  color: rgba(59, 130, 246, 0.8);
  font-weight: 500;
}

/* 工具网格 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 0;
  position: relative;
  z-index: 2;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 20rpx;
}

/* 重置所有可能的点击效果 */
.tool-item {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 自定义hover效果类 */
.tool-item-hover {
  background: rgba(0, 0, 0, 0.05) !important;
  transform: scale(0.98) !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 确保图标不会有独立的点击效果 */
.tool-icon {
  pointer-events: none;
}

.tool-icon image {
  pointer-events: none;
}



.tool-icon {
  width: 110rpx;
  height: 110rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    0 1rpx 4rpx rgba(71, 85, 105, 0.03),
    inset 0 1rpx 0 rgba(255,255,255,0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.4);
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  position: relative;
  overflow: hidden;
}





.icon-emoji {
  font-size: 44rpx;
  line-height: 1;
}

.icon-image {
  width: 72rpx;
  height: 72rpx;
  border-radius: 18rpx;
}

.tool-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  font-weight: 400;
  line-height: 1.2;
  margin-top: 8rpx;
}

/* 图标容器样式 - 不同类别使用不同的淡色底色 */

/* 化验及药剂相关工具 - 统一使用淡蓝色系 */
.oil-gradient, .suspended-gradient, .bacteria-gradient, .calibration-gradient, .concentration-gradient, .inventory-gradient {
  background: linear-gradient(145deg, #f0f8ff, #e6f3ff);
  border: 1rpx solid rgba(59, 130, 246, 0.08);
}

/* 工作管理工具 - 统一使用淡橙色系 */
.schedule-gradient, .fee-gradient, .jintie-gradient, .pension-gradient {
  background: linear-gradient(145deg, #fff7ed, #ffedd5);
  border: 1rpx solid rgba(249, 115, 22, 0.08);
}

/* 技术计算工具 - 淡青色系 */
.analog-gradient, .maintenance-gradient, .separator-gradient {
  background: linear-gradient(145deg, #f0fdfa, #e6fffa);
  border: 1rpx solid rgba(20, 184, 166, 0.08);
}

/* 查询工具 - 淡红色系 */
.tag-gradient, .pipe-gradient, .symbol-gradient, .alarm-gradient, .pressure-gradient, .vibration-gradient, .explosion-gradient, .risk-gradient {
  background: linear-gradient(145deg, #fef2f2, #fee2e2);
  border: 1rpx solid rgba(239, 68, 68, 0.08);
}

/* 实用工具 - 淡灰色系 */
.bmi-gradient, .photo-gradient, .more-gradient {
  background: linear-gradient(145deg, #f9fafb, #f3f4f6);
  border: 1rpx solid rgba(107, 114, 128, 0.08);
}

/* 底部信息 */
.bottom-info {
  text-align: center;
  padding: 48rpx 32rpx;
  position: relative;
  z-index: 1;
}

.info-text {
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.8);
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 免责声明 */
.disclaimer {
  display: inline-block;
}

.disclaimer text {
  color: rgba(100, 116, 139, 0.8);
  font-size: 22rpx;
  padding: 12rpx 32rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.4);
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

/* 即将推出样式 */
.coming-soon {
  opacity: 0.85;
}

.coming-soon .tool-text {
  color: #999;
}