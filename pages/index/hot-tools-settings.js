// 热门工具设置页面
Page({
  data: {
    selectedTools: [], // 已选择的工具
    selectedToolIds: [], // 已选择的工具ID列表
    // 工具分类
    chemicalTools: [],
    workTools: [],
    calculationTools: [],
    queryTools: [],
    utilityTools: [],
    // 所有工具配置
    allTools: [
      {
        id: 'oil-calculator',
        name: '含油计算器',
        icon: '/images/zhuye/hy_oil.png',
        iconClass: 'oil-gradient',
        page: 'oil-calculator',
        category: 'chemical'
      },
      {
        id: 'drug-concentration',
        name: '药剂浓度计算器',
        icon: '/images/zhuye/yjnd.png',
        iconClass: 'concentration-gradient',
        page: 'drug-concentration',
        category: 'chemical'
      },
      {
        id: 'drug-inventory',
        name: '药剂库存计算',
        icon: '/images/zhuye/yjkc.png',
        iconClass: 'inventory-gradient',
        page: 'drug-inventory',
        category: 'chemical'
      },
      {
        id: 'suspended-calculator',
        name: '悬浮物计算器',
        icon: '/images/zhuye/hy_suspended.png',
        iconClass: 'suspended-gradient',
        page: 'suspended-calculator',
        category: 'chemical'
      },
      {
        id: 'triple-bacteria',
        name: '三菌计算器',
        icon: '/images/zhuye/sjjsq.png',
        iconClass: 'bacteria-gradient',
        page: 'triple-bacteria',
        category: 'chemical'
      },
      {
        id: 'drug-calibration',
        name: '药剂标定计算器',
        icon: '/images/zhuye/yjbd.png',
        iconClass: 'calibration-gradient',
        page: 'drug-calibration',
        category: 'chemical'
      },
      {
        id: 'seaSchedule',
        name: '倒班时间查询',
        icon: '/images/zhuye/dbsj.png',
        iconClass: 'schedule-gradient',
        page: 'seaSchedule',
        category: 'work'
      },
      {
        id: 'combined-calculator',
        name: '海费夜班费计算',
        icon: '/images/zhuye/hfybf.png',
        iconClass: 'fee-gradient',
        page: 'combined-calculator',
        category: 'work'
      },
      {
        id: 'stationJintie',
        name: '一站津贴查询',
        icon: '/images/zhuye/yzjt.png',
        iconClass: 'jintie-gradient',
        page: 'stationJintie',
        category: 'work'
      },
      {
        id: 'pensionTaxCalculator',
        name: '个人养老金节税计算器',
        icon: '/images/zhuye/grylj.png',
        iconClass: 'pension-gradient',
        page: 'pensionTaxCalculator',
        category: 'work'
      },
      {
        id: 'analogConverter',
        name: '4-20mA模拟量转换',
        icon: '/images/zhuye/4-20mA.png',
        iconClass: 'analog-gradient',
        page: 'analogConverter',
        category: 'calculation'
      },
      {
        id: 'equipmentMaintenance',
        name: '设备保养时间',
        icon: '/images/zhuye/sbby.png',
        iconClass: 'maintenance-gradient',
        page: 'equipmentMaintenance',
        category: 'calculation'
      },
      {
        id: 'horizontal-separator',
        name: '分离器计算',
        icon: '/images/zhuye/flq.png',
        iconClass: 'separator-gradient',
        page: 'horizontal-separator',
        category: 'calculation'
      },
      {
        id: 'tagQuery',
        name: '仪表位号及阀门编号查询',
        icon: '/images/zhuye/ybwh.png',
        iconClass: 'tag-gradient',
        page: 'tagQuery',
        category: 'query'
      },
      {
        id: 'pipeSizeTable',
        name: '管线尺寸对照表',
        icon: '/images/zhuye/gxcc.png',
        iconClass: 'pipe-gradient',
        page: 'pipeSizeTable',
        category: 'query'
      },
      {
        id: 'pressureConverter',
        name: 'PSI压力换算',
        icon: '/images/zhuye/PSI.png',
        iconClass: 'pressure-gradient',
        page: 'pressureConverter',
        category: 'query'
      },
      {
        id: 'vibrationConverter',
        name: '振速位移换算',
        icon: '/images/zhuye/zd.png',
        iconClass: 'vibration-gradient',
        page: 'vibrationConverter',
        category: 'query'
      },
      {
        id: 'processSymbols',
        name: '工艺及仪表符号查询',
        icon: '/images/zhuye/ybwh.png',
        iconClass: 'symbol-gradient',
        page: 'processSymbols',
        category: 'query'
      },
      {
        id: 'alarmStandards',
        name: '报警值设定标准查询',
        icon: '/images/zhuye/bjz.png',
        iconClass: 'alarm-gradient',
        page: 'alarmStandards',
        category: 'query'
      },
      {
        id: 'explosionProofQuery',
        name: '防爆标识查询',
        icon: '/images/zhuye/fbbs.png',
        iconClass: 'explosion-gradient',
        page: 'explosionProofQuery',
        category: 'query'
      },
      {
        id: 'high-risk-operations',
        name: '高风险作业级别查询',
        icon: '/images/zhuye/gfxzy.png',
        iconClass: 'risk-gradient',
        page: 'high-risk-operations',
        category: 'query'
      },
      {
        id: 'bmiCalculator',
        name: 'BMI计算',
        icon: '/images/zhuye/BMI.png',
        iconClass: 'bmi-gradient',
        page: 'bmiCalculator',
        category: 'utility'
      },
      {
        id: 'photoBackground',
        name: '证件照换背景',
        icon: '📷',
        iconClass: 'photo-gradient',
        page: 'photoBackground',
        category: 'utility',
        isEmoji: true
      }
    ]
  },

  onLoad() {
    this.initTools();
    this.loadCurrentSettings();
  },

  // 初始化工具分类
  initTools() {
    const chemicalTools = this.data.allTools.filter(tool => tool.category === 'chemical');
    const workTools = this.data.allTools.filter(tool => tool.category === 'work');
    const calculationTools = this.data.allTools.filter(tool => tool.category === 'calculation');
    const queryTools = this.data.allTools.filter(tool => tool.category === 'query');
    const utilityTools = this.data.allTools.filter(tool => tool.category === 'utility');

    this.setData({
      chemicalTools,
      workTools,
      calculationTools,
      queryTools,
      utilityTools
    });
  },

  // 加载当前设置
  async loadCurrentSettings() {
    // 检查用户是否已绑定
    const token = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isBindUser = !!(token && userInfo);

    let customHotTools = [];

    if (isBindUser) {
      // 已绑定用户，优先从服务器读取
      try {
        const serverSettings = await this.loadSettingsFromServer();
        if (serverSettings && serverSettings.length > 0) {
          customHotTools = serverSettings;
        } else {
          // 服务器没有设置，从本地读取
          customHotTools = wx.getStorageSync('custom_hot_tools') || [];
        }
      } catch (error) {
        // 网络错误，从本地读取
        customHotTools = wx.getStorageSync('custom_hot_tools') || [];
      }
    } else {
      // 未绑定用户，只从本地读取
      customHotTools = wx.getStorageSync('custom_hot_tools') || [];
    }

    const selectedTools = customHotTools.map(toolId => {
      return this.data.allTools.find(tool => tool.id === toolId);
    }).filter(tool => tool);

    this.setData({
      selectedTools: selectedTools,
      selectedToolIds: customHotTools
    });
  },

  // 从服务器加载设置
  async loadSettingsFromServer() {
    const token = wx.getStorageSync('wechat_token');
    if (!token) {
      throw new Error('用户未登录');
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://sunxiyue.com/zdh/api/hot_tools_settings_api.php',
        method: 'GET',
        data: {
          action: 'get_hot_tools_settings',
          token: token
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data.hot_tools || []);
          } else {
            reject(new Error(res.data.message || '获取设置失败'));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  },

  // 判断工具是否已选择（用于WXML中的条件判断）
  isSelected: function(toolId) {
    return this.data.selectedToolIds.includes(toolId);
  },

  // 切换工具选择状态
  toggleTool(e) {
    const tool = e.currentTarget.dataset.tool;
    const isCurrentlySelected = this.data.selectedToolIds.includes(tool.id);

    if (isCurrentlySelected) {
      // 取消选择
      this.removeFromSelected({ currentTarget: { dataset: { id: tool.id } } });
    } else {
      // 添加选择
      if (this.data.selectedTools.length >= 4) {
        wx.showToast({
          title: '最多只能选择4个工具',
          icon: 'none'
        });
        return;
      }

      const newSelectedTools = [...this.data.selectedTools, tool];
      const newSelectedToolIds = [...this.data.selectedToolIds, tool.id];

      this.setData({
        selectedTools: newSelectedTools,
        selectedToolIds: newSelectedToolIds
      });
    }
  },

  // 从已选择中移除工具
  removeFromSelected(e) {
    const toolId = e.currentTarget.dataset.id;
    const newSelectedTools = this.data.selectedTools.filter(tool => tool.id !== toolId);
    const newSelectedToolIds = this.data.selectedToolIds.filter(id => id !== toolId);

    this.setData({
      selectedTools: newSelectedTools,
      selectedToolIds: newSelectedToolIds
    });
  },

  // 恢复默认设置
  resetToDefault() {
    const defaultToolIds = ['oil-calculator', 'drug-concentration', 'drug-inventory', 'seaSchedule'];
    const defaultTools = defaultToolIds.map(toolId => {
      return this.data.allTools.find(tool => tool.id === toolId);
    }).filter(tool => tool);

    this.setData({
      selectedTools: defaultTools,
      selectedToolIds: defaultToolIds
    });

    wx.showToast({
      title: '已恢复默认设置',
      icon: 'success'
    });
  },

  // 保存设置
  async saveSettings() {
    if (this.data.selectedTools.length === 0) {
      wx.showToast({
        title: '请至少选择一个工具',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    // 检查用户是否已绑定
    const token = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isBindUser = !!(token && userInfo);

    try {
      // 先保存到本地存储（作为备份）
      wx.setStorageSync('custom_hot_tools', this.data.selectedToolIds);

      if (isBindUser) {
        // 已绑定用户，同时保存到服务器
        try {
          await this.saveSettingsToServer();
          wx.showToast({
            title: '设置已同步到云端',
            icon: 'success'
          });
        } catch (error) {
          console.error('保存到服务器失败:', error);
          wx.showToast({
            title: '已保存到本地，云端同步失败',
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        // 未绑定用户，只保存到本地
        wx.showToast({
          title: '设置保存成功',
          icon: 'success'
        });
      }

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('保存设置失败:', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 保存设置到服务器
  async saveSettingsToServer() {
    const token = wx.getStorageSync('wechat_token');
    if (!token) {
      throw new Error('用户未登录');
    }

    // 将数组转换为字符串，用逗号分隔
    const hotToolsString = this.data.selectedToolIds.join(',');

    const params = {
      action: 'save_hot_tools_settings',
      token: token,
      hot_tools: hotToolsString
    };

    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    return new Promise((resolve, reject) => {
      wx.request({
        url: `https://sunxiyue.com/zdh/api/hot_tools_settings_api.php?${queryString}`,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data);
          } else {
            reject(new Error(res.data.message || '保存到服务器失败'));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  }
});
