Page({
  data: {
    showNotice: true, // 默认显示通知
    appVersion: '0.8.2', // 当前应用版本，请根据实际版本修改
    isLoggedIn: false, // 用户登录状态
    hotTools: [], // 自定义热门工具列表
    // 所有可用工具的配置
    allTools: [
      {
        id: 'oil-calculator',
        name: '含油计算器',
        icon: '/images/zhuye/hy_oil.png',
        iconClass: 'oil-gradient',
        page: 'oil-calculator'
      },
      {
        id: 'drug-concentration',
        name: '药剂浓度计算器',
        icon: '/images/zhuye/yjnd.png',
        iconClass: 'concentration-gradient',
        page: 'drug-concentration'
      },
      {
        id: 'drug-inventory',
        name: '药剂库存计算',
        icon: '/images/zhuye/yjkc.png',
        iconClass: 'inventory-gradient',
        page: 'drug-inventory'
      },
      {
        id: 'seaSchedule',
        name: '倒班时间查询',
        icon: '/images/zhuye/dbsj.png',
        iconClass: 'schedule-gradient',
        page: 'seaSchedule'
      },
      {
        id: 'suspended-calculator',
        name: '悬浮物计算器',
        icon: '/images/zhuye/hy_suspended.png',
        iconClass: 'suspended-gradient',
        page: 'suspended-calculator'
      },
      {
        id: 'triple-bacteria',
        name: '三菌计算器',
        icon: '/images/zhuye/sjjsq.png',
        iconClass: 'bacteria-gradient',
        page: 'triple-bacteria'
      },
      {
        id: 'drug-calibration',
        name: '药剂标定计算器',
        icon: '/images/zhuye/yjbd.png',
        iconClass: 'calibration-gradient',
        page: 'drug-calibration'
      },
      {
        id: 'combined-calculator',
        name: '海费夜班费计算',
        icon: '/images/zhuye/hfybf.png',
        iconClass: 'fee-gradient',
        page: 'combined-calculator'
      },
      {
        id: 'stationJintie',
        name: '一站津贴查询',
        icon: '/images/zhuye/yzjt.png',
        iconClass: 'jintie-gradient',
        page: 'stationJintie'
      },
      {
        id: 'pensionTaxCalculator',
        name: '个人养老金节税计算器',
        icon: '/images/zhuye/grylj.png',
        iconClass: 'pension-gradient',
        page: 'pensionTaxCalculator'
      },
      {
        id: 'analogConverter',
        name: '4-20mA模拟量转换',
        icon: '/images/zhuye/4-20mA.png',
        iconClass: 'analog-gradient',
        page: 'analogConverter'
      },
      {
        id: 'equipmentMaintenance',
        name: '设备保养时间',
        icon: '/images/zhuye/sbby.png',
        iconClass: 'maintenance-gradient',
        page: 'equipmentMaintenance'
      },
      {
        id: 'horizontal-separator',
        name: '分离器计算',
        icon: '/images/zhuye/flq.png',
        iconClass: 'separator-gradient',
        page: 'horizontal-separator'
      },
      {
        id: 'tagQuery',
        name: '仪表位号及阀门编号查询',
        icon: '/images/zhuye/ybwh.png',
        iconClass: 'tag-gradient',
        page: 'tagQuery'
      },
      {
        id: 'pipeSizeTable',
        name: '管线尺寸对照表',
        icon: '/images/zhuye/gxcc.png',
        iconClass: 'pipe-gradient',
        page: 'pipeSizeTable'
      },
      {
        id: 'pressureConverter',
        name: 'PSI压力换算',
        icon: '/images/zhuye/PSI.png',
        iconClass: 'pressure-gradient',
        page: 'pressureConverter'
      },
      {
        id: 'vibrationConverter',
        name: '振速位移换算',
        icon: '/images/zhuye/zd.png',
        iconClass: 'vibration-gradient',
        page: 'vibrationConverter'
      },
      {
        id: 'processSymbols',
        name: '工艺及仪表符号查询',
        icon: '/images/zhuye/ybwh.png',
        iconClass: 'symbol-gradient',
        page: 'processSymbols'
      },
      {
        id: 'alarmStandards',
        name: '报警值设定标准查询',
        icon: '/images/zhuye/bjz.png',
        iconClass: 'alarm-gradient',
        page: 'alarmStandards'
      },
      {
        id: 'explosionProofQuery',
        name: '防爆标识查询',
        icon: '/images/zhuye/fbbs.png',
        iconClass: 'explosion-gradient',
        page: 'explosionProofQuery'
      },
      {
        id: 'high-risk-operations',
        name: '高风险作业级别查询',
        icon: '/images/zhuye/gfxzy.png',
        iconClass: 'risk-gradient',
        page: 'high-risk-operations'
      },
      {
        id: 'bmiCalculator',
        name: 'BMI计算',
        icon: '/images/zhuye/BMI.png',
        iconClass: 'bmi-gradient',
        page: 'bmiCalculator'
      },
      {
        id: 'photoBackground',
        name: '证件照换背景',
        icon: '📷',
        iconClass: 'photo-gradient',
        page: 'photoBackground',
        isEmoji: true
      }
    ]
  },
  
  // 关闭通知
  closeNotice() {
    this.setData({
      showNotice: false
    });
    
    // 保存关闭状态和时间到本地存储
    const currentTime = new Date().getTime();
    wx.setStorageSync('noticeInfo', {
      hideNotice: true,
      closeTime: currentTime,
      lastVersion: this.data.appVersion
    });
  },
  
  onLoad() {
    // 立即设置显示通知，避免延迟
    this.checkNoticeStatus();
    // 检查登录状态
    this.checkLoginStatus();
    // 初始化热门工具
    this.initHotTools();
  },

  onShow() {
    // 页面显示时再次检查，确保状态正确
    this.checkNoticeStatus();
    // 检查登录状态
    this.checkLoginStatus();
    // 重新加载热门工具（可能用户修改了设置）
    this.initHotTools();
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isLoggedIn = !!(token && userInfo);

    this.setData({
      isLoggedIn: isLoggedIn
    });
  },

  // 初始化热门工具
  async initHotTools() {
    const isLoggedIn = this.data.isLoggedIn;
    let hotTools = [];

    if (isLoggedIn) {
      // 用户已登录，获取自定义设置
      const token = wx.getStorageSync('wechat_token');
      const userInfo = wx.getStorageSync('user_info');
      const isBindUser = !!(token && userInfo);

      let customHotTools = [];

      if (isBindUser) {
        // 已绑定用户，优先从服务器读取
        try {
          customHotTools = await this.loadHotToolsFromServer();
          if (!customHotTools || customHotTools.length === 0) {
            // 服务器没有设置，从本地读取
            customHotTools = wx.getStorageSync('custom_hot_tools') || [];
          }
        } catch (error) {
          // 网络错误，从本地读取
          customHotTools = wx.getStorageSync('custom_hot_tools') || [];
        }
      } else {
        // 未绑定用户，只从本地读取
        customHotTools = wx.getStorageSync('custom_hot_tools') || [];
      }

      if (customHotTools && customHotTools.length > 0) {
        // 使用用户自定义的工具
        hotTools = customHotTools.map(toolId => {
          return this.data.allTools.find(tool => tool.id === toolId);
        }).filter(tool => tool); // 过滤掉可能不存在的工具
      } else {
        // 使用默认的热门工具
        hotTools = this.getDefaultHotTools();
      }
    } else {
      // 用户未登录，使用默认的热门工具
      hotTools = this.getDefaultHotTools();
    }

    this.setData({
      hotTools: hotTools
    });
  },

  // 从服务器加载热门工具设置
  async loadHotToolsFromServer() {
    const token = wx.getStorageSync('wechat_token');
    if (!token) {
      throw new Error('用户未登录');
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://sunxiyue.com/zdh/api/hot_tools_settings_api.php',
        method: 'GET',
        data: {
          action: 'get_hot_tools_settings',
          token: token
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data.hot_tools || []);
          } else {
            reject(new Error(res.data.message || '获取设置失败'));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  },

  // 获取默认热门工具
  getDefaultHotTools() {
    const defaultToolIds = ['oil-calculator', 'drug-concentration', 'drug-inventory', 'seaSchedule'];
    return defaultToolIds.map(toolId => {
      return this.data.allTools.find(tool => tool.id === toolId);
    }).filter(tool => tool);
  },

  // 跳转到热门工具设置页面
  navigateToHotToolsSettings() {
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录后再进行自定义设置',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/index'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/index/hot-tools-settings'
    });
  },
  
  // 检查通知显示状态
  checkNoticeStatus() {
    const noticeInfo = wx.getStorageSync('noticeInfo') || {};
    const currentTime = new Date().getTime();
    
    // 计算距离上次关闭的天数（默认7天后再次显示）
    const timeDiff = currentTime - (noticeInfo.closeTime || 0);
    const daysPassed = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    
    // 确定是否显示通知的条件:
    // 1. 从未关闭过
    // 2. 版本更新了
    // 3. 距离上次关闭已经过了7天
    const shouldShow = 
      !noticeInfo.hideNotice || 
      noticeInfo.lastVersion !== this.data.appVersion || 
      daysPassed >= 7;
    
    this.setData({
      showNotice: shouldShow
    });
  },

  navToDisclaimer() {
    // 免责声明文件在当前目录下
    wx.navigateTo({
      url: '/pages/index/disclaimer'
    });
  },

  // 显示更多功能开发中提示
  showComingSoon() {
    wx.showToast({
      title: '更多功能正在开发中',
      icon: 'none',
      duration: 2000
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用查询工具',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '平台常用查询工具',
      query: 'from=timeline'
    };
  },

  navigateTo(e) {
    const page = e.currentTarget.dataset.page;

    // TabBar页面需要使用switchTab（当前只有主页和我的页面是tabBar页面）
    const tabBarPages = ['index', 'profile'];
    if (tabBarPages.includes(page)) {
      wx.switchTab({
        url: `/pages/${page}/index`
      });
      return;
    }

    // 特殊处理管线尺寸对照表页面
    if (page === 'pipeSizeTable') {
      wx.navigateTo({
        url: '/pages/pipe-size-table/pipe-size-table'
      });
      return;
    }

    // 特殊处理PSI压力换算页面
    if (page === 'pressureConverter') {
      wx.navigateTo({
        url: '/pages/pressure-converter/pressure-converter'
      });
      return;
    }

    // 特殊处理振速位移换算页面
    if (page === 'vibrationConverter') {
      wx.navigateTo({
        url: '/pages/vibration-converter/vibration-converter'
      });
      return;
    }

    // 特殊处理工艺及仪表符号查询页面
    if (page === 'processSymbols') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理报警值设定标准查询页面
    if (page === 'alarmStandards') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理4-20mA模拟量转换页面
    if (page === 'analogConverter') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理设备保养时间计算页面
    if (page === 'equipmentMaintenance') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 一站津贴查询页面 - 跳转到主页面（已集成微信登录）
    if (page === 'stationJintie') {
      wx.navigateTo({
        url: `/pages/${page}/index`
      });
      return;
    }

    // 特殊处理防爆标识查询页面
    if (page === 'explosionProofQuery') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理高风险作业级别查询页面
    if (page === 'high-risk-operations') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    // 特殊处理证件照换背景页面
    if (page === 'photoBackground') {
      wx.navigateTo({
        url: `/pages/${page}/${page}`
      });
      return;
    }

    if (page) {
      wx.navigateTo({
        url: `/pages/${page}/index`,
      });
    }
  }
});