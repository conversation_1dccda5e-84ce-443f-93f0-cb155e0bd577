/* index.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.content {
  font-size: 28rpx;
  line-height: 1.8;
  color: #666;
  padding: 20rpx;
}

.back-btn {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  color: #007aff;
  font-size: 28rpx;
  background: none;
  border: none;
  padding: 0;
}

.back-btn::after {
  border: none;
}