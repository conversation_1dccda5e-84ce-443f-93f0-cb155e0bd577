<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="header-icon">🛡️</view>
    <view class="header-content">
      <text class="header-title">防爆标识查询</text>
      <text class="header-subtitle">输入防爆标识自动解读含义，或查看标准参考</text>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="section-title">
      <text class="title-icon">🔍</text>
      <text class="title-text">标识解读</text>
    </view>
    <view class="input-group">
      <text class="input-label">请输入防爆标识</text>
      <view class="input-container">
        <input
          class="input-field"
          placeholder="例如：Ex d IIB T4 Gb"
          value="{{inputValue}}"
          bindinput="onInputChange"
          confirm-type="search"
          bindconfirm="parseExplosionProof"
        />
        <view class="camera-icon" bindtap="showCameraOptions" hover-class="none">
          <text class="camera-icon-text">📷</text>
        </view>
      </view>
    </view>
    <button class="search-btn" bindtap="parseExplosionProof" hover-class="none">
      <text class="btn-icon">🔍</text>
      <text class="btn-text">解读标识</text>
    </button>

    <!-- 识别状态提示 -->
    <view class="recognition-status" wx:if="{{isRecognizing}}">
      <view class="loading-icon">⏳</view>
      <text class="status-text">正在识别图片中的文字...</text>
    </view>
  </view>

  <!-- 解读结果 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="section-title">
      <text class="title-icon">📋</text>
      <text class="title-text">解读结果</text>
    </view>
    <view class="result-list">
      <view class="result-item {{item.colorClass || ''}} {{item.isTitle ? 'compound-title' : ''}} {{item.isSeparator ? 'isSeparator' : ''}} {{item.isStandardInfo ? 'standard-info' : ''}} {{item.standardType === 'old' ? 'old-standard' : ''}}" wx:for="{{parseResults}}" wx:key="index">
        <!-- 标准徽章（仅在标题上显示） -->
        <view class="standard-badge {{item.standardType === 'old' ? 'old-standard' : ''}}" wx:if="{{item.isTitle && item.standardBadge}}">{{item.standardBadge}}</view>

        <view class="result-label">{{item.label}}</view>
        <view class="result-value" wx:if="{{!item.isSeparator}}">{{item.value}}</view>
      </view>
    </view>
  </view>

  <!-- 常用示例 -->
  <view class="example-section">
    <view class="section-title">
      <text class="title-icon">💡</text>
      <text class="title-text">常用示例</text>
    </view>
    <view class="example-list">
      <view class="example-item" wx:for="{{examples}}" wx:key="index" bindtap="selectExample" data-example="{{item.code}}" hover-class="none">
        <view class="example-header">
          <view class="example-code">{{item.code}}</view>
          <view class="standard-tag {{item.standard === '新标准' ? 'new-standard' : 'old-standard'}}">{{item.standard}}</view>
        </view>
        <view class="example-desc">{{item.description}}</view>
      </view>
    </view>
  </view>

  <!-- 快速参考 -->
  <view class="reference-section">
    <view class="section-title">
      <text class="title-icon">📖</text>
      <text class="title-text">快速参考</text>
      <text class="title-hint">（点击可查看详情）</text>
    </view>
    <view class="reference-tabs">
      <view
        class="tab-item {{activeTab === 'type' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="type"
        hover-class="none"
      >
        防爆类型
      </view>
      <view
        class="tab-item {{activeTab === 'gas' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="gas"
        hover-class="none"
      >
        气体组别
      </view>
      <view
        class="tab-item {{activeTab === 'temp' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="temp"
        hover-class="none"
      >
        温度等级
      </view>
      <view
        class="tab-item {{activeTab === 'level' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="level"
        hover-class="none"
      >
        保护级别
      </view>
    </view>
    <view class="reference-content">
      <view class="reference-grid">
        <view class="reference-item {{item.colorClass || 'type-color'}}" wx:for="{{currentReferenceData}}" wx:key="code" bindtap="showDetail" data-item="{{item}}">
          <view class="reference-code">{{item.code}}</view>
          <view class="reference-desc">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 标准说明 -->
  <view class="info-section">
    <view class="section-title">
      <text class="title-icon">📋</text>
      <text class="title-text">标准说明</text>
    </view>
    <view class="info-content">
      <text class="info-text">• <text style="color: #16a34a; font-weight: 600;">新标准 GB/T 3836-2021</text>：自2022年5月1日起实施，防爆型式更加细分，增加EPL保护等级</text>
      <text class="info-text">• <text style="color: #6b7280; font-weight: 600;">旧标准 GB/T 3836-2010</text>：已废止，但仍有设备使用此标准标识</text>
      <text class="info-text">• 防爆型式变化：d→da/db/dc，e→eb/ec，i→ia/ib/ic，新增tb/tc(粉尘)</text>
      <text class="info-text">• 设备类别扩展：新增IIIA(飞絮)、IIIB(非导电粉尘)、IIIC(导电粉尘)</text>
      <text class="info-text">• 温度标识：气体环境仍用T1-T6，粉尘环境用具体温度值如T80°C</text>
      <text class="info-text">• EPL保护等级：Ga/Gb/Gc(气体)、Da/Db/Dc(粉尘)、Ma/Mb(煤矿)</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="info-section">
    <view class="section-title">
      <text class="title-icon">ℹ️</text>
      <text class="title-text">使用说明</text>
    </view>
    <view class="info-content">
      <text class="info-text">• 防爆标识用于表明设备符合特定防爆安全标准</text>
      <text class="info-text">• 可在易燃易爆危险环境中安全使用</text>
      <text class="info-text">• 新标准格式：Ex + 防爆型式 + 设备类别 + 温度标识 + EPL等级</text>
      <text class="info-text">• 旧标准格式：Ex + 防爆类型 + 设备类别 + 气体组别 + 温度组别</text>
      <text class="info-text">• 点击示例可快速填入输入框进行解读</text>
      <text class="info-text">• 点击快速参考中的防爆类型可查看详细介绍</text>
    </view>
  </view>



  <!-- 详细介绍弹窗 -->
  <view class="modal-overlay" wx:if="{{showDetailModal}}" bindtap="closeDetailModal" hover-class="none">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{detailInfo.title}}</text>
      </view>
      <view class="modal-body">
        <text class="modal-text">{{detailInfo.content}}</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn" bindtap="closeDetailModal" hover-class="none">关闭</button>
      </view>
    </view>
  </view>

  <!-- 图片预览模态框 -->
  <view class="image-modal" wx:if="{{showImageModal}}" bindtap="cancelRecognition">
    <view class="image-modal-content" catchtap="">
      <view class="image-modal-header">
        <text class="modal-title">图片预览</text>
        <view class="header-controls">
          <text class="control-btn" catchtap="toggleCropMode">{{cropMode ? '取消选择' : '选择区域'}}</text>
          <text class="modal-close" catchtap="cancelRecognition">✕</text>
        </view>
      </view>
      <view class="image-preview" id="imagePreview">
        <image
          class="preview-image"
          src="{{selectedImage}}"
          mode="aspectFit"
          bindload="onImageLoad"
          id="previewImage"
        ></image>

        <!-- 区域选择框 -->
        <view
          class="crop-overlay"
          wx:if="{{cropMode}}"
          style="width: {{imageDisplayWidth}}px; height: {{imageDisplayHeight}}px; left: {{imageDisplayLeft}}px; top: {{imageDisplayTop}}px;"
        >
          <!-- 选择框 -->
          <view
            class="crop-box"
            style="left: {{cropBox.left}}px; top: {{cropBox.top}}px; width: {{cropBox.width}}px; height: {{cropBox.height}}px;"
            bindtouchstart="onCropTouchStart"
            bindtouchmove="onCropTouchMove"
            bindtouchend="onCropTouchEnd"
          >
            <!-- 四个角的拖拽点 -->
            <view class="crop-handle crop-handle-tl" data-handle="tl"></view>
            <view class="crop-handle crop-handle-tr" data-handle="tr"></view>
            <view class="crop-handle crop-handle-bl" data-handle="bl"></view>
            <view class="crop-handle crop-handle-br" data-handle="br"></view>

            <!-- 四条边的拖拽点 -->
            <view class="crop-handle crop-handle-t" data-handle="t"></view>
            <view class="crop-handle crop-handle-r" data-handle="r"></view>
            <view class="crop-handle crop-handle-b" data-handle="b"></view>
            <view class="crop-handle crop-handle-l" data-handle="l"></view>
          </view>
        </view>
      </view>
      <view class="image-modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelRecognition">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmRecognition">
          {{cropMode && hasCropSelection ? '识别选中区域' : '识别整张图片'}}
        </button>
      </view>
    </view>
  </view>
</view>
