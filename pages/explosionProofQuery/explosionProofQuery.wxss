page {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.container {
  padding: 32rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background: linear-gradient(180deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 页面标题 */
.header {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  box-shadow: 0 8rpx 24rpx rgba(71, 85, 105, 0.08);
}

.header-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
  display: block;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: rgba(100, 116, 139, 0.8);
  display: block;
}

/* 通用容器样式 */
.search-section, .result-section, .example-section, .reference-section, .info-section {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
  box-shadow: 0 8rpx 24rpx rgba(71, 85, 105, 0.08);
}

/* 区块标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
}

.title-hint {
  font-size: 22rpx;
  color: rgba(156, 163, 175, 0.8);
  margin-left: 12rpx;
  font-weight: 400;
}

/* 输入组件 */
.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: rgba(71, 85, 105, 0.8);
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}

.input-container {
  display: flex;
  align-items: center;
  position: relative;
}

.input-field {
  flex: 1;
  height: 96rpx;
  padding: 28rpx 80rpx 28rpx 32rpx;
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  border-radius: 20rpx;
  font-size: 30rpx;
  line-height: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
  box-sizing: border-box;
  color: rgba(30, 41, 59, 0.9);
}

.input-field:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}

.input-field::placeholder {
  color: rgba(156, 163, 175, 0.8);
  font-size: 28rpx;
}

.camera-icon {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.camera-icon:active {
  transform: translateY(-50%) scale(0.95);
  background: rgba(59, 130, 246, 0.2);
}

.camera-icon-text {
  font-size: 32rpx;
}

/* 搜索按钮 */
.search-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(145deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(239, 68, 68, 0.3);
  transition: all 0.2s ease;
}

.search-btn::after {
  border: none;
}

.search-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.4);
}

.btn-icon {
  margin-right: 12rpx;
}

/* 解读结果 */
.result-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.result-item {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.5);
}

.result-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #ef4444;
  margin-bottom: 8rpx;
}

.result-value {
  font-size: 30rpx;
  color: rgba(30, 41, 59, 0.9);
  line-height: 1.4;
}

/* 示例列表 */
.example-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.example-item {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.5);
  transition: all 0.2s ease;
}

.example-item:active {
  background: rgba(226, 232, 240, 0.9);
  transform: scale(0.98);
}

.example-code {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 12rpx;
  font-family: 'Courier New', monospace;
}

.example-desc {
  font-size: 26rpx;
  color: rgba(71, 85, 105, 0.8);
  line-height: 1.4;
}

/* 参考标签页 */
.reference-tabs {
  display: flex;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 24rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  font-size: 24rpx;
  color: rgba(71, 85, 105, 0.8);
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.tab-item.active {
  background: #ef4444;
  color: white;
  font-weight: 600;
}

/* 参考网格 */
.reference-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.reference-item {
  border-radius: 16rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid rgba(226, 232, 240, 0.5);
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-item:active {
  transform: scale(0.98);
}

.reference-code {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

/* 不同类型的容器背景颜色 */
.reference-item.type-color {
  background: rgba(239, 68, 68, 0.1); /* 红色背景 - 防爆类型 */
  border-color: rgba(239, 68, 68, 0.3);
}

.reference-item.type-color .reference-code {
  color: #ef4444;
}

.reference-item.gas-color {
  background: rgba(59, 130, 246, 0.1); /* 蓝色背景 - 气体组别 */
  border-color: rgba(59, 130, 246, 0.3);
}

.reference-item.gas-color .reference-code {
  color: #3b82f6;
}

.reference-item.temp-color {
  background: rgba(245, 158, 11, 0.1); /* 橙色背景 - 温度等级 */
  border-color: rgba(245, 158, 11, 0.3);
}

.reference-item.temp-color .reference-code {
  color: #f59e0b;
}

.reference-item.level-color {
  background: rgba(16, 185, 129, 0.1); /* 绿色背景 - 保护级别 */
  border-color: rgba(16, 185, 129, 0.3);
}

.reference-item.level-color .reference-code {
  color: #10b981;
}

/* 不同类型的点击效果 */
.reference-item.type-color:active {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  transform: scale(0.98);
}

.reference-item.gas-color:active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  transform: scale(0.98);
}

.reference-item.temp-color:active {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.5);
  transform: scale(0.98);
}

.reference-item.level-color:active {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
  transform: scale(0.98);
}

.reference-desc {
  font-size: 22rpx;
  color: rgba(71, 85, 105, 0.8);
  line-height: 1.3;
}

/* 使用说明 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-text {
  font-size: 26rpx;
  color: rgba(71, 85, 105, 0.8);
  line-height: 1.5;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25rpx);
  border-radius: 28rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(226, 232, 240, 0.5);
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(30, 41, 59, 0.9);
}

.modal-body {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.modal-text {
  font-size: 28rpx;
  color: rgba(71, 85, 105, 0.9);
  line-height: 1.6;
}

.modal-footer {
  padding: 24rpx 32rpx 32rpx;
  display: flex;
  justify-content: center;
}

.modal-btn {
  padding: 20rpx 48rpx;
  background: linear-gradient(145deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.3);
  transition: all 0.2s ease;
}

.modal-btn::after {
  border: none;
}

.modal-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.4);
}



/* 新旧标准颜色区分 */
.reference-item.new-standard-color {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.reference-item.new-standard-color:active {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.5);
  transform: scale(0.98);
}

.reference-item.old-standard-color {
  background: rgba(156, 163, 175, 0.1);
  border-color: rgba(156, 163, 175, 0.3);
}

.reference-item.old-standard-color:active {
  background: rgba(156, 163, 175, 0.2);
  border-color: rgba(156, 163, 175, 0.5);
  transform: scale(0.98);
}

/* 空内容样式 */
.gas-content:empty::after {
  content: '-';
  color: #9ca3af;
}

/* 示例标准标签样式 */
.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.standard-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.standard-tag.new-standard {
  background: rgba(34, 197, 94, 0.15);
  color: #16a34a;
  border: 1rpx solid rgba(34, 197, 94, 0.3);
}

.standard-tag.old-standard {
  background: rgba(156, 163, 175, 0.15);
  color: #6b7280;
  border: 1rpx solid rgba(156, 163, 175, 0.3);
}

/* 识别状态样式 */
.recognition-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  padding: 16rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12rpx;
}

.loading-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text {
  font-size: 24rpx;
  color: #1e40af;
}

/* 图片预览模态框样式 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.image-modal-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.image-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #f8fafc;
  border-bottom: 1rpx solid #e2e8f0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.control-btn {
  font-size: 28rpx;
  color: #3b82f6;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: #eff6ff;
  border: 1rpx solid #dbeafe;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  font-size: 36rpx;
  color: #6b7280;
  padding: 8rpx;
  line-height: 1;
}

.image-preview {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  background: #f9fafb;
  position: relative;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 500rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.image-modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 24rpx 32rpx;
  background: #f8fafc;
  border-top: 1rpx solid #e2e8f0;
}

.modal-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.cancel-btn:active {
  background: #e5e7eb;
  transform: scale(0.98);
}

.confirm-btn {
  background: #3b82f6;
  color: white;
}

.confirm-btn:active {
  background: #2563eb;
  transform: scale(0.98);
}

/* 复合标识样式 */
.compound-title {
  background: rgba(59, 130, 246, 0.1);
  border-left: 4rpx solid #3b82f6;
  padding: 16rpx;
  margin: 8rpx 0 8rpx 0;
  border-radius: 8rpx;
  position: relative;
}

.compound-title .result-label {
  color: #1e40af;
  font-weight: 600;
  font-size: 28rpx;
}

.compound-title .result-value {
  color: #3b82f6;
  font-size: 24rpx;
  margin-top: 4rpx;
}

.result-item.isSeparator {
  text-align: center;
  color: #9ca3af;
  font-size: 24rpx;
  margin: 8rpx 0;
  border: none;
  background: none;
  border-top: 1rpx solid #e5e7eb;
  padding: 8rpx 0;
}

.result-item.isSeparator .result-label {
  display: none;
}

/* 标准类型标签 */
.standard-badge {
  position: absolute;
  top: -12rpx;
  right: 16rpx;
  background: #22c55e;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.standard-badge.old-standard {
  background: #6b7280;
}

.standard-info {
  background: rgba(34, 197, 94, 0.1);
  border: 1rpx solid rgba(34, 197, 94, 0.3);
  padding: 8rpx 16rpx;
  margin: 0 0 6rpx 0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #15803d;
  text-align: center;
  font-weight: 500;
}

.standard-info.old-standard {
  background: rgba(107, 114, 128, 0.1);
  border-color: rgba(107, 114, 128, 0.3);
  color: #374151;
}

.standard-info .result-value {
  display: none;
}

/* 区域选择样式 */
.crop-overlay {
  position: absolute;
  background: rgba(0, 0, 0, 0.3);
  pointer-events: none;
}

.crop-box {
  position: absolute;
  border: 2rpx solid #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: auto;
  min-width: 60rpx;
  min-height: 60rpx;
}

.crop-handle {
  position: absolute;
  background: #3b82f6;
  border: 2rpx solid white;
  border-radius: 50%;
  width: 20rpx;
  height: 20rpx;
  pointer-events: auto;
}

/* 四个角的拖拽点 */
.crop-handle-tl {
  top: -10rpx;
  left: -10rpx;
  cursor: nw-resize;
}

.crop-handle-tr {
  top: -10rpx;
  right: -10rpx;
  cursor: ne-resize;
}

.crop-handle-bl {
  bottom: -10rpx;
  left: -10rpx;
  cursor: sw-resize;
}

.crop-handle-br {
  bottom: -10rpx;
  right: -10rpx;
  cursor: se-resize;
}

/* 四条边的拖拽点 */
.crop-handle-t {
  top: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.crop-handle-r {
  right: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

.crop-handle-b {
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.crop-handle-l {
  left: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  cursor: w-resize;
}
