/* 添加用户页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 表单标题 */
.form-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.form-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 表单组 */
.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-right: 8rpx;
}

/* 输入框 */
.form-input {
  width: 100%;
  padding: 28rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
  line-height: 1.5;
  height: 88rpx;
  color: #333;
}

/* placeholder样式 */
.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.form-input.error {
  border-color: #ff4757;
}

.form-input:focus {
  border-color: #007aff;
}

/* 选择器 */
.form-picker {
  width: 100%;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: white;
  box-sizing: border-box;
}

.form-picker.error {
  border-color: #ff4757;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 24rpx;
  font-size: 28rpx;
  height: 88rpx;
  box-sizing: border-box;
}

.placeholder {
  color: #999;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

/* 开关组 */
.switch-group {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
}

.switch-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

/* 错误提示 */
.error-text {
  display: block;
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 表单操作 */
.form-actions {
  margin-top: 60rpx;
  display: flex;
  gap: 20rpx;
}

.submit-btn {
  flex: 1;
  padding: 24rpx;
  background: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
}

.cancel-btn {
  flex: 1;
  padding: 24rpx;
  background: transparent;
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.cancel-btn[disabled] {
  color: #ccc;
  border-color: #f0f0f0;
}

/* 表单提示 */
.form-tips {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
