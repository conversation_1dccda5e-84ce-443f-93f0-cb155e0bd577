// 添加用户页面
Page({
  data: {
    formData: {
      username: '',
      password: '',
      confirmPassword: '',
      realName: '',
      role: 'user',
      isStationStaff: false
    },
    errors: {},
    submitting: false,
    roleIndex: -1,
    roleOptions: [
      { value: 'user', text: '普通用户' },
      { value: 'manager', text: '普通管理员' },
      { value: 'admin', text: '管理员' }
    ]
  },

  onLoad() {
    // 检查管理员权限
    this.checkAdminPermission();

    // 确保角色选择器正确初始化
    this.setData({
      roleIndex: 0,
      'formData.role': 'user'
    });
  },

  // 检查管理员权限
  checkAdminPermission() {
    const userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('wechat_token');
    
    if (!token || !userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以添加用户',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }
    return true;
  },

  // 获取管理员Token
  getAdminToken() {
    return wx.getStorageSync('wechat_token');
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误提示
    });
  },

  // 角色选择变化
  onRoleChange(e) {
    const index = parseInt(e.detail.value);
    const role = this.data.roleOptions[index];
    
    this.setData({
      roleIndex: index,
      'formData.role': role.value,
      'errors.role': ''
    });
  },

  // 开关变化
  onSwitchChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    const errors = {};

    // 验证用户名
    if (!formData.username.trim()) {
      errors.username = '请输入用户名';
    } else if (formData.username.trim().length < 3) {
      errors.username = '用户名至少3个字符';
    } else if (formData.username.trim().length > 50) {
      errors.username = '用户名不能超过50个字符';
    } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(formData.username.trim())) {
      errors.username = '用户名只能包含字母、数字、下划线和中文';
    }

    // 验证密码
    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少6个字符';
    }

    // 验证确认密码
    if (!formData.confirmPassword) {
      errors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
    }

    // 验证真实姓名（可选）
    if (formData.realName && formData.realName.length > 50) {
      errors.realName = '真实姓名不能超过50个字符';
    }

    // 验证角色
    if (!formData.role) {
      errors.role = '请选择用户角色';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 提交表单
  async onSubmit() {
    if (!this.checkAdminPermission()) return;
    
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查表单信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      const { formData } = this.data;
      
      const response = await this.request({
        url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=add',
        method: 'POST',
        data: {
          token: this.getAdminToken(),
          username: formData.username.trim(),
          password: formData.password,
          real_name: formData.realName.trim() || formData.username.trim(),
          role: formData.role,
          is_station_staff: formData.isStationStaff
        }
      });

      if (response.success) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
        
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(response.message || '添加用户失败');
      }
    } catch (error) {
      console.error('添加用户失败:', error);
      
      // 处理特定错误
      if (error.message.includes('用户名已存在')) {
        this.setData({
          'errors.username': '用户名已存在，请使用其他用户名'
        });
      } else {
        wx.showToast({
          title: error.message || '添加失败',
          icon: 'none'
        });
      }
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  },

  // 取消操作
  onCancel() {
    // 检查是否有未保存的内容
    const { formData } = this.data;
    const hasContent = formData.username || formData.password || formData.realName;
    
    if (hasContent) {
      wx.showModal({
        title: '确认取消',
        content: '表单内容尚未保存，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  }
});
