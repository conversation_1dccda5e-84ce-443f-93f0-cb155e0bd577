/* 编辑用户页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-info-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.current-role {
  font-size: 28rpx;
  color: #666;
}

.wechat-status {
  font-size: 24rpx;
  color: white;
  background: #1dd1a1;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 表单区域 */
.form-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #007aff;
}

/* 表单组 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

/* 输入框 */
.form-input {
  width: 100%;
  padding: 28rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  box-sizing: border-box;
  line-height: 1.5;
  height: 88rpx;
  color: #333;
}

/* placeholder样式 */
.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.form-input.error {
  border-color: #ff4757;
}

.form-input:focus {
  border-color: #007aff;
}

/* 选择器 */
.form-picker {
  width: 100%;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: white;
  box-sizing: border-box;
}

.form-picker.error {
  border-color: #ff4757;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 24rpx;
  font-size: 28rpx;
  height: 88rpx;
  box-sizing: border-box;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

/* 开关组 */
.switch-group {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
}

.switch-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

/* 错误提示 */
.error-text {
  display: block;
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 保存按钮 */
.save-btn {
  width: 100%;
  padding: 24rpx;
  background: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-top: 20rpx;
}

.save-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 解除微信绑定按钮 */
.unbind-wechat-btn {
  width: 100%;
  padding: 24rpx;
  background: #ff6b6b;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-top: 20rpx;
}

.unbind-wechat-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 修改密码按钮 */
.password-btn {
  width: 100%;
  padding: 24rpx;
  background: #ffa502;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-top: 20rpx;
}

.password-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 危险操作区域 */
.danger-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  border: 2rpx solid #ff4757;
}

.danger-title {
  color: #ff4757;
  border-bottom-color: #ff4757;
}

.danger-btn {
  width: 100%;
  padding: 24rpx;
  background: #ff4757;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 16rpx;
}

.danger-tip {
  display: block;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.error-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 32rpx;
  color: #ff4757;
  display: block;
  margin-bottom: 40rpx;
}

.retry-btn {
  padding: 20rpx 40rpx;
  background: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

/* 操作提示 */
.tips-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
