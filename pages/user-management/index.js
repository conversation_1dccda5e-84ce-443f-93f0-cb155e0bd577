// 用户管理列表页面
Page({
  data: {
    userList: [],
    loading: false,
    searchKeyword: '',
    roleIndex: 0,
    roleOptions: [
      { value: '', text: '全部角色' },
      { value: 'admin', text: '管理员' },
      { value: 'manager', text: '普通管理员' },
      { value: 'user', text: '普通用户' }
    ],
    pagination: {
      current_page: 1,
      total_pages: 1,
      total_items: 0,
      items_per_page: 20
    }
  },

  onLoad() {
    // 检查管理员权限
    this.checkAdminPermission();
    // 加载用户列表
    this.loadUserList();
  },

  onShow() {
    // 页面显示时刷新列表
    this.loadUserList();
  },

  // 检查管理员权限
  checkAdminPermission() {
    const userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('wechat_token');
    
    if (!token || !userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以访问用户管理功能',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }
    return true;
  },

  // 获取管理员Token
  getAdminToken() {
    return wx.getStorageSync('wechat_token');
  },

  // 构建查询参数
  buildQueryParams(params) {
    const queryParts = [];
    for (const key in params) {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
      }
    }
    return queryParts.join('&');
  },

  // 加载用户列表
  async loadUserList(page = 1) {
    if (!this.checkAdminPermission()) return;

    this.setData({ loading: true });

    try {
      // 构建查询参数
      const params = {
        action: 'list',
        page: page,
        token: this.getAdminToken()
      };

      // 添加搜索关键词
      if (this.data.searchKeyword.trim()) {
        params.search = this.data.searchKeyword.trim();
      }

      // 添加角色筛选
      const selectedRole = this.data.roleOptions[this.data.roleIndex].value;
      if (selectedRole) {
        params.role = selectedRole;
      }

      const queryString = this.buildQueryParams(params);
      const response = await this.request({
        url: `https://sunxiyue.com/zdh/api/user_management_api.php?${queryString}`,
        method: 'GET'
      });

      if (response.success) {
        this.setData({
          userList: response.data.users || [],
          pagination: response.data.pagination || this.data.pagination
        });
      } else {
        throw new Error(response.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      'pagination.current_page': 1
    });
    this.loadUserList(1);
  },

  // 角色筛选变化
  onRoleChange(e) {
    this.setData({
      roleIndex: parseInt(e.detail.value),
      'pagination.current_page': 1
    });
    this.loadUserList(1);
  },

  // 上一页
  prevPage() {
    const currentPage = this.data.pagination.current_page;
    if (currentPage > 1) {
      this.loadUserList(currentPage - 1);
    }
  },

  // 下一页
  nextPage() {
    const currentPage = this.data.pagination.current_page;
    const totalPages = this.data.pagination.total_pages;
    if (currentPage < totalPages) {
      this.loadUserList(currentPage + 1);
    }
  },

  // 跳转到添加用户页面
  navigateToAdd() {
    wx.navigateTo({
      url: '/pages/user-management/add'
    });
  },

  // 显示操作选择菜单
  showActionSheet(e) {
    const user = e.currentTarget.dataset.user;
    const userId = e.currentTarget.dataset.id;
    const username = e.currentTarget.dataset.username;

    // 根据用户是否绑定微信来动态构建菜单项
    const itemList = ['编辑用户'];

    // 如果用户绑定了微信，添加解绑选项
    if (user.has_wechat) {
      itemList.push('解除微信绑定');
    }

    itemList.push('删除用户');

    wx.showActionSheet({
      itemList: itemList,
      itemColor: '#333333',
      success: (res) => {
        if (res.tapIndex === 0) {
          // 编辑用户
          this.navigateToEdit(user);
        } else if (user.has_wechat && res.tapIndex === 1) {
          // 解除微信绑定
          this.confirmUnbindWechat(userId, username);
        } else if (res.tapIndex === itemList.length - 1) {
          // 删除用户（总是最后一个选项）
          this.confirmDeleteUser(userId, username);
        }
      }
    });
  },

  // 跳转到编辑用户页面
  navigateToEdit(user) {
    wx.navigateTo({
      url: `/pages/user-management/edit?userId=${user.id}`
    });
  },

  // 确认解除微信绑定
  confirmUnbindWechat(userId, username) {
    wx.showModal({
      title: '确认解除绑定',
      content: `确定要解除用户 "${username}" 的微信绑定吗？解除后该用户将无法使用微信登录。`,
      confirmText: '解除绑定',
      confirmColor: '#ff6b6b',
      success: (res) => {
        if (res.confirm) {
          this.performUnbindWechat(userId, username);
        }
      }
    });
  },

  // 确认解除微信绑定
  confirmUnbindWechat(userId, username) {
    wx.showModal({
      title: '确认解除绑定',
      content: `确定要解除用户 "${username}" 的微信绑定吗？解除后该用户将无法使用微信登录。`,
      confirmText: '解除绑定',
      confirmColor: '#ff6b6b',
      success: (res) => {
        if (res.confirm) {
          this.performUnbindWechat(userId, username);
        }
      }
    });
  },

  // 执行解除微信绑定
  async performUnbindWechat(userId, username) {
    wx.showLoading({ title: '解除绑定中...' });

    try {
      const params = {
        action: 'unbind_wechat',
        token: this.getAdminToken(),
        user_id: userId
      };

      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      const response = await this.request({
        url: `https://sunxiyue.com/zdh/api/user_management_api.php?${queryString}`,
        method: 'GET'
      });

      if (response.success) {
        wx.showToast({
          title: '解除绑定成功',
          icon: 'success'
        });

        // 重新加载当前页
        this.loadUserList(this.data.pagination.current_page);
      } else {
        throw new Error(response.message || '解除绑定失败');
      }
    } catch (error) {
      console.error('解除微信绑定失败:', error);
      wx.showToast({
        title: error.message || '解除绑定失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 确认删除用户
  confirmDelete(e) {
    const userId = e.currentTarget.dataset.id;
    const username = e.currentTarget.dataset.username;
    this.confirmDeleteUser(userId, username);
  },

  // 确认删除用户（内部方法）
  confirmDeleteUser(userId, username) {
    // 先获取用户信息，检查是否绑定了微信
    this.getUserInfo(userId, username);
  },

  // 获取用户信息并确认删除
  async getUserInfo(userId, username) {
    try {
      const response = await this.request({
        url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=get_user',
        method: 'POST',
        data: {
          token: this.getAdminToken(),
          user_id: userId
        }
      });

      if (response.success) {
        const userInfo = response.data;
        const hasWechat = userInfo.has_wechat;

        let content = `确定要删除用户 "${username}" 吗？此操作不可恢复。`;
        if (hasWechat) {
          content += '\n\n注意：该用户已绑定微信，删除时将自动解除微信绑定。';
        }

        wx.showModal({
          title: '确认删除',
          content: content,
          confirmText: '删除',
          confirmColor: '#ff4757',
          success: (res) => {
            if (res.confirm) {
              this.deleteUser(userId, hasWechat);
            }
          }
        });
      } else {
        // 如果获取用户信息失败，使用原来的确认对话框
        wx.showModal({
          title: '确认删除',
          content: `确定要删除用户 "${username}" 吗？此操作不可恢复。`,
          confirmText: '删除',
          confirmColor: '#ff4757',
          success: (res) => {
            if (res.confirm) {
              this.deleteUser(userId, false);
            }
          }
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，使用原来的确认对话框
      wx.showModal({
        title: '确认删除',
        content: `确定要删除用户 "${username}" 吗？此操作不可恢复。`,
        confirmText: '删除',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.deleteUser(userId, false);
          }
        }
      });
    }
  },

  // 删除用户
  async deleteUser(userId, hasWechat = false) {
    wx.showLoading({ title: '删除中...' });

    try {
      // 如果用户绑定了微信，先解除绑定
      if (hasWechat) {
        wx.showLoading({ title: '解除微信绑定中...' });

        try {
          // 尝试使用管理员解绑接口
          const unbindResponse = await this.request({
            url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=admin_unbind_wechat',
            method: 'POST',
            data: {
              token: this.getAdminToken(),
              user_id: userId
            }
          });

          if (!unbindResponse.success) {
            console.warn('管理员解绑接口失败，尝试替代方法:', unbindResponse.message);
            throw new Error('需要尝试替代方法');
          }
        } catch (error) {
          // 如果管理员接口失败，尝试使用登录API
          try {
            const altResponse = await this.request({
              url: 'https://sunxiyue.com/zdh/api/login_api.php',
              method: 'POST',
              data: {
                action: 'admin_unbind_user',
                token: this.getAdminToken(),
                user_id: userId
              }
            });

            if (altResponse.status !== 'success') {
              console.warn('替代解绑方法也失败，但继续删除用户:', altResponse.message);
            }
          } catch (altError) {
            console.warn('所有解绑方法都失败，但继续删除用户:', altError.message);
          }
        }
      }

      // 删除用户
      wx.showLoading({ title: '删除用户中...' });

      const response = await this.request({
        url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=delete',
        method: 'POST',
        data: {
          token: this.getAdminToken(),
          user_id: userId
        }
      });

      if (response.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        // 重新加载当前页
        this.loadUserList(this.data.pagination.current_page);
      } else {
        throw new Error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
