<!-- 添加用户页面 -->
<view class="container">
  <view class="form-container">
    <view class="form-header">
      <text class="form-title">添加新用户</text>
      <text class="form-subtitle">请填写用户基本信息</text>
    </view>

    <form bindsubmit="onSubmit">
      <!-- 用户名 -->
      <view class="form-group">
        <label class="form-label">
          <text class="required">*</text>
          <text>用户名</text>
        </label>
        <input
          class="form-input {{errors.username ? 'error' : ''}}"
          name="username"
          placeholder="请输入用户名"
          value="{{formData.username}}"
          bindinput="onInputChange"
          data-field="username"
          maxlength="50"
        />
        <text class="error-text" wx:if="{{errors.username}}">{{errors.username}}</text>
      </view>

      <!-- 密码 -->
      <view class="form-group">
        <label class="form-label">
          <text class="required">*</text>
          <text>密码</text>
        </label>
        <input
          class="form-input {{errors.password ? 'error' : ''}}"
          name="password"
          type="password"
          password="true"
          placeholder="请输入密码"
          value="{{formData.password}}"
          bindinput="onInputChange"
          data-field="password"
          maxlength="100"
        />
        <text class="error-text" wx:if="{{errors.password}}">{{errors.password}}</text>
      </view>

      <!-- 确认密码 -->
      <view class="form-group">
        <label class="form-label">
          <text class="required">*</text>
          <text>确认密码</text>
        </label>
        <input
          class="form-input {{errors.confirmPassword ? 'error' : ''}}"
          name="confirmPassword"
          type="password"
          password="true"
          placeholder="请再次输入密码"
          value="{{formData.confirmPassword}}"
          bindinput="onInputChange"
          data-field="confirmPassword"
          maxlength="100"
        />
        <text class="error-text" wx:if="{{errors.confirmPassword}}">{{errors.confirmPassword}}</text>
      </view>

      <!-- 真实姓名 -->
      <view class="form-group">
        <label class="form-label">
          <text>真实姓名</text>
        </label>
        <input
          class="form-input {{errors.realName ? 'error' : ''}}"
          name="realName"
          placeholder="请输入真实姓名"
          value="{{formData.realName}}"
          bindinput="onInputChange"
          data-field="realName"
          maxlength="50"
        />
        <text class="error-text" wx:if="{{errors.realName}}">{{errors.realName}}</text>
      </view>

      <!-- 用户角色 -->
      <view class="form-group">
        <label class="form-label">
          <text class="required">*</text>
          <text>用户角色</text>
        </label>
        <picker 
          class="form-picker {{errors.role ? 'error' : ''}}"
          bindchange="onRoleChange" 
          value="{{roleIndex}}" 
          range="{{roleOptions}}"
          range-key="text"
        >
          <view class="picker-display">
            <text>{{roleOptions[roleIndex].text}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <text class="error-text" wx:if="{{errors.role}}">{{errors.role}}</text>
      </view>

      <!-- 一站人员 -->
      <view class="form-group">
        <label class="form-label">
          <text>一站人员</text>
        </label>
        <view class="switch-group">
          <switch 
            checked="{{formData.isStationStaff}}"
            bindchange="onSwitchChange"
            data-field="isStationStaff"
            color="#007aff"
          />
          <text class="switch-text">{{formData.isStationStaff ? '是' : '否'}}</text>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <button 
          class="submit-btn"
          formType="submit"
          disabled="{{submitting}}"
        >
          {{submitting ? '添加中...' : '添加用户'}}
        </button>
        <button 
          class="cancel-btn"
          bindtap="onCancel"
          disabled="{{submitting}}"
        >
          取消
        </button>
      </view>
    </form>
  </view>

  <!-- 表单说明 -->
  <view class="form-tips">
    <view class="tip-item">
      <text class="tip-icon">💡</text>
      <text class="tip-text">用户名必须唯一，创建后不可修改</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">🔒</text>
      <text class="tip-text">密码至少6个字符，建议包含字母和数字</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">👤</text>
      <text class="tip-text">真实姓名用于显示，如不填写则使用用户名</text>
    </view>
  </view>
</view>
