<!-- 编辑用户页面 -->
<view class="container">
  <!-- 用户基本信息 -->
  <view class="user-info-card" wx:if="{{userInfo}}">
    <view class="user-header">
      <text class="username">{{userInfo.username}}</text>
      <text class="user-id">ID: {{userInfo.id}}</text>
    </view>
    <view class="user-meta">
      <text class="current-role">当前角色：{{userInfo.role_text}}</text>
      <text class="wechat-status" wx:if="{{userInfo.has_wechat}}">已绑定微信</text>
    </view>
  </view>

  <!-- 编辑表单 -->
  <view class="form-container" wx:if="{{userInfo}}">
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <!-- 真实姓名 -->
      <view class="form-group">
        <label class="form-label">真实姓名</label>
        <input 
          class="form-input {{errors.realName ? 'error' : ''}}"
          placeholder="请输入真实姓名"
          value="{{formData.realName}}"
          bindinput="onInputChange"
          data-field="realName"
          maxlength="50"
        />
        <text class="error-text" wx:if="{{errors.realName}}">{{errors.realName}}</text>
      </view>

      <!-- 用户角色 -->
      <view class="form-group">
        <label class="form-label">用户角色</label>
        <picker 
          class="form-picker {{errors.role ? 'error' : ''}}"
          bindchange="onRoleChange" 
          value="{{roleIndex}}" 
          range="{{roleOptions}}"
          range-key="text"
        >
          <view class="picker-display">
            <text>{{roleOptions[roleIndex].text}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <text class="error-text" wx:if="{{errors.role}}">{{errors.role}}</text>
      </view>

      <!-- 一站人员 -->
      <view class="form-group">
        <label class="form-label">一站人员</label>
        <view class="switch-group">
          <switch 
            checked="{{formData.isStationStaff}}"
            bindchange="onSwitchChange"
            data-field="isStationStaff"
            color="#007aff"
          />
          <text class="switch-text">{{formData.isStationStaff ? '是' : '否'}}</text>
        </view>
      </view>

      <!-- 保存基本信息按钮 -->
      <button
        class="save-btn"
        bindtap="saveBasicInfo"
        disabled="{{submitting}}"
      >
        {{submitting ? '保存中...' : '保存基本信息'}}
      </button>

      <!-- 解除微信绑定按钮 -->
      <button
        class="unbind-wechat-btn"
        bindtap="unbindWechat"
        disabled="{{submitting}}"
        wx:if="{{userInfo.has_wechat}}"
      >
        解除微信绑定
      </button>
    </view>

    <!-- 修改密码区域 -->
    <view class="form-section">
      <view class="section-title">修改密码</view>
      
      <view class="form-group">
        <label class="form-label">新密码</label>
        <input
          class="form-input {{errors.newPassword ? 'error' : ''}}"
          type="password"
          password="true"
          placeholder="请输入新密码"
          value="{{passwordData.newPassword}}"
          bindinput="onPasswordInputChange"
          data-field="newPassword"
          maxlength="100"
        />
        <text class="error-text" wx:if="{{errors.newPassword}}">{{errors.newPassword}}</text>
      </view>

      <view class="form-group">
        <label class="form-label">确认新密码</label>
        <input
          class="form-input {{errors.confirmNewPassword ? 'error' : ''}}"
          type="password"
          password="true"
          placeholder="请再次输入新密码"
          value="{{passwordData.confirmNewPassword}}"
          bindinput="onPasswordInputChange"
          data-field="confirmNewPassword"
          maxlength="100"
        />
        <text class="error-text" wx:if="{{errors.confirmNewPassword}}">{{errors.confirmNewPassword}}</text>
      </view>

      <!-- 修改密码按钮 -->
      <button 
        class="password-btn"
        bindtap="changePassword"
        disabled="{{changingPassword}}"
      >
        {{changingPassword ? '修改中...' : '修改密码'}}
      </button>
    </view>
  </view>

  <!-- 危险操作区域 -->
  <view class="danger-section" wx:if="{{userInfo && !isSelf}}">
    <view class="section-title danger-title">危险操作</view>
    <button 
      class="danger-btn"
      bindtap="confirmDeleteUser"
    >
      删除用户
    </button>
    <text class="danger-tip">删除用户将无法恢复，请谨慎操作</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{loadError}}">
    <text class="error-icon">⚠️</text>
    <text class="error-text">{{loadError}}</text>
    <button class="retry-btn" bindtap="loadUserInfo">重试</button>
  </view>

  <!-- 操作提示 -->
  <view class="tips-section" wx:if="{{userInfo}}">
    <view class="tip-item">
      <text class="tip-icon">💡</text>
      <text class="tip-text">修改基本信息后需要点击"保存基本信息"按钮</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">🔒</text>
      <text class="tip-text">修改密码是独立操作，不影响其他信息</text>
    </view>
    <view class="tip-item" wx:if="{{isSelf}}">
      <text class="tip-icon">⚠️</text>
      <text class="tip-text">您正在编辑自己的账号，无法修改角色或删除</text>
    </view>
  </view>
</view>
