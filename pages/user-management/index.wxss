/* 用户管理列表页面样式 - Apple高级风格 */

/* 重置button默认样式 */
button {
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: none !important;
  font-size: inherit !important;
  line-height: inherit !important;
  border-radius: 0 !important;
}

button::after {
  border: none !important;
}

page {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  -webkit-tap-highlight-color: transparent;
}

.container {
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 32rpx 32rpx 24rpx 32rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 8rpx 24rpx rgba(71, 85, 105, 0.08),
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.page-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.5rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 14rpx 18rpx !important;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%) !important;
  color: white !important;
  border-radius: 16rpx !important;
  font-size: 24rpx !important;
  font-weight: 600;
  border: none !important;
  box-shadow:
    0 2rpx 8rpx rgba(0, 122, 255, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  min-width: auto !important;
  width: auto !important;
  flex-shrink: 0;
  line-height: 1 !important;
  margin: 0 !important;
}

.add-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 1rpx 4rpx rgba(0, 122, 255, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.add-icon {
  margin-right: 6rpx;
  font-size: 24rpx;
  font-weight: 300;
}

/* 搜索和筛选区域 */
.search-section {
  margin: 0 32rpx 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 24rpx;
  padding: 32rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    0 1rpx 4rpx rgba(71, 85, 105, 0.03),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.search-box {
  display: flex;
  margin-bottom: 24rpx;
  gap: 12rpx;
  align-items: stretch;
}

.search-input {
  flex: 1;
  padding: 0 20rpx;
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  border-radius: 12rpx;
  font-size: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  color: #334155;
  transition: all 0.2s ease;
  height: 64rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.search-input:focus {
  border-color: #007aff;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
}

.search-btn {
  padding: 0 20rpx !important;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%) !important;
  color: white !important;
  border-radius: 12rpx !important;
  font-size: 24rpx !important;
  font-weight: 600;
  border: none !important;
  height: 64rpx !important;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 2rpx 8rpx rgba(0, 122, 255, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  width: 100rpx !important;
  min-width: 100rpx !important;
  max-width: 100rpx !important;
  flex-shrink: 0;
  line-height: 1 !important;
  margin: 0 !important;
}

.search-btn:active {
  transform: scale(0.96);
}

.filter-box {
  display: flex;
  align-items: center;
}

.role-picker {
  flex: 1;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  border-radius: 16rpx;
  background: rgba(248, 250, 252, 0.8);
  font-size: 28rpx;
  color: #334155;
  transition: all 0.2s ease;
}

.picker-display:active {
  background: rgba(255, 255, 255, 0.9);
  border-color: #007aff;
}

.picker-arrow {
  color: #64748b;
  font-size: 24rpx;
  transition: transform 0.2s ease;
}

/* 用户列表 */
.user-list {
  margin: 0 32rpx;
  position: relative;
  z-index: 1;
}

.user-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow:
    0 4rpx 16rpx rgba(71, 85, 105, 0.06),
    0 1rpx 4rpx rgba(71, 85, 105, 0.03),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
  transition: all 0.2s ease;
}

.user-item:active {
  transform: scale(0.99);
  background: rgba(255, 255, 255, 0.9);
}

.user-item:last-child {
  margin-bottom: 32rpx;
}

.user-info {
  flex: 4;
  margin-right: 16rpx;
}

.user-main {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.username {
  font-size: 34rpx;
  font-weight: 700;
  color: #1e293b;
  margin-right: 20rpx;
  letter-spacing: -0.3rpx;
}

.real-name {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.role-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  color: white;
  letter-spacing: 0.5rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.role-admin {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.role-manager {
  background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.role-user {
  background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
}

.station-tag {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #5c6bc0 0%, #3f51b5 100%);
  color: white;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  box-shadow: 0 2rpx 8rpx rgba(63, 81, 181, 0.3);
}

.wechat-tag {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #26c6da 0%, #00acc1 100%);
  color: white;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 172, 193, 0.3);
}

/* 操作按钮 */
.user-actions {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 20%; /* 最多占用1/5宽度 */
}

.action-btn {
  width: 36rpx !important;
  height: 36rpx !important;
  border-radius: 18rpx !important;
  font-size: 20rpx !important;
  font-weight: 600;
  border: none !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(100, 116, 139, 0.08) !important;
  color: #64748b !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
}

.more-btn:active {
  transform: scale(0.85);
  background: rgba(100, 116, 139, 0.15);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  margin: 0 32rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 8rpx 24rpx rgba(71, 85, 105, 0.08),
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.empty-icon {
  font-size: 140rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #64748b;
  display: block;
  margin-bottom: 48rpx;
  font-weight: 500;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  box-shadow:
    0 4rpx 16rpx rgba(0, 122, 255, 0.25),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.empty-action:active {
  transform: scale(0.96);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 120rpx 40rpx;
  margin: 0 32rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 28rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 8rpx 24rpx rgba(71, 85, 105, 0.08),
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.loading-text {
  font-size: 32rpx;
  color: #64748b;
  font-weight: 500;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24rpx 32rpx;
  padding: 20rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  position: relative;
  z-index: 1;
  box-shadow:
    0 2rpx 8rpx rgba(71, 85, 105, 0.04),
    0 1rpx 2rpx rgba(71, 85, 105, 0.02),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(226, 232, 240, 0.3);
}

.page-btn {
  padding: 12rpx 20rpx !important;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%) !important;
  color: white !important;
  border-radius: 12rpx !important;
  font-size: 24rpx !important;
  font-weight: 600;
  border: none !important;
  box-shadow:
    0 2rpx 6rpx rgba(0, 122, 255, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  min-width: 100rpx !important;
  width: auto !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  flex-shrink: 0;
}

.page-btn:active {
  transform: scale(0.95);
}

.page-btn[disabled] {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
  color: #94a3b8 !important;
  box-shadow: none !important;
}

.page-info {
  font-size: 26rpx;
  color: #475569;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0 20rpx;
  white-space: nowrap;
  min-width: 120rpx;
}

/* 统计信息 */
.stats-info {
  text-align: center;
  padding: 24rpx;
  margin: 0 32rpx 32rpx 32rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15rpx);
  -webkit-backdrop-filter: blur(15rpx);
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  position: relative;
  z-index: 1;
}
