// 编辑用户页面
Page({
  data: {
    userId: null,
    userInfo: null,
    formData: {
      realName: '',
      role: '',
      isStationStaff: false
    },
    passwordData: {
      newPassword: '',
      confirmNewPassword: ''
    },
    errors: {},
    loading: false,
    loadError: '',
    submitting: false,
    changingPassword: false,
    isSelf: false,
    roleIndex: 0,
    roleOptions: [
      { value: 'user', text: '普通用户' },
      { value: 'manager', text: '普通管理员' },
      { value: 'admin', text: '管理员' }
    ]
  },

  onLoad(options) {
    if (!options.userId) {
      wx.showToast({
        title: '缺少用户ID',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({ userId: options.userId });
    
    // 检查管理员权限
    if (this.checkAdminPermission()) {
      this.loadUserInfo();
    }
  },

  // 检查管理员权限
  checkAdminPermission() {
    const userInfo = wx.getStorageSync('user_info');
    const token = wx.getStorageSync('wechat_token');
    
    if (!token || !userInfo || userInfo.role !== 'admin') {
      wx.showModal({
        title: '权限不足',
        content: '只有管理员可以编辑用户',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }
    return true;
  },

  // 获取管理员Token
  getAdminToken() {
    return wx.getStorageSync('wechat_token');
  },

  // 加载用户信息
  async loadUserInfo() {
    this.setData({ 
      loading: true, 
      loadError: '' 
    });

    try {
      const response = await this.request({
        url: `https://sunxiyue.com/zdh/api/user_management_api.php?action=detail&user_id=${this.data.userId}&token=${this.getAdminToken()}`,
        method: 'GET'
      });

      if (response.success) {
        const userInfo = response.data;
        const currentUser = wx.getStorageSync('user_info');
        const isSelf = currentUser && currentUser.id == userInfo.id;
        
        // 找到角色在选项中的索引
        const roleIndex = this.data.roleOptions.findIndex(option => option.value === userInfo.role);
        
        this.setData({
          userInfo: userInfo,
          isSelf: isSelf,
          roleIndex: roleIndex >= 0 ? roleIndex : 0,
          formData: {
            realName: userInfo.real_name || '',
            role: userInfo.role,
            isStationStaff: userInfo.is_station_staff == 1
          }
        });
      } else {
        throw new Error(response.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      this.setData({
        loadError: error.message || '加载失败'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误提示
    });
  },

  // 密码输入框变化
  onPasswordInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`passwordData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误提示
    });
  },

  // 角色选择变化
  onRoleChange(e) {
    const index = parseInt(e.detail.value);
    const role = this.data.roleOptions[index];
    
    this.setData({
      roleIndex: index,
      'formData.role': role.value,
      'errors.role': ''
    });
  },

  // 开关变化
  onSwitchChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 解除微信绑定
  unbindWechat() {
    const userInfo = this.data.userInfo;

    wx.showModal({
      title: '确认解除绑定',
      content: `确定要解除用户 "${userInfo.username}" 的微信绑定吗？解除后该用户将无法使用微信登录。`,
      confirmText: '解除绑定',
      confirmColor: '#ff6b6b',
      success: (res) => {
        if (res.confirm) {
          this.performUnbindWechat();
        }
      }
    });
  },

  // 执行解除微信绑定
  async performUnbindWechat() {
    this.setData({ submitting: true });

    try {
      // 使用user_management_api.php的unbind_wechat功能，改用GET方式
      const params = {
        action: 'unbind_wechat',
        token: this.getAdminToken(),
        user_id: this.data.userId
      };

      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      const response = await this.request({
        url: `https://sunxiyue.com/zdh/api/user_management_api.php?${queryString}`,
        method: 'GET'
      });

      if (response.success) {
        wx.showToast({
          title: '解除绑定成功',
          icon: 'success'
        });

        // 直接更新页面状态，避免重新加载可能的权限问题
        this.setData({
          'userInfo.has_wechat': false,
          'userInfo.wechat_info': null,
          'userInfo.wechat_bindings': 0
        });

        // 延迟重新加载用户信息，给服务器一些时间处理
        setTimeout(() => {
          this.loadUserInfo();
        }, 1000);
      } else {
        throw new Error(response.message || '解除绑定失败');
      }
    } catch (error) {
      console.error('解除微信绑定失败:', error);
      wx.showToast({
        title: error.message || '解除绑定失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },



  // 验证基本信息
  validateBasicInfo() {
    const { formData } = this.data;
    const errors = {};

    // 验证真实姓名
    if (!formData.realName.trim()) {
      errors.realName = '请输入真实姓名';
    } else if (formData.realName.length > 50) {
      errors.realName = '真实姓名不能超过50个字符';
    }

    // 验证角色（如果不是编辑自己）
    if (!this.data.isSelf && !formData.role) {
      errors.role = '请选择用户角色';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 验证密码
  validatePassword() {
    const { passwordData } = this.data;
    const errors = {};

    if (!passwordData.newPassword) {
      errors.newPassword = '请输入新密码';
    } else if (passwordData.newPassword.length < 6) {
      errors.newPassword = '密码至少6个字符';
    }

    if (!passwordData.confirmNewPassword) {
      errors.confirmNewPassword = '请确认新密码';
    } else if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      errors.confirmNewPassword = '两次输入的密码不一致';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 保存基本信息
  async saveBasicInfo() {
    if (!this.validateBasicInfo()) {
      wx.showToast({
        title: '请检查表单信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      const { formData } = this.data;
      
      // 分别调用真实姓名和角色更新接口
      const promises = [];
      
      // 更新真实姓名
      promises.push(
        this.request({
          url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=update_real_name',
          method: 'POST',
          data: {
            token: this.getAdminToken(),
            user_id: this.data.userId,
            real_name: formData.realName.trim()
          }
        })
      );

      // 更新角色（如果不是编辑自己）
      if (!this.data.isSelf) {
        promises.push(
          this.request({
            url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=update_role',
            method: 'POST',
            data: {
              token: this.getAdminToken(),
              user_id: this.data.userId,
              role: formData.role,
              is_station_staff: formData.isStationStaff ? 1 : 0
            }
          })
        );
      }

      const results = await Promise.all(promises);
      
      // 检查所有请求是否成功
      const allSuccess = results.every(result => result.success);
      
      if (allSuccess) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 重新加载用户信息
        setTimeout(() => {
          this.loadUserInfo();
        }, 1000);
      } else {
        const failedResult = results.find(result => !result.success);
        throw new Error(failedResult.message || '保存失败');
      }
    } catch (error) {
      console.error('保存基本信息失败:', error);
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 修改密码
  async changePassword() {
    if (!this.validatePassword()) {
      wx.showToast({
        title: '请检查密码信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ changingPassword: true });

    try {
      const response = await this.request({
        url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=update_password',
        method: 'POST',
        data: {
          token: this.getAdminToken(),
          user_id: this.data.userId,
          new_password: this.data.passwordData.newPassword
        }
      });

      if (response.success) {
        wx.showToast({
          title: '密码修改成功',
          icon: 'success'
        });
        
        // 清空密码输入框
        this.setData({
          passwordData: {
            newPassword: '',
            confirmNewPassword: ''
          }
        });
      } else {
        throw new Error(response.message || '密码修改失败');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      wx.showToast({
        title: error.message || '修改失败',
        icon: 'none'
      });
    } finally {
      this.setData({ changingPassword: false });
    }
  },

  // 确认删除用户
  confirmDeleteUser() {
    const { userInfo } = this.data;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除用户 "${userInfo.username}" 吗？此操作不可恢复，将同时删除该用户的所有相关数据。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteUser();
        }
      }
    });
  },

  // 删除用户
  async deleteUser() {
    wx.showLoading({ title: '删除中...' });

    try {
      const response = await this.request({
        url: 'https://sunxiyue.com/zdh/api/user_management_api.php?action=delete',
        method: 'POST',
        data: {
          token: this.getAdminToken(),
          user_id: this.data.userId
        }
      });

      if (response.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  }
});
