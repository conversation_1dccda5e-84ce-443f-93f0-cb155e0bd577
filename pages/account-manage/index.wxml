<view class="container">
  <!-- 用户信息展示 -->
  <view class="user-info-section">
    <view class="user-avatar">
      <image src="/images/zhuye/wd.png" class="avatar-image" mode="aspectFill"></image>
    </view>
    <view class="user-details">
      <text class="username">{{userInfo.real_name || userInfo.username}}</text>
      <text class="user-role">{{userInfo.role === 'admin' ? '管理员' : '普通用户'}}{{userInfo.is_station_staff ? ' | 一站人员' : ''}}</text>
      <text class="user-account" wx:if="{{userInfo.real_name && userInfo.real_name !== userInfo.username}}">账号：{{userInfo.username}}</text>
    </view>
  </view>

  <!-- 账号管理功能 -->
  <view class="function-list">
    <!-- 账号安全 -->
    <view class="function-section">
      <view class="section-title">账号安全</view>
      
      <!-- 修改密码 -->
      <view class="function-item" bindtap="onChangePassword">
        <view class="function-icon">
          <text class="icon-emoji">🔐</text>
        </view>
        <view class="function-info">
          <text class="function-title">修改密码</text>
          <text class="function-desc">更改登录密码</text>
        </view>
        <view class="arrow-icon">
          <text class="iconfont">›</text>
        </view>
      </view>

      <!-- 修改真实姓名 -->
      <view class="function-item" bindtap="onChangeRealName">
        <view class="function-icon">
          <text class="icon-emoji">👤</text>
        </view>
        <view class="function-info">
          <text class="function-title">{{userInfo.real_name ? '修改真实姓名' : '设置真实姓名'}}</text>
          <text class="function-desc">{{userInfo.real_name ? '更改真实姓名' : '设置您的真实姓名'}}</text>
        </view>
        <view class="arrow-icon">
          <text class="iconfont">›</text>
        </view>
      </view>
    </view>

    <!-- 微信绑定 -->
    <view class="function-section">
      <view class="section-title">微信绑定</view>
      
      <!-- 解除微信绑定 -->
      <view class="function-item" bindtap="onUnbindWechat">
        <view class="function-icon">
          <text class="icon-emoji">🔗</text>
        </view>
        <view class="function-info">
          <text class="function-title">解除微信绑定</text>
          <text class="function-desc">取消微信快捷登录</text>
        </view>
        <view class="arrow-icon">
          <text class="iconfont">›</text>
        </view>
      </view>
    </view>

    <!-- 其他功能 -->
    <view class="function-section">
      <view class="section-title">其他功能</view>
      
      <!-- 登录记录 -->
      <view class="function-item" bindtap="onViewLoginHistory">
        <view class="function-icon">
          <text class="icon-emoji">📋</text>
        </view>
        <view class="function-info">
          <text class="function-title">登录记录</text>
          <text class="function-desc">查看登录历史</text>
        </view>
        <view class="arrow-icon">
          <text class="iconfont">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 修改密码弹窗 -->
  <view class="modal" wx:if="{{showChangePasswordModal}}">
    <view class="modal-mask" bindtap="closePasswordModal"></view>
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">修改密码</text>
        <view class="modal-close" bindtap="closePasswordModal">
          <text>×</text>
        </view>
      </view>
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">当前密码</text>
          <input class="form-input" type="password" password="true" placeholder="请输入当前密码" value="{{passwordForm.oldPassword}}" bindinput="onOldPasswordInput" />
        </view>
        <view class="form-group">
          <text class="form-label">新密码</text>
          <input class="form-input" type="password" password="true" placeholder="请输入新密码" value="{{passwordForm.newPassword}}" bindinput="onNewPasswordInput" />
        </view>
        <view class="form-group">
          <text class="form-label">确认新密码</text>
          <input class="form-input" type="password" password="true" placeholder="请再次输入新密码" value="{{passwordForm.confirmPassword}}" bindinput="onConfirmPasswordInput" />
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closePasswordModal">取消</button>
        <button class="modal-btn confirm" bindtap="confirmChangePassword" loading="{{loading}}">确认</button>
      </view>
    </view>
  </view>

  <!-- 修改真实姓名弹窗 -->
  <view class="modal" wx:if="{{showChangeRealNameModal}}">
    <view class="modal-mask" bindtap="closeRealNameModal"></view>
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{userInfo.real_name ? '修改真实姓名' : '设置真实姓名'}}</text>
        <view class="modal-close" bindtap="closeRealNameModal">
          <text>×</text>
        </view>
      </view>
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">真实姓名</text>
          <input class="form-input" placeholder="请输入您的真实姓名" value="{{realNameForm.newRealName}}" bindinput="onRealNameInput" maxlength="20" />
        </view>
        <view class="form-tip" wx:if="{{!userInfo.real_name}}">
          <text>设置真实姓名后，系统将优先显示您的真实姓名</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closeRealNameModal">取消</button>
        <button class="modal-btn confirm" bindtap="confirmChangeRealName" loading="{{loading}}">{{userInfo.real_name ? '确认修改' : '确认设置'}}</button>
      </view>
    </view>
  </view>
</view>
