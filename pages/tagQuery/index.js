Page({
  data: {
    activeTab: 'instrument', // 主选项卡：instrument(仪表位号), valve(阀门号)
    queryType: 'rcp', // 默认查询类型：rcp, junction, tag
    rcpName: '',
    junctionName: '',
    tagInfo: '',
    valveInfo: '', // 阀门查询信息
    showResult: false,
    noResult: false,
    rcpResults: [],
    junctionResults: [],
    tagResults: [],
    valveResults: [], // 阀门查询结果
    showValveResult: false, // 是否显示阀门查询结果
    noValveResult: false, // 是否没有阀门查询结果
    valveResultCount: 0, // 阀门查询结果数量
    resultCount: 0,
    showBackToTop: false,
    searchValue: '',
    searchResults: [],
    isLoggedIn: false,
    userInfo: null,
    isAdmin: false,
    userRole: '',
    isLoading: true, // 新增加载状态，控制界面显示
    isGuestMode: false, // 是否为游客模式
  },

  onLoad() {
    // 引入微信登录管理器
    const loginManager = getApp().globalData?.loginManager || require('../../utils/login.js');
    this.loginManager = loginManager;

    // 检查微信登录状态
    this.checkWechatLoginStatus();
  },

  onShow() {
    // 每次页面显示时，重新检查微信登录状态
    this.checkWechatLoginStatus();
  },
  
  // 检查微信登录状态
  checkWechatLoginStatus: function() {
    // 检查是否已通过微信登录并绑定系统账号
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isGuestMode = wx.getStorageSync('guest_mode');

    if (wechatToken && userInfo && !isGuestMode) {
      // 已登录并绑定系统账号，可以直接使用
      this.setData({
        isLoggedIn: true,
        isLoading: false,
        userInfo: {
          username: userInfo.username,
          real_name: userInfo.real_name
        },
        isAdmin: userInfo.role === 'admin' || userInfo.role === 'manager',
        userRole: userInfo.role
      });
    } else if (isGuestMode) {
      // 游客模式，需要绑定系统账号
      this.setData({
        isLoggedIn: false,
        isLoading: false,
        isGuestMode: true
      });
    } else {
      // 未登录，需要先登录
      this.setData({
        isLoggedIn: false,
        isLoading: false,
        isGuestMode: false
      });
    }
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.showModal({
      title: '需要登录',
      content: '请先在"我的"页面进行微信登录并绑定系统账号',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 提示绑定账号
  showBindTip: function() {
    wx.showModal({
      title: '需要绑定账号',
      content: '您当前是游客模式，需要绑定系统账号才能使用仪表位号查询功能',
      confirmText: '去绑定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 检查登录状态 - 已废弃，现在使用微信登录状态检查
  checkLoginStatus: function() {
    // 现在统一使用微信登录，这个方法已不再需要
    this.setData({
      isLoading: false
    });
  },



  // 处理登录 - 已废弃，现在使用微信登录
  handleLogin: function() {
    // 引导用户使用微信登录
    wx.showModal({
      title: '请使用微信登录',
      content: '为了更好的用户体验，请在"我的"页面使用微信登录功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },











  // 页面滚动时触发
  onPageScroll(res) {
    // 当滚动超过300px时显示返回顶部按钮
    this.setData({
      showBackToTop: res.scrollTop > 300
    });
  },

  // 切换查询类型
  switchQueryType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      queryType: type,
      showResult: false,
      noResult: false
    });
  },

  // 输入处理函数
  inputRcpName(e) {
    this.setData({ rcpName: e.detail.value });
  },

  inputJunctionName(e) {
    this.setData({ junctionName: e.detail.value });
  },

  inputTagInfo(e) {
    this.setData({ tagInfo: e.detail.value });
  },



  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // RCP柜查询
  queryRcp() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const name = this.data.rcpName.trim();
    if (!name) {
      wx.showToast({
        title: '请输入RCP柜名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '查询中...' });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    // 调用API查询RCP柜信息
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_rcp',
        name: name,
        token: wechatToken
      },
      success: (res) => {
        if (res.data.status === 'success' && res.data.data && res.data.data.length > 0) {
          this.setData({
            rcpResults: res.data.data,
            resultCount: res.data.data.length,
            showResult: true,
            noResult: false
          });
        } else {
          this.setData({
            showResult: false,
            noResult: true
          });
          
          // 如果有具体错误消息，显示给用户
          if (res.data.message) {
            wx.showToast({
              title: '查询失败: ' + res.data.message,
              icon: 'none',
              duration: 3000
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '查询失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 接线箱查询
  queryJunction() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const name = this.data.junctionName.trim();
    if (!name) {
      wx.showToast({
        title: '请输入接线箱名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '查询中...' });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    // 调用API查询接线箱信息
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_junction',
        name: name,
        token: wechatToken
      },
      success: (res) => {
        if (res.data.status === 'success' && res.data.data && res.data.data.length > 0) {
          this.setData({
            junctionResults: res.data.data,
            resultCount: res.data.data.length,
            showResult: true,
            noResult: false
          });
        } else {
          this.setData({
            showResult: false,
            noResult: true
          });
          
          // 如果有具体错误消息，显示给用户
          if (res.data.message) {
            wx.showToast({
              title: '查询失败: ' + res.data.message,
              icon: 'none',
              duration: 3000
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '查询失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 位号查询
  queryTag() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const info = this.data.tagInfo.trim();
    if (!info) {
      wx.showToast({
        title: '请输入位号或名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '查询中...' });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    // 调用API查询位号信息
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_tag',
        info: info,
        token: wechatToken
      },
      success: (res) => {
        if (res.data.status === 'success' && res.data.data && res.data.data.length > 0) {
          this.setData({
            tagResults: res.data.data,
            resultCount: res.data.data.length,
            showResult: true,
            noResult: false
          });
        } else {
          this.setData({
            showResult: false,
            noResult: true
          });
          
          // 简单显示错误信息
          const errMsg = res.data.message || '未找到匹配的位号';
          wx.showToast({
            title: errMsg,
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        this.setData({
          showResult: false,
          noResult: true
        });
        wx.showToast({
          title: '查询失败，请检查网络连接',
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 导航到RCP柜详情
  navigateToRcp(e) {
    const name = e.currentTarget.dataset.name;
    if (!name) return;
    
    // 切换到RCP柜查询
    this.setData({
      queryType: 'rcp',
      rcpName: name,
      showResult: false,
      noResult: false
    });
    
    // 执行查询
    this.queryRcp();
  },

  // 导航到接线箱详情
  navigateToJunction(e) {
    const name = e.currentTarget.dataset.name;
    if (!name) return;
    
    // 切换到接线箱查询
    this.setData({
      queryType: 'junction',
      junctionName: name,
      showResult: false,
      noResult: false
    });
    
    // 执行查询
    this.queryJunction();
  },

  // 导航到位号详情
  navigateToTag(e) {
    const code = e.currentTarget.dataset.code;
    if (!code) return;
    
    // 切换到位号查询
    this.setData({
      queryType: 'tag',
      tagInfo: code,
      showResult: false,
      noResult: false
    });
    
    // 执行查询
    this.queryTag();
  },

  // 返回顶部
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 修改搜索请求，添加token验证
  onSearch: function() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    if (!this.data.searchValue.trim()) {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '搜索中...',
    });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_tag',
        info: this.data.searchValue,
        token: wechatToken
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 'success') {
          this.setData({
            searchResults: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-仪表位号及阀门编号查询',
      path: '/pages/tagQuery/index'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-仪表位号及阀门编号查询',
      query: 'from=timeline'
    };
  },

  // 切换主选项卡 (仪表位号/阀门号)
  switchMainTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
      // 重置查询结果
      showResult: false,
      noResult: false,
      showValveResult: false,
      noValveResult: false
    });
  },

  // 阀门号输入
  inputValveInfo(e) {
    this.setData({
      valveInfo: e.detail.value,
      showValveResult: false,
      noValveResult: false
    });
  },

  // 复制阀门信息
  copyValveInfo(e) {
    const info = e.currentTarget.dataset.info;
    wx.setClipboardData({
      data: info,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 复制阀门全部信息
  copyAllValveInfo(e) {
    const index = e.currentTarget.dataset.valve;
    const valve = this.data.valveResults[index];
    
    if (!valve) return;
    
    // 组装完整的阀门信息文本
    let allInfo = `阀门编号：${valve.valve_no || ''}\n`;
    allInfo += `阀门名称：${valve.name || ''}\n`;
    allInfo += `平台名称：${valve.platform_name || '未知'}\n`;
    allInfo += `位置：${valve.location || '未知'}\n`;
    allInfo += `类型：${valve.type || '未知'}\n`;
    allInfo += `规格尺寸：${valve.spec || '未知'}\n`;
    allInfo += `流程类型：${valve.flow_type || '未知'}\n`;
    allInfo += `投产时间：${valve.operation_date || '未知'}\n`;
    allInfo += `法兰形式：${valve.flange_type || '未知'}\n`;
    allInfo += `法兰孔数：${valve.flange_holes || '未知'}\n`;
    allInfo += `密封面形式：${valve.seal_type || '未知'}\n`;
    allInfo += `垫子类型：${valve.gasket_type || '未知'}\n`;
    if (valve.remark) {
      allInfo += `备注：${valve.remark}`;
    }
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: allInfo,
      success: () => {
        wx.showToast({
          title: '已复制全部信息',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 查询阀门号
  queryValve() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const valveInfo = this.data.valveInfo.trim();
    if (!valveInfo) {
      wx.showToast({
        title: '请输入阀门编号或名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '查询中...',
    });

    // 获取微信登录token和用户信息
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_valve',
        valve_info: valveInfo,
        token: wechatToken,
        username: userInfo?.username
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.status === 'success') {
          const results = res.data.data || [];
          this.setData({
            valveResults: results,
            valveResultCount: results.length,
            showValveResult: true,
            noValveResult: results.length === 0
          });
          
          // 记录到历史搜索
          if (results.length > 0) {
            this.addToSearchHistory(valveInfo);
          }
          
          // 滚动到顶部
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 300
          });
        } else {
          // 显示错误信息
          this.setData({
            showValveResult: false,
            noValveResult: true
          });
          
          const message = res.data?.message || '查询失败，请稍后重试';
          wx.showToast({
            title: message,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        this.setData({
          showValveResult: false,
          noValveResult: true
        });
        wx.showToast({
          title: '网络错误，请检查网络连接',
          icon: 'none'
        });
      }
    });
  },

  // 添加到历史搜索
  addToSearchHistory(query) {
    // 实现添加到历史搜索的逻辑
  },

  // 复制所有阀门查询结果
  copyAllValveResults() {
    const valves = this.data.valveResults;
    
    if (!valves || valves.length === 0) {
      wx.showToast({
        title: '没有可复制的结果',
        icon: 'none'
      });
      return;
    }
    
    // 只复制阀门编号和名称，不带序号
    let allResults = '';
    valves.forEach((valve) => {
      allResults += `阀门编号：${valve.valve_no || ''}，阀门名称：${valve.name || ''}\n`;
    });
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: allResults,
      success: () => {
        wx.showToast({
          title: '已复制全部结果',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },
}); 