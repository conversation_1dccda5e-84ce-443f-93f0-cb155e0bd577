Page({
  data: {
    activeTab: 'instrument', // 主选项卡：instrument(仪表位号), valve(阀门号)
    queryType: 'rcp', // 默认查询类型：rcp, junction, tag
    rcpName: '',
    junctionName: '',
    tagInfo: '',
    valveInfo: '', // 阀门查询信息
    showResult: false,
    noResult: false,
    rcpResults: [],
    junctionResults: [],
    tagResults: [],
    valveResults: [], // 阀门查询结果
    showValveResult: false, // 是否显示阀门查询结果
    noValveResult: false, // 是否没有阀门查询结果
    valveResultCount: 0, // 阀门查询结果数量
    resultCount: 0,
    showBackToTop: false,
    searchValue: '',
    searchResults: [],
    isLoggedIn: false,
    userInfo: null,
    username: '',
    password: '',
    isAdmin: false,
    rememberUsername: false,
    rememberPassword: false,
    // 修改密码相关
    showPasswordModal: false,
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    userRole: '',
    isLoading: true, // 新增加载状态，控制界面显示
    isGuestMode: false, // 是否为游客模式
  },

  onLoad() {
    // 引入微信登录管理器
    const loginManager = getApp().globalData?.loginManager || require('../../utils/login.js');
    this.loginManager = loginManager;

    // 检查微信登录状态
    this.checkWechatLoginStatus();
  },

  onShow() {
    // 每次页面显示时，重新检查微信登录状态
    this.checkWechatLoginStatus();
  },
  
  // 检查微信登录状态
  checkWechatLoginStatus: function() {
    // 检查是否已通过微信登录并绑定系统账号
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');
    const isGuestMode = wx.getStorageSync('guest_mode');

    if (wechatToken && userInfo && !isGuestMode) {
      // 已登录并绑定系统账号，可以直接使用
      this.setData({
        isLoggedIn: true,
        isLoading: false,
        userInfo: {
          username: userInfo.username,
          real_name: userInfo.real_name
        },
        isAdmin: userInfo.role === 'admin' || userInfo.role === 'manager',
        userRole: userInfo.role
      });
    } else if (isGuestMode) {
      // 游客模式，需要绑定系统账号
      this.setData({
        isLoggedIn: false,
        isLoading: false,
        isGuestMode: true
      });
    } else {
      // 未登录，需要先登录
      this.setData({
        isLoggedIn: false,
        isLoading: false,
        isGuestMode: false
      });
    }
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.showModal({
      title: '需要登录',
      content: '请先在"我的"页面进行微信登录并绑定系统账号',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 提示绑定账号
  showBindTip: function() {
    wx.showModal({
      title: '需要绑定账号',
      content: '您当前是游客模式，需要绑定系统账号才能使用仪表位号查询功能',
      confirmText: '去绑定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        }
      }
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const token = wx.getStorageSync('userToken');
    const username = wx.getStorageSync('username');
    const role = wx.getStorageSync('userRole');
    
    if (token) {
      wx.request({
        url: 'https://sunxiyue.com/zdh/api/api.php',
        method: 'POST',
        data: {
          action: 'check_login',
          token: token,
          username: username
        },
        success: (res) => {
          if (res.data.status === 'success') {
            // 设置登录状态和管理员状态
            this.setData({
              isLoggedIn: true,
              isLoading: false,
              userInfo: {
                username: username
              },
              // 只有admin角色才能管理用户，manager和user不能管理
              isAdmin: role === 'admin' || role === 'manager',
              userRole: role
            });
          } else {
            this.silentLogout();
          }
        },
        fail: () => {
          this.silentLogout();
        }
      });
    } else {
      this.setData({
        isLoading: false
      });
    }
  },

  // 加载记住的账号和密码
  loadRememberedCredentials: function() {
    const rememberUsername = wx.getStorageSync('tagQueryRememberUsername') || false;
    const rememberPassword = wx.getStorageSync('tagQueryRememberPassword') || false;
    const savedUsername = wx.getStorageSync('tagQuerySavedUsername') || '';
    const savedPassword = wx.getStorageSync('tagQuerySavedPassword') || '';

    this.setData({
      rememberUsername: rememberUsername,
      rememberPassword: rememberPassword,
      username: rememberUsername ? savedUsername : '',
      password: rememberPassword ? savedPassword : ''
    });
  },

  // 切换记住账号
  toggleRememberUsername: function() {
    const newValue = !this.data.rememberUsername;
    this.setData({
      rememberUsername: newValue
    });

    // 如果取消记住账号，也要取消记住密码，并清除保存的数据
    if (!newValue) {
      this.setData({
        rememberPassword: false
      });
      wx.removeStorageSync('tagQuerySavedUsername');
      wx.removeStorageSync('tagQuerySavedPassword');
    }
  },

  // 切换记住密码
  toggleRememberPassword: function() {
    const newValue = !this.data.rememberPassword;

    // 如果勾选记住密码，自动勾选记住账号
    if (newValue) {
      this.setData({
        rememberUsername: true,
        rememberPassword: true
      });
    } else {
      this.setData({
        rememberPassword: false
      });
      // 如果取消记住密码，清除保存的密码
      wx.removeStorageSync('tagQuerySavedPassword');
    }
  },

  // 处理登录
  handleLogin: function() {
    const { username, password } = this.data;
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '登录中...',
    });

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/api.php',
      method: 'POST',
      data: {
        action: 'login',
        username: username,
        password: password,
        lock_type: 'ip_only' // 添加参数指示只锁定IP
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.status === 'success') {
          // 获取角色信息
          const role = res.data.data?.role || 'user';
          
          // 保存登录信息
          wx.setStorageSync('userToken', res.data.data.token);
          wx.setStorageSync('username', username);
          wx.setStorageSync('userRole', role);

          // 根据用户选择保存账号和密码
          this.saveCredentialsIfNeeded();

          this.setData({
            isLoggedIn: true,
            isLoading: false,
            userInfo: {
              username: username
            },
            isAdmin: role === 'admin' || role === 'manager',
            userRole: role,
            // 保持记住功能的状态，不要清空
            // rememberUsername 和 rememberPassword 保持当前值
          });

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          // 登录成功后自动搜索
          if (this.data.searchValue) {
            this.onSearch();
          }
        } else {
          // 判断是否为锁定消息
          const message = res.data?.message || '登录失败';
          const isLockMessage = message.includes('锁定') || message.includes('分钟后再试');
          
          // 对于锁定消息，使用modal提示，显示更详细的信息
          if (isLockMessage) {
            wx.showModal({
              title: '登录已锁定',
              content: message + '\n\n为了保护系统安全，连续错误5次后将锁定IP地址1小时。请稍后再试或联系管理员。',
              showCancel: false,
              confirmText: '我知道了',
              confirmColor: '#e74c3c'
            });
          } else {
            // 检查是否为数据库错误
            const isDBError = message.includes('数据库错误') || message.includes('不存在') || message.includes('登录服务');
            if (isDBError) {
              wx.showModal({
                title: '系统提示',
                content: message,
                showCancel: false
              });
            } else {
              // 普通错误消息使用toast提示
              wx.showToast({
                title: message,
                icon: 'none',
                duration: 3000
              });
            }
          }
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 保存账号和密码（如果用户选择了记住）
  saveCredentialsIfNeeded: function() {
    const { username, password, rememberUsername, rememberPassword } = this.data;

    // 保存记住选项的状态
    wx.setStorageSync('tagQueryRememberUsername', rememberUsername);
    wx.setStorageSync('tagQueryRememberPassword', rememberPassword);

    // 根据用户选择保存账号
    if (rememberUsername) {
      wx.setStorageSync('tagQuerySavedUsername', username);
    } else {
      wx.removeStorageSync('tagQuerySavedUsername');
    }

    // 根据用户选择保存密码
    if (rememberPassword) {
      wx.setStorageSync('tagQuerySavedPassword', password);
    } else {
      wx.removeStorageSync('tagQuerySavedPassword');
    }
  },

  // 显示修改密码弹窗
  showChangePassword: function() {
    this.setData({
      showPasswordModal: true,
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  },

  // 隐藏修改密码弹窗
  hideChangePassword: function() {
    this.setData({
      showPasswordModal: false
    });
  },

  // 处理修改密码
  handleChangePassword: function() {
    const { oldPassword, newPassword, confirmPassword } = this.data;
    
    // 检查输入是否为空
    if (!oldPassword || !newPassword || !confirmPassword) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }
    
    // 检查新密码长度
    if (newPassword.length < 6) {
      wx.showToast({
        title: '新密码长度至少6位',
        icon: 'none'
      });
      return;
    }
    
    // 检查两次密码是否一致
    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次密码输入不一致',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '修改中...'
    });
    
    const username = wx.getStorageSync('username');
    const token = wx.getStorageSync('userToken');
    
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/api.php',
      method: 'POST',
      data: {
        action: 'change_password',
        username: username,
        old_password: oldPassword,
        new_password: newPassword,
        token: token
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 'success') {
          this.hideChangePassword();
          wx.showToast({
            title: '密码修改成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.message || '修改失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },



  // 静默退出登录（不显示确认对话框）
  silentLogout: function() {
    // 清除登录信息
    wx.removeStorageSync('userToken');
    wx.removeStorageSync('username');
    wx.removeStorageSync('userRole');

    // 重新加载记住的凭据，保持记住功能的状态
    this.loadRememberedCredentials();

    this.setData({
      isLoggedIn: false,
      isLoading: false,
      userInfo: null,
      searchResults: [],
      searchValue: '',
      queryType: 'rcp',
      rcpName: '',
      junctionName: '',
      tagInfo: '',
      showResult: false,
      noResult: false,
      rcpResults: [],
      junctionResults: [],
      tagResults: [],
      resultCount: 0,
      showBackToTop: false,
      isAdmin: false
    });
  },

  // 处理退出登录
  handleLogout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const username = wx.getStorageSync('username');

          // 调用后端API记录退出操作
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/api.php',
            method: 'POST',
            data: {
              action: 'logout',
              username: username
            },
            success: (res) => {
              // 退出日志记录成功
            },
            fail: (err) => {
              // 退出日志记录失败
            },
            complete: () => {
              // 无论API调用成功与否，都执行退出操作
              // 清除登录信息
              wx.removeStorageSync('userToken');
              wx.removeStorageSync('username');
              wx.removeStorageSync('userRole');

              // 重新加载记住的凭据，保持记住功能的状态
              this.loadRememberedCredentials();

              this.setData({
                isLoggedIn: false,
                isLoading: false,
                userInfo: null,
                // 不要清空 username 和 password，让 loadRememberedCredentials 来处理
                searchResults: [],
                searchValue: '',
                queryType: 'rcp',
                rcpName: '',
                junctionName: '',
                tagInfo: '',
                showResult: false,
                noResult: false,
                rcpResults: [],
                junctionResults: [],
                tagResults: [],
                resultCount: 0,
                showBackToTop: false,
                isAdmin: false
              });
            }
          });
        }
      }
    });
  },

  // 页面滚动时触发
  onPageScroll(res) {
    // 当滚动超过300px时显示返回顶部按钮
    this.setData({
      showBackToTop: res.scrollTop > 300
    });
  },

  // 切换查询类型
  switchQueryType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      queryType: type,
      showResult: false,
      noResult: false
    });
  },

  // 输入处理函数
  inputRcpName(e) {
    this.setData({ rcpName: e.detail.value });
  },

  inputJunctionName(e) {
    this.setData({ junctionName: e.detail.value });
  },

  inputTagInfo(e) {
    this.setData({ tagInfo: e.detail.value });
  },

  // 用户名输入
  onInputUsername: function(e) {
    this.setData({
      username: e.detail.value
    });
  },

  // 密码输入
  onInputPassword: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // RCP柜查询
  queryRcp() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const name = this.data.rcpName.trim();
    if (!name) {
      wx.showToast({
        title: '请输入RCP柜名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '查询中...' });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    // 调用API查询RCP柜信息
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_rcp',
        name: name,
        token: wechatToken
      },
      success: (res) => {
        if (res.data.status === 'success' && res.data.data && res.data.data.length > 0) {
          this.setData({
            rcpResults: res.data.data,
            resultCount: res.data.data.length,
            showResult: true,
            noResult: false
          });
        } else {
          this.setData({
            showResult: false,
            noResult: true
          });
          
          // 如果有具体错误消息，显示给用户
          if (res.data.message) {
            wx.showToast({
              title: '查询失败: ' + res.data.message,
              icon: 'none',
              duration: 3000
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '查询失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 接线箱查询
  queryJunction() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const name = this.data.junctionName.trim();
    if (!name) {
      wx.showToast({
        title: '请输入接线箱名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '查询中...' });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    // 调用API查询接线箱信息
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_junction',
        name: name,
        token: wechatToken
      },
      success: (res) => {
        if (res.data.status === 'success' && res.data.data && res.data.data.length > 0) {
          this.setData({
            junctionResults: res.data.data,
            resultCount: res.data.data.length,
            showResult: true,
            noResult: false
          });
        } else {
          this.setData({
            showResult: false,
            noResult: true
          });
          
          // 如果有具体错误消息，显示给用户
          if (res.data.message) {
            wx.showToast({
              title: '查询失败: ' + res.data.message,
              icon: 'none',
              duration: 3000
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '查询失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 位号查询
  queryTag() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const info = this.data.tagInfo.trim();
    if (!info) {
      wx.showToast({
        title: '请输入位号或名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '查询中...' });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    // 调用API查询位号信息
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_tag',
        info: info,
        token: wechatToken
      },
      success: (res) => {
        if (res.data.status === 'success' && res.data.data && res.data.data.length > 0) {
          this.setData({
            tagResults: res.data.data,
            resultCount: res.data.data.length,
            showResult: true,
            noResult: false
          });
        } else {
          this.setData({
            showResult: false,
            noResult: true
          });
          
          // 简单显示错误信息
          const errMsg = res.data.message || '未找到匹配的位号';
          wx.showToast({
            title: errMsg,
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        this.setData({
          showResult: false,
          noResult: true
        });
        wx.showToast({
          title: '查询失败，请检查网络连接',
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 导航到RCP柜详情
  navigateToRcp(e) {
    const name = e.currentTarget.dataset.name;
    if (!name) return;
    
    // 切换到RCP柜查询
    this.setData({
      queryType: 'rcp',
      rcpName: name,
      showResult: false,
      noResult: false
    });
    
    // 执行查询
    this.queryRcp();
  },

  // 导航到接线箱详情
  navigateToJunction(e) {
    const name = e.currentTarget.dataset.name;
    if (!name) return;
    
    // 切换到接线箱查询
    this.setData({
      queryType: 'junction',
      junctionName: name,
      showResult: false,
      noResult: false
    });
    
    // 执行查询
    this.queryJunction();
  },

  // 导航到位号详情
  navigateToTag(e) {
    const code = e.currentTarget.dataset.code;
    if (!code) return;
    
    // 切换到位号查询
    this.setData({
      queryType: 'tag',
      tagInfo: code,
      showResult: false,
      noResult: false
    });
    
    // 执行查询
    this.queryTag();
  },

  // 返回顶部
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 修改搜索请求，添加token验证
  onSearch: function() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    if (!this.data.searchValue.trim()) {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '搜索中...',
    });

    // 获取微信登录token
    const wechatToken = wx.getStorageSync('wechat_token');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_tag',
        info: this.data.searchValue,
        token: wechatToken
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 'success') {
          this.setData({
            searchResults: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.message,
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '平台常用计算工具-仪表位号及阀门编号查询',
      path: '/pages/tagQuery/index'
    };
  },

  // 分享到朋友圈（需基础库 2.11.3+）
  onShareTimeline() {
    return {
      title: '平台常用计算工具-仪表位号及阀门编号查询',
      query: 'from=timeline'
    };
  },

  // 切换主选项卡 (仪表位号/阀门号)
  switchMainTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
      // 重置查询结果
      showResult: false,
      noResult: false,
      showValveResult: false,
      noValveResult: false
    });
  },

  // 阀门号输入
  inputValveInfo(e) {
    this.setData({
      valveInfo: e.detail.value,
      showValveResult: false,
      noValveResult: false
    });
  },

  // 复制阀门信息
  copyValveInfo(e) {
    const info = e.currentTarget.dataset.info;
    wx.setClipboardData({
      data: info,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 复制阀门全部信息
  copyAllValveInfo(e) {
    const index = e.currentTarget.dataset.valve;
    const valve = this.data.valveResults[index];
    
    if (!valve) return;
    
    // 组装完整的阀门信息文本
    let allInfo = `阀门编号：${valve.valve_no || ''}\n`;
    allInfo += `阀门名称：${valve.name || ''}\n`;
    allInfo += `平台名称：${valve.platform_name || '未知'}\n`;
    allInfo += `位置：${valve.location || '未知'}\n`;
    allInfo += `类型：${valve.type || '未知'}\n`;
    allInfo += `规格尺寸：${valve.spec || '未知'}\n`;
    allInfo += `流程类型：${valve.flow_type || '未知'}\n`;
    allInfo += `投产时间：${valve.operation_date || '未知'}\n`;
    allInfo += `法兰形式：${valve.flange_type || '未知'}\n`;
    allInfo += `法兰孔数：${valve.flange_holes || '未知'}\n`;
    allInfo += `密封面形式：${valve.seal_type || '未知'}\n`;
    allInfo += `垫子类型：${valve.gasket_type || '未知'}\n`;
    if (valve.remark) {
      allInfo += `备注：${valve.remark}`;
    }
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: allInfo,
      success: () => {
        wx.showToast({
          title: '已复制全部信息',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 查询阀门号
  queryValve() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      if (this.data.isGuestMode) {
        this.showBindTip();
      } else {
        this.goToLogin();
      }
      return;
    }

    const valveInfo = this.data.valveInfo.trim();
    if (!valveInfo) {
      wx.showToast({
        title: '请输入阀门编号或名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '查询中...',
    });

    // 获取微信登录token和用户信息
    const wechatToken = wx.getStorageSync('wechat_token');
    const userInfo = wx.getStorageSync('user_info');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/wechat_api.php',
      method: 'POST',
      data: {
        action: 'query_valve',
        valve_info: valveInfo,
        token: wechatToken,
        username: userInfo?.username
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.status === 'success') {
          const results = res.data.data || [];
          this.setData({
            valveResults: results,
            valveResultCount: results.length,
            showValveResult: true,
            noValveResult: results.length === 0
          });
          
          // 记录到历史搜索
          if (results.length > 0) {
            this.addToSearchHistory(valveInfo);
          }
          
          // 滚动到顶部
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 300
          });
        } else {
          // 显示错误信息
          this.setData({
            showValveResult: false,
            noValveResult: true
          });
          
          const message = res.data?.message || '查询失败，请稍后重试';
          wx.showToast({
            title: message,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        this.setData({
          showValveResult: false,
          noValveResult: true
        });
        wx.showToast({
          title: '网络错误，请检查网络连接',
          icon: 'none'
        });
      }
    });
  },

  // 添加到历史搜索
  addToSearchHistory(query) {
    // 实现添加到历史搜索的逻辑
  },

  // 复制所有阀门查询结果
  copyAllValveResults() {
    const valves = this.data.valveResults;
    
    if (!valves || valves.length === 0) {
      wx.showToast({
        title: '没有可复制的结果',
        icon: 'none'
      });
      return;
    }
    
    // 只复制阀门编号和名称，不带序号
    let allResults = '';
    valves.forEach((valve) => {
      allResults += `阀门编号：${valve.valve_no || ''}，阀门名称：${valve.name || ''}\n`;
    });
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: allResults,
      success: () => {
        wx.showToast({
          title: '已复制全部结果',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },
}); 