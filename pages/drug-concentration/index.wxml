<!-- index.wxml -->
<view class="container">

  <!-- 计算方法一：计算加药量 -->
  <view class="sub-title">计算加药量</view>
  <view class="input-section">
    <view class="form-header">
      <view class="form-header-title">
        <text class="form-header-icon" style="color: #8b5cf6;">⚗️</text>
        <text>浓度参数</text>
      </view>
    </view>
    <view class="form-content">
      <view class="input-group">
        <text class="input-label">液量 (m³/d)</text>
        <input
          type="digit"
          placeholder="请输入液量"
          value="{{liquidVolume}}"
          data-field="liquidVolume"
          bindinput="handleInput"
          class="input-field"
        />
        <view class="input-desc">每日处理的液体量</view>
      </view>

      <view class="input-row">
        <view class="input-group">
          <text class="input-label">加药浓度 (mg/L)</text>
          <input
            type="digit"
            placeholder="请输入浓度"
            value="{{drugConcentration}}"
            data-field="drugConcentration"
            bindinput="handleInput"
            class="input-field"
          />
          <view class="input-desc">目标药剂浓度</view>
        </view>

        <view class="input-group">
          <text class="input-label">允许范围 (%)</text>
          <input
            type="digit"
            placeholder="默认10"
            value="{{allowedRange}}"
            data-field="allowedRange"
            bindinput="handleInput"
            class="input-field"
          />
          <view class="input-desc">浓度允许偏差范围</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加药量结果 -->
  <view class="result-box">
    <view class="result-row">
      <view class="result-item">
        <text class="label">标准加药量</text>
        <text class="value">{{concentrationResults.dosage}} L/d</text>
      </view>
    </view>
    <view class="result-row">
      <view class="result-item">
        <text class="label">最小加药量</text>
        <text class="value">{{concentrationResults.minDosage}} L/d</text>
      </view>
      <view class="result-item">
        <text class="label">最大加药量</text>
        <text class="value">{{concentrationResults.maxDosage}} L/d</text>
      </view>
    </view>
  </view>

  <!-- 计算方法二：计算加药浓度 -->
  <view class="sub-title">计算加药浓度</view>
  <view class="input-section">
    <view class="form-header">
      <view class="form-header-title">
        <text class="form-header-icon" style="color: #f59e0b;">💊</text>
        <text>加药参数</text>
      </view>
    </view>
    <view class="form-content">
      <view class="input-row">
        <view class="input-group">
          <text class="input-label">液量 (m³/d)</text>
          <input
            type="digit"
            placeholder="请输入液量"
            value="{{liquidVolume2}}"
            data-field="liquidVolume2"
            bindinput="handleInput"
            class="input-field"
          />
          <view class="input-desc">每日处理的液体量</view>
        </view>

        <view class="input-group">
          <text class="input-label">加药量 (L/d)</text>
          <input
            type="digit"
            placeholder="请输入加药量"
            value="{{drugDosage}}"
            data-field="drugDosage"
            bindinput="handleInput"
            class="input-field"
          />
          <view class="input-desc">实际每日加药量</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 浓度结果 -->
  <view class="result-box compact">
    <view class="result-row">
      <view class="result-item compact-item">
        <text class="value total-value">加药浓度：{{concentrationResults.concentration}} mg/L</text>
      </view>
    </view>
  </view>

</view>
