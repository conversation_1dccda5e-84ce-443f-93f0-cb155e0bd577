// index.js
Page({
  data: {
    // 药剂浓度计算数据
    liquidVolume: '',
    drugConcentration: '',
    allowedRange: '10',
    liquidVolume2: '',
    drugDosage: '',
    concentrationResults: {
      dosage: '0.00',
      minDosage: '0.00',
      maxDosage: '0.00',
      concentration: '0.00'
    }
  },

  // 输入处理
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [field]: value
    });

    // 实时计算
    if (field === 'liquidVolume' || field === 'drugConcentration' || field === 'allowedRange') {
      this.calculateDosage();
    }
    if (field === 'liquidVolume2' || field === 'drugDosage') {
      this.calculateDrugConcentration();
    }
  },

  // 计算加药量
  calculateDosage() {
    const { liquidVolume, drugConcentration, allowedRange } = this.data;
    const volume = parseFloat(liquidVolume) || 0;
    const concentration = parseFloat(drugConcentration) || 0;
    const range = parseFloat(allowedRange) || 0;
    
    // 加药量(L/d) = 液量(m³/d) * 浓度(mg/L) / 1000
    let dosage = '0.00';
    let minDosage = '0.00';
    let maxDosage = '0.00';
    
    if (volume > 0 && concentration > 0) {
      const baseDosage = volume * concentration / 1000;
      dosage = baseDosage.toFixed(2);
      
      // 计算允许范围
      if (range > 0) {
        const rangeValue = baseDosage * range / 100;
        minDosage = (baseDosage - rangeValue).toFixed(2);
        maxDosage = (baseDosage + rangeValue).toFixed(2);
      }
    }
    
    this.setData({
      concentrationResults: {
        ...this.data.concentrationResults,
        dosage,
        minDosage,
        maxDosage
      }
    });
  },

  // 计算加药浓度
  calculateDrugConcentration() {
    const { liquidVolume2, drugDosage } = this.data;
    const volume = parseFloat(liquidVolume2) || 0;
    const dosage = parseFloat(drugDosage) || 0;
    
    // 加药浓度(mg/L) = 加药量(L/d) * 1000 / 液量(m³/d)
    let concentration = '0.00';
    if (volume > 0) {
      concentration = (dosage * 1000 / volume).toFixed(2);
    }
    
    this.setData({
      concentrationResults: {
        ...this.data.concentrationResults,
        concentration
      }
    });
  },

  onShareAppMessage() {
    return {
      title: '平台常用计算工具-药剂浓度计算器',
      path: '/pages/drug-concentration/index'
    };
  },

  onShareTimeline() {
    return {
      title: '平台常用计算工具-药剂浓度计算器',
      query: 'from=timeline'
    };
  }
});
