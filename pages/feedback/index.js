Page({
  data: {
    feedbackContent: '',
    contactInfo: '',
    feedbackType: '建议',
    typeOptions: ['建议', '问题反馈', '功能需求', '其他']
  },

  onLoad: function (options) {
    
  },

  // 选择反馈类型
  onTypeChange: function(e) {
    this.setData({
      feedbackType: this.data.typeOptions[e.detail.value]
    });
  },

  // 输入反馈内容
  onContentInput: function(e) {
    this.setData({
      feedbackContent: e.detail.value
    });
  },

  // 输入联系方式
  onContactInput: function(e) {
    this.setData({
      contactInfo: e.detail.value
    });
  },

  // 提交反馈
  onSubmit: function() {
    if (!this.data.feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    // 这里可以添加提交到服务器的逻辑
    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      // 清空表单
      this.setData({
        feedbackContent: '',
        contactInfo: '',
        feedbackType: '建议'
      });
    }, 1500);
  }
});
