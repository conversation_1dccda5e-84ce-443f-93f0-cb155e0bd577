<view class="container">
  <view class="feedback-form">
    <!-- 反馈类型 -->
    <view class="form-item">
      <view class="form-label">反馈类型</view>
      <picker bindchange="onTypeChange" value="{{feedbackType}}" range="{{typeOptions}}">
        <view class="picker-content">
          <text class="picker-text">{{feedbackType}}</text>
          <text class="picker-arrow">›</text>
        </view>
      </picker>
    </view>

    <!-- 反馈内容 -->
    <view class="form-item">
      <view class="form-label">反馈内容</view>
      <textarea 
        class="feedback-textarea" 
        placeholder="请详细描述您的问题或建议..." 
        value="{{feedbackContent}}"
        bindinput="onContentInput"
        maxlength="500"
        show-confirm-bar="{{false}}"
        auto-height
      ></textarea>
      <view class="char-count">{{feedbackContent.length}}/500</view>
    </view>

    <!-- 联系方式 -->
    <view class="form-item">
      <view class="form-label">联系方式（可选）</view>
      <input 
        class="feedback-input" 
        placeholder="请输入您的联系方式，便于我们回复" 
        value="{{contactInfo}}"
        bindinput="onContactInput"
        maxlength="50"
      />
    </view>

    <!-- 提交按钮 -->
    <button class="submit-btn" bindtap="onSubmit">提交反馈</button>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-section">
    <view class="tips-title">温馨提示</view>
    <view class="tips-content">
      <text class="tip-item">• 我们会认真对待每一条反馈</text>
      <text class="tip-item">• 如需回复，请留下您的联系方式</text>
      <text class="tip-item">• 感谢您为产品改进提出宝贵意见</text>
    </view>
  </view>
</view>
