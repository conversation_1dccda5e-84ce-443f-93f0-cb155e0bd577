page {
  background: #f2f2f7;
  min-height: 100vh;
}

.container {
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部标题 */
.header {
  text-align: center;
  margin-bottom: 24rpx;
  padding: 88rpx 40rpx 48rpx;
  background: #ffffff;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 16rpx;
  letter-spacing: -0.5rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #86868b;
  font-weight: 400;
  line-height: 1.4;
}

/* 作业类型列表 */
.operations-list {
  margin-bottom: 40rpx;
  padding: 0 32rpx;
}

.operation-item {
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

/* 作业类型标题 */
.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 32rpx;
  background: #ffffff;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1rpx solid #f2f2f7;
}

.operation-header:active {
  background: #f9f9f9;
}

.operation-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d1d1f;
  flex: 1;
  letter-spacing: -0.5rpx;
}

.toggle-icon {
  font-size: 28rpx;
  color: #86868b;
  transition: transform 0.3s ease;
  margin-left: 16rpx;
  font-weight: 300;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 作业内容 */
.operation-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: #ffffff;
}

.operation-content.show {
  max-height: 2000rpx;
}

.operation-content > view {
  padding: 0 32rpx;
}

.operation-content > view:last-child {
  padding-bottom: 40rpx;
}

/* 定义和级别 */
.definition, .level {
  margin-bottom: 32rpx;
  padding-top: 24rpx;
}

.label {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
  display: inline-block;
  margin-bottom: 16rpx;
  letter-spacing: -0.5rpx;
}

.content {
  font-size: 32rpx;
  color: #424245;
  line-height: 1.7;
  text-align: justify;
  font-weight: 400;
}

/* 级别详情 */
.level-details {
  padding-top: 8rpx;
}

.level-item {
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx;
  background: #f9f9f9;
  border-radius: 12rpx;
  border: none;
}

.level-item:last-child {
  margin-bottom: 0;
}

.level-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 12rpx;
  letter-spacing: -0.3rpx;
}

.level-desc {
  font-size: 30rpx;
  color: #424245;
  line-height: 1.6;
  text-align: justify;
  font-weight: 400;
}

/* 底部说明 */
.footer {
  text-align: center;
  padding: 48rpx 40rpx 88rpx;
  background: #ffffff;
  margin-top: 24rpx;
}

.footer-text {
  font-size: 28rpx;
  color: #86868b;
  line-height: 1.6;
  font-weight: 400;
}
