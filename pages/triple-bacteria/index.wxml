<!-- index.wxml -->
<view class="container">
  <!-- 隐藏的canvas，用于生成分享图片 -->
  <canvas type="2d" id="shareCanvas" style="width: 300px; height: 400px; position: absolute; left: -2000px; top: 0px;"></canvas>

  <!-- 计算器类型选择 -->
  <view class="calculator-section">
    <view class="calculator-type">
      <view class="type-item {{calculatorType === 'single' ? 'active' : ''}}" bindtap="switchCalculatorType" data-type="single">单次计算</view>
      <view class="type-item {{calculatorType === 'batch' ? 'active' : ''}}" bindtap="switchCalculatorType" data-type="batch">批量计算</view>
    </view>

    <!-- 单次计算器 -->
    <view class="calculator-form" wx:if="{{calculatorType === 'single'}}">
      <!-- 使用说明 -->
      <view class="instruction">
        <view class="instruction-content">
          <text class="instruction-title">使用说明：</text>
          <view class="instruction-item">1、依次从1号瓶到6号瓶开始点击</view>
          <view class="instruction-item">
            <text>2、根据培养结果点击对应数量的</text>
            <view class="example-circle"/>
          </view>
          <view class="instruction-item">3、完成后点击开始计算按钮</view>
        </view>
        <text class="notice-text">（注意：必须按瓶号顺序选择，前瓶未选后瓶不可操作）</text>
      </view>

      <view class="bottle-columns">
        <view class="bottle-column" wx:for="{{6}}" wx:for-item="i" wx:key="i">
          <text class="bottle-label">{{i+1}}号瓶</text>
          <view class="circle-group">
            <view 
              wx:for="{{3}}" 
              wx:key="idx"
              class="circle {{selectedCircles[i] && selectedCircles[i][index] ? 'selected' : ''}} {{i > 0 && selectedCount[i-1] === 0 ? 'disabled' : ''}}"
              data-index="{{i}}"
              data-circle="{{index}}"
              bindtap="onCircleTap"
            ></view>
          </view>
        </view>
      </view>

      <button class="calc-btn" bindtap="calculate">开始计算</button>

      <view class="result-box {{resultClass}}">
        <view class="result-item">
          <text class="label">生长指标：</text>
          <text class="value">{{growthIndicator}}</text>
        </view>
        <view class="result-item">
          <text class="label">级数：</text>
          <text class="value">10^{{magnitudeLevel}}</text>
        </view>
        <view class="result-item">
          <text class="label">细菌数：</text>
          <text class="value">{{bacteriaCount}} 个/mL</text>
        </view>
        <view class="result-item">
          <text class="label">化验结果：</text>
          <text class="value">{{testResult}}</text>
        </view>
        <view class="standard">
          合格要求：细菌数 ≤25 个/mL
        </view>
      </view>
    </view>

    <!-- 批量计算页面 -->
    <view class="calculator-form" wx:if="{{calculatorType === 'batch'}}">
      <!-- 化验信息区域 -->
      <view class="test-info-container">
        <view class="test-info-item vertical">
          <view class="label-block">化验时间</view>
          <picker mode="date" value="{{testStartDate}}" bindchange="changeTestStartDate">
            <view class="picker {{testStartDate ? '' : 'placeholder'}}">
              <view class="date-text">{{testStartDate || '选择日期'}}</view>
            </view>
          </picker>
        </view>
        <view class="test-info-item vertical">
          <view class="label-block">结束时间</view>
          <picker mode="date" value="{{testEndDate}}" bindchange="changeTestEndDate">
            <view class="picker {{testEndDate ? '' : 'placeholder'}}">
              <view class="date-text">{{testEndDate || '选择日期'}}</view>
            </view>
          </picker>
        </view>
        <view class="test-info-item vertical">
          <view class="label-block">三菌类型</view>
          <picker range="{{bacteriaTypeList}}" value="{{bacteriaTypeIndex}}" bindchange="changeBacteriaType">
            <view class="picker">
              {{bacteriaTypeList[bacteriaTypeIndex]}}
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 滚动区域开始 -->
      <scroll-view scroll-y="true" class="batch-scroll-area">
        <!-- 批量计算表格 -->
        <view class="batch-table">
          <view class="table-header">
            <view class="table-cell cell-mini">序号</view>
            <view class="table-cell">取样地点</view>
            <view class="table-cell">化验现象</view>
            <view class="table-cell">生长指标</view>
            <view class="table-cell">级数</view>
            <view class="table-cell">细菌个数(个/mL)</view>
            <view class="table-cell">化验结果</view>
            <view class="table-cell cell-mini">操作</view>
          </view>
          
          <block wx:for="{{batchRecords}}" wx:key="index">
            <view class="table-row">
              <view class="table-cell cell-mini">{{index + 1}}</view>
              <view class="table-cell position-relative">
                <picker wx:if="{{item.locationIndex !== 3}}" bindchange="changeSampleLocation" data-index="{{index}}" value="{{item.locationIndex}}" range="{{sampleLocations}}">
                  <view class="picker">
                    {{sampleLocations[item.locationIndex]}}
                  </view>
                </picker>
                <!-- 自定义地点名称输入框，当选择"自定义"选项时显示 -->
                <input wx:if="{{item.locationIndex === 3}}" class="location-custom-input standalone" type="text" placeholder="输入地点名称" bindinput="inputCustomLocationName" data-index="{{index}}" value="{{item.locationCustomName}}"/>
                <!-- 返回选择器按钮 -->
                <view wx:if="{{item.locationIndex === 3}}" class="return-to-picker" bindtap="returnToPicker" data-index="{{index}}">
                  <text>↺</text>
                </view>
              </view>
              <view class="table-cell">
                <view bindtap="openBottleSelector" data-index="{{index}}" class="test-phenomenon-cell">
                  {{item.bottlesDisplay ? '查看' : '点击选择'}}
                </view>
              </view>
              <view class="table-cell">
                <text>{{item.growthIndicator || '-'}}</text>
              </view>
              <view class="table-cell">
                <text>{{item.magnitudeLevel !== '' && item.magnitudeLevel !== null && item.magnitudeLevel !== undefined ? ('10^' + item.magnitudeLevel) : '-'}}</text>
              </view>
              <view class="table-cell">
                <text>{{item.bacteriaCount || '-'}}</text>
              </view>
              <view class="table-cell">
                <text class="{{item.testResult === '合格' ? 'qualified' : (item.testResult === '不合格' ? 'unqualified' : '')}}">{{item.testResult || '-'}}</text>
              </view>
              <view class="table-cell cell-mini">
                <view class="delete-btn" bindtap="deleteRow" data-index="{{index}}">×</view>
              </view>
            </view>
          </block>
        </view>
      </scroll-view>
      <!-- 滚动区域结束 -->
      
      <!-- 操作按钮 -->
      <view class="batch-action-buttons">
        <view class="action-btn" bindtap="addNewRow">添加一行</view>
      </view>
      
      <view class="batch-buttons">
        <view class="save-btn" bindtap="saveBatchResults">保存</view>
        <view class="clear-btn" bindtap="clearAllBatchRecords">清除全部</view>
      </view>
      
      <!-- 分享按钮 -->
      <view class="share-button-container">
        <view class="share-btn" bindtap="generateAndShareTableImage">分享表格截图</view>
      </view>
    </view>
  </view>
  
  <!-- 瓶子选择弹窗 -->
  <view class="bottle-popup" wx:if="{{showBottleSelector}}">
    <view class="popup-content">
      <view class="popup-title">选择培养结果</view>
      <view class="popup-subtitle">（为每个瓶子选择阳性反应数量）</view>
      
      <view class="popup-bottle-grid">
        <view class="popup-bottle-item" wx:for="{{6}}" wx:for-item="i" wx:key="i">
          <text class="popup-bottle-label">{{i+1}}号瓶</text>
          <view class="popup-circle-group">
            <view 
              wx:for="{{3}}" 
              wx:key="idx"
              class="circle {{popupSelectedCircles[i] && popupSelectedCircles[i][index] ? 'selected' : ''}} {{i > 0 && popupSelectedCount[i-1] === 0 ? 'disabled' : ''}}"
              data-index="{{i}}"
              data-circle="{{index}}"
              bindtap="onPopupCircleTap"
            ></view>
          </view>
        </view>
      </view>
      
      <view class="popup-buttons">
        <button class="popup-btn cancel" bindtap="closeBottleSelector">取消</button>
        <button class="popup-btn confirm" bindtap="confirmBottleSelection">确定</button>
      </view>
    </view>
  </view>
</view>