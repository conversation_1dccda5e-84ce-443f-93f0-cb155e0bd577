/* index.wxss */
/* 新增标题样式 */
.title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin: 5rpx 0 10rpx; /* 减少顶部间距 */
  padding: 15rpx 0;
  border-bottom: 2rpx solid #eee;
  flex-shrink: 0;
}

/* 新增使用说明样式 */
.instruction {
  background: #f0faff;
  border: 1rpx solid #91d5ff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.instruction-content {
  display: block;
}

.instruction-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin: 12rpx 0;
  line-height: 1.4;
}

.instruction-title {
  display: block;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.example-circle {
  width: 28rpx;  /* 调整后尺寸 */
  height: 28rpx;
  border: 3rpx solid #333;
  border-radius: 50%;
  margin-left: 12rpx; /* 精准间距 */
  flex-shrink: 0;
}

.notice-text {
  display: block;
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
}

.container {
  padding: 20rpx;
  height: 100vh; /* 固定高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 隐藏溢出 */
}

.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.input-group text {
  flex: 1;
  font-size: 28rpx;
}

.picker {
  min-width: 140rpx;
  text-align: center;
  padding: 15rpx;
  background: #fff;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
}

.calc-btn {
  background: #2196F3;
  color: white;
  border-radius: 8rpx;
  margin: 40rpx 0;
}

.result-box {
    padding: 20rpx; /* 减少内边距 */
    border-radius: 12rpx;
    background: #f8f9fa;
    flex-shrink: 0; /* 禁止收缩 */
    overflow-y: auto; /* 允许内容滚动 */
    max-height: 30vh; /* 限制最大高度 */
  }

.result-item {
  display: flex;
  margin: 20rpx 0;
  font-size: 28rpx;
}

.label {
  width: 180rpx;
  color: #666;
}

.value {
  flex: 1;
  color: #333;
  font-weight: 500;
}

.error .value {
  color: #ff9800 !important; /* 橙色修正 */
}

.disabled {
  background-color: #f5f5f5;
  color: #cccccc;
}

.standard {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  color: #666;
  font-size: 26rpx;
}


.qualified .value { color: #4CAF50; }
.unqualified .value { color: #F44336; }

/* 新增圆圈样式 */
.bottle-columns {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx; /* 减少底部间距 */
  flex-shrink: 0; /* 禁止收缩 */
}

.bottle-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10rpx;
}

.bottle-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  text-align: center;
}

.circle-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.circle {
  width: 50rpx;
  height: 50rpx;
  border: 4rpx solid #ddd;
  border-radius: 50%;
  transition: all 0.2s;
}

.circle.selected {
  background: #333;
  border-color: #333;
}

.circle.disabled {
  border-color: #eee;
  background: #fafafa;
  pointer-events: none;
}

.calc-btn {
  background: #2196F3;
  color: white;
  border-radius: 8rpx;
  margin: 20rpx 0; /* 减少上下间距 */
  width: 80%;
  align-self: center;
  flex-shrink: 0; /* 禁止收缩 */
}

/* 新增滚动容器 */
.instruction {
  flex-shrink: 0; /* 禁止收缩 */
}

/* 计算器类型选择样式 */
.calculator-section {
  width: 100%;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.calculator-type {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.type-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 30rpx;
  color: #7f8c8d;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.type-item.active {
  color: #3498db;
  border-bottom: 4rpx solid #3498db;
  font-weight: bold;
}

/* 批量计算表格样式 */
.batch-table {
  width: 100%;
  border: 1rpx solid #eaeaea;
  border-radius: 10rpx;
  margin-top: 30rpx;
  overflow: hidden;
  font-size: 26rpx;
}

.table-header {
  display: flex;
  background-color: #f7f9fc;
  font-weight: bold;
  border-bottom: 1rpx solid #eaeaea;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eaeaea;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 20rpx 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 26rpx;
  overflow: hidden;
  border-right: 1rpx solid #eaeaea;
  word-break: break-all;
}

.table-cell:last-child {
  border-right: none;
}

.cell-mini {
  flex: 0.25;
  padding: 20rpx 2rpx;
  font-size: 24rpx;
}

.table-input {
  width: 100%;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  padding: 0 10rpx;
  font-size: 26rpx;
  text-align: center;
  background-color: #f8f9fa;
}

/* 化验信息区域样式 */
.test-info-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  width: 100%;
}

.test-info-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding-right: 15rpx;
}

/* 垂直布局的表单项 */
.test-info-item.vertical {
  flex-direction: column;
  align-items: flex-start;
  padding-bottom: 0;
}

.label-block {
  display: block;
  width: 100%;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

/* 调整选择器和输入框在垂直布局中的样式 */
.test-info-item.vertical .picker,
.test-info-item.vertical .input-box {
  height: 88rpx;
  line-height: 88rpx;
  width: 100%;
  box-sizing: border-box;
  border: 1rpx solid #e0e5ea;
  background-color: #f7f9fc;
  border-radius: 6rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
}

.test-info-item:first-child {
  flex: 1.1; /* 轻微增加日期选择器的宽度比例 */
}

.test-info-item:last-child {
  padding-right: 0;
  flex: 0.9; /* 轻微减少化验人员宽度比例 */
}

/* 日期选择器特定样式 */
.test-info-item.vertical .picker {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30rpx; /* 为箭头腾出空间 */
}

.placeholder {
  color: #aaa;
}

.date-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10rpx;
}

/* 操作按钮样式 */
.batch-action-buttons {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
}

.action-btn {
  width: 60%;
  height: 70rpx;
  background: linear-gradient(135deg, #ffb74d, #f57c00);
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 35rpx;
  box-shadow: 0 4rpx 12rpx rgba(245, 124, 0, 0.3);
  transition: all 0.3s;
}

.action-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(245, 124, 0, 0.3);
}

/* 修改保存和清除按钮布局 */
.batch-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.batch-buttons .save-btn,
.batch-buttons .clear-btn {
  width: 48%;
  margin-top: 0;
}

.save-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(46, 204, 113, 0.3);
  transition: all 0.3s;
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(46, 204, 113, 0.3);
}

.clear-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.3);
  transition: all 0.3s;
}

.clear-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(231, 76, 60, 0.3);
}

/* 删除按钮样式 */
.delete-btn {
  width: 28rpx;
  height: 28rpx;
  background-color: #f5f5f5;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20rpx;
  box-shadow: none;
  transition: all 0.2s;
}

.delete-btn:active {
  transform: scale(0.9);
  background-color: #e0e0e0;
  color: #e74c3c;
}

/* 分享按钮样式 */
.share-button-container {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.share-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3498db, #1a5276);
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
  transition: all 0.3s;
}

.share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(52, 152, 219, 0.3);
}

/* 自定义地点输入框样式 */
.location-custom-input {
  width: 100%;
  height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  margin-top: 10rpx;
  padding: 0 10rpx;
  font-size: 26rpx;
  background-color: #f8f9fa;
}

/* 独立模式的自定义地点输入框 */
.location-custom-input.standalone {
  margin-top: 0;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  width: calc(100% - 60rpx); /* 为返回按钮留出空间 */
}

/* 返回选择器按钮 */
.return-to-picker {
  position: absolute;
  right: 15rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #555;
  transition: all 0.3s;
}

.return-to-picker:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

/* 相对定位容器 */
.position-relative {
  position: relative;
}

/* 化验现象表格单元格点击样式 */
.test-phenomenon-cell {
  width: 100%;
  min-height: 60rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  padding: 10rpx;
  font-size: 24rpx;
  text-align: center;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-phenomenon-cell:active {
  background-color: #e8e8e8;
}

/* 瓶子选择弹窗样式 */
.bottle-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.popup-content {
  width: 90%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  overflow-y: auto;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10rpx;
}

.popup-subtitle {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-bottle-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.popup-bottle-item {
  width: 30%;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-bottle-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.popup-circle-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.popup-btn {
  width: 48%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.popup-btn.cancel {
  background-color: #f1f1f1;
  color: #666;
}

.popup-btn.confirm {
  background-color: #2196F3;
  color: white;
}

/* 批量表格滚动区域 */
.batch-scroll-area {
  flex: 1;
  min-height: 200rpx;
  max-height: 60vh;
  overflow-y: auto;
  margin-bottom: 20rpx;
}