// index.js
const { bacteriaData } = require('../../utils/bacteriaData');

Page({
  data: {
    // 基本数据
    selectedCircles: new Array(6).fill().map(() => new Array(3).fill(false)),
    selectedCount: new Array(6).fill(0),
    growthIndicator: '--',
    magnitudeLevel: '--',
    bacteriaCount: '--',
    testResult: '等待计算',
    resultClass: '',
    
    // 计算器类型
    calculatorType: 'single', // 'single'单次计算 或 'batch'批量计算
    
    // 批量计算相关数据
    testStartDate: '',
    testEndDate: '',
    sampleLocations: ['海三来水', '平台分水', '注水泵出水', '自定义'],
    batchRecords: [],
    
    // 弹窗相关
    showBottleSelector: false,
    currentEditingIndex: -1,
    popupSelectedCircles: new Array(6).fill().map(() => new Array(3).fill(false)),
    popupSelectedCount: new Array(6).fill(0),
    bacteriaTypeList: ['SRB', 'FB', 'TGB'],
    bacteriaTypeIndex: 0,
  },
  
  onLoad() {
    // 获取当天日期
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    const todayStr = `${yyyy}-${mm}-${dd}`;
    // 初始化批量计算记录和化验时间
    this.setData({
      testStartDate: '', // 默认空
      testEndDate: todayStr // 默认当前日期
    });
    this.initBatchRecords();
  },
  
  // 切换计算器类型
  switchCalculatorType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      calculatorType: type
    });
  },
  
  // 初始化批量计算记录
  initBatchRecords() {
    // 默认三条记录，分别为海三来水、平台分水、注水泵出水
    if (this.data.batchRecords.length === 0) {
      const batchRecords = [0,1,2].map(idx => ({
        locationIndex: idx,
        locationCustomName: '',
        bottles: new Array(6).fill(0),
        bottlesDisplay: '',
        growthIndicator: '',
        magnitudeLevel: '',
        bacteriaCount: '',
        testResult: ''
      }));
      this.setData({ batchRecords });
    }
  },
  
  // 单次计算 - 选择圆点
  onCircleTap(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const circleIndex = parseInt(e.currentTarget.dataset.circle);
    
    // 前一个瓶未选择时禁止操作
    if (index > 0 && this.data.selectedCount[index-1] === 0) return;

    const newSelectedCircles = [...this.data.selectedCircles];
    newSelectedCircles[index][circleIndex] = !newSelectedCircles[index][circleIndex];
    
    // 统计选中数量
    const count = newSelectedCircles[index].filter(v => v).length;
    const newSelectedCount = [...this.data.selectedCount];
    newSelectedCount[index] = count;

    // 仅当设置为0时锁定后续瓶
    if (count === 0) {
      for (let i = index + 1; i < 6; i++) {
        newSelectedCount[i] = 0;
        newSelectedCircles[i] = new Array(3).fill(false);
      }
    }

    this.setData({
      selectedCircles: newSelectedCircles,
      selectedCount: newSelectedCount
    });
  },

  // 单次计算 - 计算结果
  calculate() {
    // 转换数据格式：将选择数量转换为原逻辑需要的数值
    const bottles = this.data.selectedCount.map(count => count > 0 ? count : null);
    const result = this.calculateBacteriaResult(bottles);
    
    this.setData({
      growthIndicator: result.growthIndicator,
      magnitudeLevel: result.magnitudeLevel,
      bacteriaCount: result.bacteriaCount,
      testResult: result.testResult,
      resultClass: result.resultClass
    });
  },
  
  // 计算细菌结果的公共方法
  calculateBacteriaResult(bottles) {
    let indicator = 0;
    const filledBottles = bottles.map(v => v !== null ? v : 0);

    // 计算生长指标（严格遵循Excel公式）
    if (filledBottles[5] > 0) {
      indicator = filledBottles[3] * 100 + filledBottles[4] * 10 + filledBottles[5];
    } else if (filledBottles[4] > 0) {
      indicator = filledBottles[2] * 100 + filledBottles[3] * 10 + filledBottles[4];
    } else if (filledBottles[3] > 0) {
      indicator = filledBottles[1] * 100 + filledBottles[2] * 10 + filledBottles[3];
    } else if (filledBottles[2] > 0) {
      indicator = filledBottles[0] * 100 + filledBottles[1] * 10 + filledBottles[2];
    } else if (filledBottles[1] > 0) {
      indicator = filledBottles[0] * 100 + filledBottles[1] * 10;
    } else {
      indicator = filledBottles[0] * 100;
    }

    // 计算级数
    const magnitudeLevel = 
      filledBottles[5] > 0 ? 3 :
      filledBottles[4] > 0 ? 2 :
      filledBottles[3] > 0 ? 1 : 0;

    // 查找菌量数据
    const bacteriaItem = bacteriaData.find(item => item.indicator === indicator);
    
    // 错误处理
    if (!bacteriaItem) {
      return {
        growthIndicator: indicator,
        magnitudeLevel: magnitudeLevel,
        bacteriaCount: '--',
        testResult: '化验结果有误，请确认输入数据',
        resultClass: 'error'
      };
    }

    // 计算结果
    const bacteriaCount = bacteriaItem.value * Math.pow(10, magnitudeLevel);
    const isQualified = bacteriaCount <= 25;

    return {
      growthIndicator: indicator,
      magnitudeLevel: magnitudeLevel,
      bacteriaCount: bacteriaCount.toFixed(1),
      testResult: isQualified ? '合格' : '不合格',
      resultClass: isQualified ? 'qualified' : 'unqualified'
    };
  },
  
  // 批量计算 - 日期选择
  changeTestStartDate(e) {
    this.setData({
      testStartDate: e.detail.value
    });
  },
  
  changeTestEndDate(e) {
    this.setData({
      testEndDate: e.detail.value
    });
  },
  
  // 批量计算 - 添加新行
  addNewRow() {
    const batchRecords = [...this.data.batchRecords];
    batchRecords.push({
      locationIndex: 3, // 默认为自定义
      locationCustomName: '',
      bottles: new Array(6).fill(0),
      bottlesDisplay: '',
      growthIndicator: '',
      magnitudeLevel: '',
      bacteriaCount: '',
      testResult: ''
    });
    
    this.setData({
      batchRecords
    });
  },
  
  // 批量计算 - 删除行
  deleteRow(e) {
    const index = e.currentTarget.dataset.index;
    const batchRecords = [...this.data.batchRecords];
    batchRecords.splice(index, 1);
    
    // 如果删除后没有记录，添加一个空行
    if (batchRecords.length === 0) {
      batchRecords.push({
        locationIndex: 0,
        locationCustomName: '',
        bottles: new Array(6).fill(0),
        bottlesDisplay: '',
        growthIndicator: '',
        magnitudeLevel: '',
        bacteriaCount: '',
        testResult: ''
      });
    }
    
    this.setData({
      batchRecords
    });
  },
  
  // 批量计算 - 取样地点选择
  changeSampleLocation(e) {
    const index = e.currentTarget.dataset.index;
    const locationIndex = parseInt(e.detail.value);
    
    const batchRecords = [...this.data.batchRecords];
    batchRecords[index].locationIndex = locationIndex;
    
    // 如果不是自定义，清空自定义名称
    if (locationIndex !== 3) {
      batchRecords[index].locationCustomName = '';
    }
    
    this.setData({
      batchRecords
    });
  },
  
  // 批量计算 - 自定义地点名称
  inputCustomLocationName(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    
    const batchRecords = [...this.data.batchRecords];
    batchRecords[index].locationCustomName = value;
    
    this.setData({
      batchRecords
    });
  },
  
  // 批量计算 - 返回地点选择器
  returnToPicker(e) {
    const index = e.currentTarget.dataset.index;
    
    const batchRecords = [...this.data.batchRecords];
    batchRecords[index].locationIndex = 0; // 重置为第一个选项
    batchRecords[index].locationCustomName = '';
    
    this.setData({
      batchRecords
    });
  },
  
  // 批量计算 - 打开瓶子选择器
  openBottleSelector(e) {
    const index = e.currentTarget.dataset.index;
    const currentRecord = this.data.batchRecords[index];
    
    // 根据当前记录初始化弹窗中的选择状态
    let popupSelectedCircles = new Array(6).fill().map(() => new Array(3).fill(false));
    let popupSelectedCount = new Array(6).fill(0);
    
    // 如果有现有的瓶子数据，转换为选择状态
    if (currentRecord.bottles && currentRecord.bottles.length === 6) {
      for (let i = 0; i < 6; i++) {
        const count = currentRecord.bottles[i];
        popupSelectedCount[i] = count;
        
        for (let j = 0; j < count; j++) {
          popupSelectedCircles[i][j] = true;
        }
      }
    }
    
    this.setData({
      showBottleSelector: true,
      currentEditingIndex: index,
      popupSelectedCircles,
      popupSelectedCount
    });
  },
  
  // 批量计算 - 瓶子选择器中的选择
  onPopupCircleTap(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const circleIndex = parseInt(e.currentTarget.dataset.circle);
    
    // 前一个瓶未选择时禁止操作
    if (index > 0 && this.data.popupSelectedCount[index-1] === 0) return;

    const newSelectedCircles = [...this.data.popupSelectedCircles];
    newSelectedCircles[index][circleIndex] = !newSelectedCircles[index][circleIndex];
    
    // 统计选中数量
    const count = newSelectedCircles[index].filter(v => v).length;
    const newSelectedCount = [...this.data.popupSelectedCount];
    newSelectedCount[index] = count;

    // 仅当设置为0时锁定后续瓶
    if (count === 0) {
      for (let i = index + 1; i < 6; i++) {
        newSelectedCount[i] = 0;
        newSelectedCircles[i] = new Array(3).fill(false);
      }
    }

    this.setData({
      popupSelectedCircles: newSelectedCircles,
      popupSelectedCount: newSelectedCount
    });
  },
  
  // 批量计算 - 关闭瓶子选择器
  closeBottleSelector() {
    this.setData({
      showBottleSelector: false,
      currentEditingIndex: -1
    });
  },
  
  // 批量计算 - 确认瓶子选择
  confirmBottleSelection() {
    const index = this.data.currentEditingIndex;
    if (index === -1) return;
    
    const batchRecords = [...this.data.batchRecords];
    const bottles = this.data.popupSelectedCount;
    
    // 生成瓶子显示文本
    let bottlesDisplay = '';
    for (let i = 0; i < 6; i++) {
      bottlesDisplay += `${i+1}号:${bottles[i]} `;
    }
    bottlesDisplay = bottlesDisplay.trim();
    
    // 计算结果
    const result = this.calculateBacteriaResult(bottles);
    
    // 更新记录
    batchRecords[index].bottles = bottles;
    batchRecords[index].bottlesDisplay = bottlesDisplay;
    batchRecords[index].growthIndicator = result.growthIndicator;
    batchRecords[index].magnitudeLevel = result.magnitudeLevel;
    batchRecords[index].bacteriaCount = result.bacteriaCount;
    batchRecords[index].testResult = result.testResult;
    
    this.setData({
      batchRecords,
      showBottleSelector: false,
      currentEditingIndex: -1
    });
  },
  
  // 批量计算 - 保存结果
  saveBatchResults() {
    // 检查是否有数据
    if (this.data.batchRecords.length === 0 || !this.data.testStartDate) {
      wx.showToast({
        title: '请完善化验信息',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否有有效记录
    const hasValidRecord = this.data.batchRecords.some(record => record.bottlesDisplay);
    if (!hasValidRecord) {
      wx.showToast({
        title: '请至少添加一条有效记录',
        icon: 'none'
      });
      return;
    }
    
    // 保存到本地存储
    try {
      const savedRecords = wx.getStorageSync('tripleBacteriaBatchRecords') || [];
      const newRecord = {
        id: Date.now(),
        testStartDate: this.data.testStartDate,
        testEndDate: this.data.testEndDate,
        records: this.data.batchRecords,
        savedTime: new Date().toLocaleString()
      };
      
      savedRecords.unshift(newRecord);
      wx.setStorageSync('tripleBacteriaBatchRecords', savedRecords);
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    } catch (e) {
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },
  
  // 批量计算 - 清除所有记录
  clearAllBatchRecords() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除当前所有记录吗？',
      success: (res) => {
        if (res.confirm) {
          const batchRecords = [0,1,2].map(idx => ({
            locationIndex: idx,
            locationCustomName: '',
            bottles: new Array(6).fill(0),
            bottlesDisplay: '',
            growthIndicator: '',
            magnitudeLevel: '',
            bacteriaCount: '',
            testResult: ''
          }));
          // 结束时间为当天，化验时间为空
          const today = new Date();
          const yyyy = today.getFullYear();
          const mm = String(today.getMonth() + 1).padStart(2, '0');
          const dd = String(today.getDate()).padStart(2, '0');
          const todayStr = `${yyyy}-${mm}-${dd}`;
          this.setData({
            batchRecords,
            testStartDate: '',
            testEndDate: todayStr
          });
        }
      }
    });
  },
  
  // 批量计算 - 生成并分享表格截图
  generateAndShareTableImage() {
    // 检查是否有数据
    if (this.data.batchRecords.length === 0 || !this.data.testStartDate) {
      wx.showToast({
        title: '请完善化验信息',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否有有效记录
    const hasValidRecord = this.data.batchRecords.some(record => record.bottlesDisplay);
    if (!hasValidRecord) {
      wx.showToast({
        title: '请至少添加一条有效记录',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '生成图片中...',
    });
    
    // 使用canvas绘制表格并生成图片
    const query = wx.createSelectorQuery();
    query.select('#shareCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        // 设置画布大小
        const width = 950;
        const rowHeight = 40;
        const headerHeight = 60; // 主表头+二级表头
        const baseY = 120;
        const tableRows = this.data.batchRecords.length;
        const height = baseY + headerHeight + tableRows * rowHeight + 100;
        canvas.width = width;
        canvas.height = height;
        
        // 绘制背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, width, height);
        
        // 优化标题样式
        ctx.fillStyle = '#222';
        ctx.font = 'bold 32px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const bacteriaType = this.data.bacteriaTypeList[this.data.bacteriaTypeIndex] || '三菌';
        ctx.fillText(bacteriaType + '化验结果', width / 2, 55);
        // 标题下横线
        ctx.strokeStyle = '#e0e0e0';
        ctx.beginPath();
        ctx.moveTo(width/2 - 160, 75);
        ctx.lineTo(width/2 + 160, 75);
        ctx.stroke();
        
        // 化验信息行（居中）
        ctx.font = '16px sans-serif';
        ctx.textAlign = 'left';
        ctx.fillStyle = '#444';
        ctx.fillText(`化验时间: ${this.data.testStartDate}`, 60, 100);
        ctx.textAlign = 'right';
        ctx.fillText(`结束时间: ${this.data.testEndDate || '无'}`, width - 60, 100);
        
        // 列宽设置
        const colWidths = [60, 120, 280, 90, 80, 120, 90];
        const phenomenonSubColWidth = colWidths[2] / 6;
        let xPos = (width - colWidths.reduce((a, b) => a + b, 0)) / 2;
        // 保证表格整体居中
        const tableTop = baseY;
        // 绘制主表头
        ctx.fillStyle = '#f2f2f2';
        ctx.fillRect(xPos, tableTop, width - 2 * xPos, 30);
        ctx.fillStyle = '#222';
        ctx.font = 'bold 18px sans-serif';
        ctx.textAlign = 'center';
        let x = xPos;
        const headers = ['序号', '取样地点', '化验现象', '生长指标', '级数', '细菌个数', '化验结果'];
        headers.forEach((header, i) => {
          ctx.fillText(header, x + colWidths[i] / 2, tableTop + 18);
          x += colWidths[i];
        });
        // 二级表头
        x = xPos + colWidths[0] + colWidths[1];
        ctx.font = '14px sans-serif';
        ctx.fillStyle = '#666';
        const subHeaders = ['1#瓶','2#瓶','3#瓶','4#瓶','5#瓶','6#瓶'];
        for (let i = 0; i < 6; i++) {
          ctx.fillText(subHeaders[i], x + phenomenonSubColWidth * i + phenomenonSubColWidth/2, tableTop + 48);
        }
        // 分割线
        ctx.strokeStyle = '#eaeaea';
        ctx.beginPath();
        ctx.moveTo(xPos, tableTop + 30);
        ctx.lineTo(xPos + colWidths.reduce((a, b) => a + b, 0), tableTop + 30);
        ctx.stroke();
        // 竖线
        let colX = xPos;
        for (let i = 0; i < colWidths.length; i++) {
          ctx.beginPath();
          ctx.moveTo(colX, tableTop);
          ctx.lineTo(colX, tableTop + headerHeight + tableRows * rowHeight);
          ctx.stroke();
          colX += colWidths[i];
        }
        // 右边框
        ctx.beginPath();
        ctx.moveTo(colX, tableTop);
        ctx.lineTo(colX, tableTop + headerHeight + tableRows * rowHeight);
        ctx.stroke();
        // 绘制表格数据
        ctx.font = '16px sans-serif';
        ctx.textAlign = 'center';
        for (let rowIdx = 0; rowIdx < tableRows; rowIdx++) {
          const record = this.data.batchRecords[rowIdx];
          const y = tableTop + headerHeight + rowIdx * rowHeight;
          // 行背景
          ctx.fillStyle = rowIdx % 2 === 0 ? '#fff' : '#f9f9f9';
          ctx.fillRect(xPos, y, colWidths.reduce((a, b) => a + b, 0), rowHeight);
          ctx.fillStyle = '#222';
          let x = xPos;
          // 序号
          ctx.fillText(String(rowIdx + 1), x + colWidths[0]/2, y + rowHeight/2 + 2);
          x += colWidths[0];
          // 取样地点
          const locationText = record.locationIndex === 3 ? record.locationCustomName : this.data.sampleLocations[record.locationIndex];
          ctx.fillText(locationText || '-', x + colWidths[1]/2, y + rowHeight/2 + 2);
          x += colWidths[1];
          // 化验现象（6个瓶子数量）
          for (let i = 0; i < 6; i++) {
            ctx.fillText((record.bottles && typeof record.bottles[i] === 'number') ? record.bottles[i] : '-', x + phenomenonSubColWidth * i + phenomenonSubColWidth/2, y + rowHeight/2 + 2);
          }
          x += colWidths[2];
          // 生长指标
          ctx.fillText(record.growthIndicator || '-', x + colWidths[3]/2, y + rowHeight/2 + 2);
          x += colWidths[3];
          // 级数（始终显示10^数字）
          ctx.fillText((record.magnitudeLevel !== '' && record.magnitudeLevel !== null && record.magnitudeLevel !== undefined) ? `10^${record.magnitudeLevel}` : '-', x + colWidths[4]/2, y + rowHeight/2 + 2);
          x += colWidths[4];
          // 细菌个数
          ctx.fillText(record.bacteriaCount ? `${record.bacteriaCount} 个/mL` : '-', x + colWidths[5]/2, y + rowHeight/2 + 2);
          x += colWidths[5];
          // 化验结果
          if (record.testResult) {
            ctx.fillStyle = record.testResult === '合格' ? '#4caf50' : '#f44336';
            ctx.fillText(record.testResult, x + colWidths[6]/2, y + rowHeight/2 + 2);
          } else {
            ctx.fillStyle = '#222';
            ctx.fillText('-', x + colWidths[6]/2, y + rowHeight/2 + 2);
          }
        }
        // 外边框
        ctx.strokeStyle = '#bbb';
        ctx.lineWidth = 1.2;
        ctx.strokeRect(xPos, tableTop, colWidths.reduce((a, b) => a + b, 0), headerHeight + tableRows * rowHeight);
        // 底部说明
        ctx.fillStyle = '#666666';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('合格要求：细菌数 ≤25 个/mL', width / 2, tableTop + headerHeight + tableRows * rowHeight + 32);
        // 生成图片
        wx.canvasToTempFilePath({
          canvas,
          success: (res) => {
            wx.hideLoading();
            // 调用微信原生分享面板，用户可选择转发、收藏和保存
            wx.showShareImageMenu({
              path: res.tempFilePath
            });
          },
          fail: () => {
            wx.hideLoading();
            wx.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        });
      });
  },
  
    // 转发给朋友
  onShareAppMessage() {
      return {
        title: '平台常用计算工具-三菌计算器',
        path: '/pages/triple-bacteria/index'
      };
    },
  
    // 分享到朋友圈（需基础库 2.11.3+）
    onShareTimeline() {
      return {
        title: '平台常用计算工具-三菌计算器',
        query: 'from=timeline'
      };
    },
  
  // 三菌类型选择
  changeBacteriaType(e) {
    this.setData({
      bacteriaTypeIndex: parseInt(e.detail.value)
    });
  },
});